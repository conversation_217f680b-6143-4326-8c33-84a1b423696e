'''
Used to get all customer for reactivation, different conditionals each time. For Dominika / CRM
'''
from __future__ import print_function
from custom.utils.exports import dump_list_as_csv
from tqdm import tqdm


# lets get all carts, from france, with carts from 15.04.2019 - 15.06.2019


carts = Order.objects.filter(country='france', status=9, created_at__range=['2019-04-15', '2019-06-15']).exclude(email__isnull=True).exclude(email='')
carts.count()

not_customers = []

for cart in carts:
    orders_with_this_user = Order.objects.filter(owner=cart.owner, status_paid=True).count()
    orders_with_this_email = Order.objects.filter(email=cart.email, status_paid=True).count()
    if orders_with_this_email + orders_with_this_email == 0:
        not_customers.append([cart.email,cart.owner.profile.language])


print(len(not_customers))

prospects = UserProspect.objects.filter(created_at__range=['2019-04-15', '2019-06-15'])

print(len(prospects))
users = User.objects.filter(email__in=[x.email for x in prospects], profile__region_id=10)
for user in users:
    if Order.objects.filter(owner=user, status_paid=True).count() == 0 and \
            Order.objects.filter(email=user.email, status_paid=True).count() == 0:
        not_customers.append([user.email, user.profile.language])

print(len(not_customers))


dump_list_as_csv(not_customers, open('/tmp/reaktywacja_francja.csv', 'w'))

