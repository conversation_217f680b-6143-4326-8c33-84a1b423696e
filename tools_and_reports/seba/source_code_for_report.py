from datetime import datetime, date

from custom.utils.exports import dump_list_as_csv
from logistic.models import LogisticOrder
from orders.enums import OrderType
from orders.models import Order
from producers.models import Product

filters = {
    "order__logistic_info__delivered_date__gte": "2023-01-01",
    "order__logistic_info__delivered_date__lt": "2023-04-01",
    "order__country": "france",
}
prods = (
    Product.objects.filter(**filters)
    .exclude(order__order_type=OrderType.COMPLAINT)
    .select_related(
        "order",
    )
    .prefetch_related("order__logistic_info")
    .order_by("order__logistic_info__delivered_date")
)

result = []
los_filter = {k.replace("order__logistic_info__", ""): v for k, v in filters.items()}
for i, product in enumerate(prods, start=1):
    los = product.order.logistic_info.all()
    los = list(
        filter(
            (
                lambda x: (
                    x.delivered_date
                    and x.delivered_date >= date(2023, 1, 1)
                    and x.delivered_date < date(2023, 4, 1)
                )
            ),
            product.order.logistic_info.all(),
        )
    )
    if len(los) != 1:
        los = list(
            filter((lambda x: (x.order_type == product.cached_product_type)), los)
        )
    if len(los) == 0:
        print(los)
        print(product)
        print(product.order)
        break
        # continue
    result.append(
        [
            los[0].delivered_date,
            product.id,
            product.order.id,
            los[0].id,
            product.get_weight_netto(),
            product.get_weight_brutto(),
    ]
    )
dump_list_as_csv(result, open('/tmp/ZbyszekQ12023_v2.csv','w+'))