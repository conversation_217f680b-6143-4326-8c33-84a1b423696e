from accounting.models import MoneyCashBack
from customer_service.models import CSCorrectionRequest
from invoice.models import Invoice
from orders.models import Order
from orders.switch_status import SwitchStatus

switched_orders = Order.objects.exclude(switch_status=SwitchStatus.BLANK.value)

for order in switched_orders:
    print(
        'Order ID;Source Region Total Price;Region Total Price;Region Promo Amount;'
        'Source Region - Region'
    )
    if order.source_region_total_price:
        print(
            f'{order.id};'
            f'{order.get_switch_status_display()};'
            f'{order.main_voucher};{order.source_region_total_price};'
            f'{order.region_total_price};'
            f'{order.region_promo_amount};'
            f'{order.source_region_total_price - order.region_total_price}'
        )
    else:
        print(
            f'{order.id};'
            f'{order.get_switch_status_display()};'
            f'{order.main_voucher};'
            f'{order.source_region_total_price};'
            f'{order.region_total_price};'
            f'{order.region_promo_amount}'
        )

    correction_requests = CSCorrectionRequest.objects.filter(invoice__order=order)
    print('Correction Requests ID;Correction Amount Gross')
    for correction_request in correction_requests:
        print(f'{correction_request.pk};{correction_request.correction_amount_gross}')

    invoices = Invoice.objects.filter(order=order)
    for invoice in invoices:
        print('Invoices ID; Diff')
        diff = invoice.to_diff_dict()
        print(f'{invoice.pk};{diff}')
        money_cashbacks = MoneyCashBack.objects.filter(invoice=invoice)
        print('Money Cashbacks ID;Amount')
        for cashback in money_cashbacks:
            print(f'{cashback.pk};{cashback.amount}')
    print('')
