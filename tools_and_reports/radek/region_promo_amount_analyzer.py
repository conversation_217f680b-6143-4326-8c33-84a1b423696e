from datetime import datetime
from decimal import Decimal

from invoice.choices import InvoiceStatus, InvoiceItemType
from invoice.models import Invoice

ISSUED_AT_NEW = datetime(2024, 4, 1, 0, 1)


def netto_to_brutto(netto, vat_rate):
    return netto * (1 + vat_rate)


def compare_invoices_and_orders():
    invoices = Invoice.objects.filter(
        order__paid_at__year=ISSUED_AT_NEW.year,
        order__paid_at__month=ISSUED_AT_NEW.month,
        status=InvoiceStatus.ENABLED,
    )

    for invoice in invoices:
        order = invoice.order
        invoice_items = invoice.invoice_items.filter(item_type=InvoiceItemType.ITEM)
        for invoice_item in invoice_items:
            invoice_item_netto = invoice_item.discount_value
            invoice_item_brutto = netto_to_brutto(
                invoice_item_netto, invoice_item.vat_rate
            )
            order_item_brutto = invoice_item.order_item.region_promo_value
            diff = invoice_item_brutto - order_item_brutto
            if diff > Decimal('0.01'):
                print(order.id, diff)
