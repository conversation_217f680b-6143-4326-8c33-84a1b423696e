import csv

from custom.utils.exports import dump_list_as_csv
from orders.models import Order

result = []

ii = 10,11

for i in ii:
    with open('/home/<USER>/Dokumenty/adyen/payments_accounting_report_filtered (%d).csv' % i) as csvfile:
         spamreader = csv.reader(csvfile, delimiter=',', quotechar='"')
         for line in spamreader:
             # print line
             if 'order' in line[3]:
                order = Order.objects.get(pk=line[3].split('order-')[1])
                inv = order.invoice_set.filter(status__in=[0,3]).last()
                if not inv:
                    print(order.pk, 'no inv ?')
                    continue
                d = inv.to_dict()
                total = d['total_value']
                income = float(line[8])
                if float(total) - income != 0:
                    print(order.id, income - float(total), income, float(total))
                    result.append((order.id,inv.pretty_id, inv.sell_at, income, total, income - float(total)))

headers = ['Order id', 'Invoice', 'Sell at', 'Adyen income', 'Invoice total', 'diff']

dump_list_as_csv(result, output=open('/home/<USER>/Dokumenty/adyen/diff.csv', 'w'),mail=None, headers=headers)