


1)PROD cstm@tylko:~$
time scp backup/db_cstm-tylko.com-2018_08_21_03_00_01.sql.gz <EMAIL>:/home/<USER>/

real 0m16.372s
user 0m6.580s
sys 0m2.604s

2)CSTM@DEV
gunzip db_cstm-tylko.com-2018_08_21_03_00_01.sql.gz
sudo supervisorctl stop all
sudo su postgres
psql
alter database db_cstm rename to db_cstm_old;
create database db_cstm;
psql db_cstm < db_cstm-tylko.com.sql
real 20m51.977s
user 0m9.476s
sys 0m5.024s
exit
sudo supervisorctl start all


3) interface
- trzeba zmienić live payment na false w https://dev.tylko.com/admin/custom/priceconfiguration/ , na tym priceConfiguration ktory jest aktywny
- i trzeba wrzuc<PERSON> i przegenerować dna z produkcji