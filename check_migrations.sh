#!/bin/bash

APPS=$(echo "$@" | sed -e 's|src/\([_[:alnum:]]*\)/[_[:alnum:]/]*.py|\1|g')
if [[ "$APPS" ]]; then
    echo "Included apps: $APPS"
fi

REPO_ROOT=$(git rev-parse --show-toplevel)
USING_DOCKER=$(grep 'USING_DOCKER=1' "$REPO_ROOT/.env")
LOCAL_POETRY=$(command -v poetry)

function check_migrations_docker {
    docker compose run --rm app python manage.py \
        makemigrations --check --dry-run "$@" 2>&1
}

function check_migrations_poetry {
    cd "$REPO_ROOT/src" || exit 1
    poetry run python manage.py \
        makemigrations --check --dry-run "$@" 2>&1
}


if [[ -n "$USING_DOCKER" || -z "$LOCAL_POETRY" ]]; then
    echo "Checking migrations using docker..."
    output=$(check_migrations_docker $APPS)
else
    echo "Checking migrations using local poetry venv..."
    output=$(check_migrations_poetry $APPS)
fi
status=$?

if [ $status -ne 0 ]; then
    echo "❌ Error: There are model changes that are not reflected in migrations!"
    echo "$output"
    exit 1
else
    echo "✅ Migrations are up to date."
    exit 0
fi
