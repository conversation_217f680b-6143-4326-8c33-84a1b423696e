from django import forms
from django.core.exceptions import ValidationError

from custom.enums import ShelfType
from vouchers.enums import VoucherType
from vouchers.models import (
    Voucher,
    VoucherGroup,
    VoucherRegionEntry,
    VoucherSettings,
)


class VoucherSettingsForm(forms.ModelForm):
    _b2b_voucher_percentage_value = forms.DecimalField(
        label='Percentage value for B2B vouchers',
        help_text='XX %',
        max_digits=2,
        decimal_places=0,
    )
    _b2b_sotty_discount_value = forms.DecimalField(
        label='Sotty discount value for B2B vouchers',
        help_text='XX %',
        max_digits=2,
        decimal_places=0,
        required=False,
    )
    _b2b_delivery_discount_value = forms.DecimalField(
        label='Delivery discount value for B2B vouchers',
        help_text='XX %',
        max_digits=2,
        decimal_places=0,
        required=False,
    )

    class Meta:
        model = VoucherSettings
        fields = [
            '_b2b_voucher_percentage_value',
            '_b2b_sotty_discount_value',
            '_b2b_delivery_discount_value',
        ]


class VoucherRegionsMassChange(forms.Form):
    currency = forms.CharField(widget=forms.TextInput(attrs={'readonly': 'readonly'}))
    in_currency = forms.FloatField()
    amount_starts = forms.FloatField()
    amount_limit = forms.FloatField()


class VoucherValuesMassChange(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    currency = forms.CharField(widget=forms.TextInput(attrs={'readonly': 'readonly'}))
    in_currency = forms.FloatField()


class VoucherEmailForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    email = forms.CharField(widget=forms.TextInput())


class VoucherEmailAndQuantityForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    email = forms.CharField(widget=forms.TextInput())
    quantity = forms.IntegerField()

    def clean_quantity(self):
        data = self.cleaned_data['quantity']
        if data > 500:
            raise ValidationError('The maximum quantity can be 500!')
        return data


class VoucherModelForm(forms.ModelForm):
    group = forms.ModelChoiceField(
        queryset=VoucherGroup.objects.order_by('-id'),
        required=False,
    )
    item_conditionals = forms.JSONField(
        required=False,
        widget=forms.Textarea(attrs={'style': 'width: 400px; height: 50px;'}),
        initial={'exclude': [{'shelf_types': [ShelfType.SOFA_TYPE01]}]},
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'style': 'width: 400px; height: 50px;'}),
    )
    active = forms.TypedChoiceField(
        required=True,
        initial=True,
        choices=(('False', 'No'), ('True', 'Yes')),
        coerce=lambda x: x == 'True',
        widget=forms.Select(),
    )
    ignore_on_invoice = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.Select(choices=((False, 'No'), (True, 'Yes'))),
    )
    kind_of = forms.ChoiceField(
        required=True, initial=VoucherType.PERCENTAGE, choices=VoucherType.choices
    )

    class Meta:
        model = Voucher
        fields = (
            'active',
            'is_stackable',
            'code',
            'kind_of',
            'value',
            'origin',
            'group',
            'start_date',
            'end_date',
            'quantity',
            'quantity_left',
            'amount_starts',
            'amount_limit',
            'for_email',
            'item_conditionals',
            'notes',
            'ignore_on_invoice',
        )


class VoucherRegionEntryModelForm(forms.ModelForm):
    value = forms.DecimalField(label='Value in currency')

    class Meta:
        model = VoucherRegionEntry
        fields = (
            'voucher',
            'region',
            'value',
            'amount_starts',
            'amount_limit',
        )
