from django.urls import (
    include,
    path,
)
from rest_framework import routers

from vouchers.views import (
    CheckVoucherAPIView,
    GlobalPromoViewSet,
    MailingAbsoluteVoucherCreateView,
    MailingPercentageVoucherCreateView,
    MailingSampleVoucherCreateView,
    RemoveVoucherAPIView,
)

router = routers.SimpleRouter()
router.register('global_promo', GlobalPromoViewSet, basename='global_promo')

urlpatterns = [
    path(
        'check_promo/',
        CheckVoucherAPIView.as_view(),
        name='rest_check_voucher',
    ),
    path(
        'check_promo/<str:code>/',
        CheckVoucherAPIView.as_view(),
        name='rest_check_voucher',
    ),
    path(
        'remove_promo/<str:code>/',
        RemoveVoucherAPIView.as_view(),
        name='remove-voucher',
    ),
    path(
        'mailing-absolute-voucher/',
        MailingAbsoluteVoucherCreateView.as_view(),
        name='mailing-absolute-voucher',
    ),
    path(
        'mailing-percentage-voucher/',
        MailingPercentageVoucherCreateView.as_view(),
        name='mailing-percentage-voucher',
    ),
    path(
        'mailing-sample-voucher/',
        MailingSampleVoucherCreateView.as_view(),
        name='mailing-sample-voucher',
    ),
    path('', include(router.urls)),
]
