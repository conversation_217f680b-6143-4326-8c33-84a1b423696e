{% extends "admin/base_site.html" %}
{% load i18n l10n admin_urls static admin_modify admin_tags %}

{% block extrahead %}
    {{ block.super }}
    {{ media.js }}
    <link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}" />
    <script type="text/javascript" src="{% url 'admin:jsi18n' %}"></script>
    {{ form.media }}
{% endblock %}

{% block breadcrumbs %}
    <div class="breadcrumbs">
        <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
        &rsaquo; <a href="{% url 'admin:app_list' app_label=app_label %}">{{ app_label|capfirst|escape }}</a>
        &rsaquo; <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
        &rsaquo; Voucher value change tool
    </div>
{% endblock %}

{% block content %}
    <p>Editing multiple vouchers</p>
    <p>*Data below is taken from first Voucher Region entry</p>
    <form action="" method="post" enctype="multipart/form-data">
        {% csrf_token %}
        {% for f in form %}
            {{f}} <br>
        {% endfor %}
        {% for obj in queryset %}
        <input type="hidden" name="_selected_action" value="{{ obj.pk|unlocalize }}" />
        {% endfor %}
        <input type="hidden" name="action" value="change_voucher_values" />
        <input type="submit" name="apply" value="Save" />
        <input type="hidden" name="select_across" value="{{ select_across }}" />

    </form>

{% endblock %}
