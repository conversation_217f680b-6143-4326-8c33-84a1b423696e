from django.urls import (
    include,
    path,
)
from rest_framework.routers import Default<PERSON>out<PERSON>

from producers.api.views import (
    CncConnectionsForBatchView,
    CustomPricingFactorChangeRequestCreateView,
    CustomPricingFactorChangeRequestUpdateView,
    CustomPricingFactorCSVExportView,
    CustomPricingFactorManagementView,
    ElementsOrderProducerApiView,
    PackagingFilesForBatchView,
    ProducerActionView,
    ProductionFilesForBatchView,
    QualityHoldReleaseRequestView,
    ServeCustomPricingFactorChangeRequestAttachmentView,
    StatusBatchPageApiView,
    StatusManufacturerView,
)

router = DefaultRouter()
router.register(
    'elements_order_rest',
    ElementsOrderProducerApiView,
    basename='elements_order_rest',
)


urlpatterns = [
    path(
        'producer_batches_rest/',
        StatusBatchPageApiView.as_view(),
        name='producer_batches_rest',
    ),
    path('', include(router.urls)),
    path(
        'producer_actions_rest/',
        ProducerActionView.as_view(),
        name='producer_actions_rest',
    ),
    path('producer_info/', StatusManufacturerView.as_view(), name='producer_info'),
    path(
        'cnc_connections/', CncConnectionsForBatchView.as_view(), name='cnc_connections'
    ),
    path(
        'production_files/',
        ProductionFilesForBatchView.as_view(),
        name='production_files',
    ),
    path(
        'packaging_files/', PackagingFilesForBatchView.as_view(), name='packaging_files'
    ),
    path(
        'custom_pricing_factors/',
        CustomPricingFactorManagementView.as_view(),
        name='custom_pricing_factors',
    ),
    path(
        'custom_pricing_factors/export_csv/',
        CustomPricingFactorCSVExportView.as_view(),
        name='custom_pricing_factors_export_csv',
    ),
    path(
        'create_custom_pricing_factor_change_request/',
        CustomPricingFactorChangeRequestCreateView.as_view(),
        name='create_custom_pricing_factor_change_request',
    ),
    path(
        'update_custom_pricing_factor_change_request/<int:pk>/',
        CustomPricingFactorChangeRequestUpdateView.as_view(),
        name='update_custom_pricing_factor_change_request',
    ),
    path(
        'get_request_attachment/<int:pk>/',
        ServeCustomPricingFactorChangeRequestAttachmentView.as_view(),
        name='custom_pricing_factors_request_attachment',
    ),
    path(
        'quality_hold_release/<int:batch_id>/',
        QualityHoldReleaseRequestView.as_view(),
        name='quality_hold_release',
    ),
]
