# Generated by Django 4.1.9 on 2024-03-28 15:16

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('producers', '0099_alter_product_priority_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='product',
            name='priority',
            field=models.IntegerField(
                choices=[
                    (1, 'On Hold'),
                    (2, 'Postponed'),
                    (5, ''),
                    (10, 'Important'),
                    (15, 'B2B'),
                    (17, 'INFLU'),
                    (20, 'VIP'),
                    (60, 'R&D'),
                    (70, 'Big Orders'),
                    (80, 'Assembly'),
                    (90, 'Fast Track'),
                    (100, 'Sku'),
                    (110, 'EDD'),
                    (120, 'On hold DES'),
                ],
                default=5,
            ),
        ),
        migrations.AlterField(
            model_name='productpriorityhistory',
            name='previous_priority',
            field=models.IntegerField(
                choices=[
                    (1, 'On Hold'),
                    (2, 'Postponed'),
                    (5, ''),
                    (10, 'Important'),
                    (15, 'B2B'),
                    (17, 'INFLU'),
                    (20, 'VIP'),
                    (60, 'R&D'),
                    (70, 'Big Orders'),
                    (80, 'Assembly'),
                    (90, 'Fast Track'),
                    (100, 'Sku'),
                    (110, 'EDD'),
                    (120, 'On hold DES'),
                ]
            ),
        ),
        migrations.AlterField(
            model_name='productpriorityhistory',
            name='priority',
            field=models.IntegerField(
                choices=[
                    (1, 'On Hold'),
                    (2, 'Postponed'),
                    (5, ''),
                    (10, 'Important'),
                    (15, 'B2B'),
                    (17, 'INFLU'),
                    (20, 'VIP'),
                    (60, 'R&D'),
                    (70, 'Big Orders'),
                    (80, 'Assembly'),
                    (90, 'Fast Track'),
                    (100, 'Sku'),
                    (110, 'EDD'),
                    (120, 'On hold DES'),
                ]
            ),
        ),
    ]
