from producers.choices import (
    BatchNeededActions,
    ProductionFileStatus,
)


class BatchFilesStatusUpdater:
    def __init__(self, batch):
        self.file_status_fields = {
            'cnc_files_status',
            'production_files_status',
            'packaging_files_status',
        }
        self.batch = batch

    def set_status_for_downloaded_files_after_recalculations(self):
        for field in self.file_status_fields:
            old_status = getattr(self.batch, field)
            if old_status == ProductionFileStatus.DOWNLOADED:
                setattr(
                    self.batch, field, ProductionFileStatus.RECALCULATED_AFTER_DOWNLOAD
                )
        self.batch.save(update_fields=self.file_status_fields)

    def set_status_after_download(self, file_status_field_name):
        if self.batch.actions_needed == BatchNeededActions.NO_ACTION_NEEDED:
            setattr(self.batch, file_status_field_name, ProductionFileStatus.DOWNLOADED)
            self.batch.save()
