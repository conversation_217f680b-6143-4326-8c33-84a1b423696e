import itertools
import logging

from collections import defaultdict
from datetime import datetime
from io import Bytes<PERSON>
from zipfile import ZipFile

from django.conf import settings
from django.core.files.base import ContentFile
from django.db.models import F
from django.utils import timezone
from django.utils.module_loading import import_string

import openpyxl

from custom import openpyxl_styles
from custom.enums import (
    Furniture,
    ShelfType,
)
from custom.utils.report_file import ReportFile
from custom.utils.xlsx import apply_styles_to_cell
from kpi.tasks import export_cash_flow_to_big_query
from producers.choices import BatchType
from producers.models import (
    Product,
    ProductBatch,
)
from producers.production_files.summary_usage import (
    get_usage_for_batches_raw,
    get_usage_for_products,
    get_usage_for_products_grouped_by_batches,
)
from producers.queries import get_guilty_manufactures_from_batches
from production_margins.enums import ElementsOrderType
from production_margins.models import ElementsOrder

logger = logging.getLogger('producers')

DOOR_VARIANT_LIST = ['door', 'door-B', 'door-C']

FURNITURE_TYPES = [
    ('MEBLE STANDARDOWE I Z DRZWIAMI', BatchType.STANDARD),
    ('MEBLE Z POŁĄCZENIAMI NA DŁUGOŚĆ', BatchType.EXTENDED),
    ('MEBLE Z SZUFLADAMI I Z DRZWIAMI', BatchType.DRAWERS),
    (
        'MEBLE Z SZUFLADAMI I Z DRZWIAMI I POŁĄCZENIAMI NA DŁUGOŚĆ',
        BatchType.DRAWERS_EXTENDED,
    ),
    ('MEBLE NIESTANDARDOWE', BatchType.CUSTOM),
    ('MEBLE INNE', BatchType.NOT_SET),
    ('REKLAMACJE', BatchType.COMPLAINTS),
]


def generate_and_send_order_summary_xls(batches_ids, email, old_report_id=None):
    elements_order, report_file = generate_report_order_summary_xls(
        batches_ids, old_report_id
    )
    report_file.send_as_email_attachment(
        emails=(email,) if email else tuple(),  # noqa: C408
        body='Batch results.',
        subject=f'Zamowienie {elements_order.id}',
    )


def generate_report_order_summary_xls(batches_ids, old_report_id=None):
    elements_order = ElementsOrder.objects.create()
    batches_queryset = ProductBatch.objects.filter(pk__in=batches_ids).order_by('id')
    file_name = get_order_summary_file_name(batches_queryset, elements_order)
    report_file = generate_report_and_save_element_order(
        batches_queryset, elements_order, old_report_id, file_name
    )
    return elements_order, report_file


def get_order_summary_file_name(batches_queryset, elements_order):
    now_time = timezone.now()

    complaint_suffix = (
        'reklamacyjne_'
        if all(batch.has_complaints for batch in batches_queryset)
        else ''
    )
    return 'Zamowienie {} dla batchy B{}_B{}_{}CW{}_{}'.format(
        elements_order.id,
        batches_queryset.first().id,
        batches_queryset.last().id,
        complaint_suffix,
        now_time.isocalendar()[1],
        now_time.strftime('%Y%m%d'),
    )


def generate_and_send_order_summary_xls_manufactor_fault(
    batches_ids, email, old_report_id=None
):
    date = datetime.now().date()
    subject = f'Batch elements orders manufactor fault from {date}'
    report_file = generate_report_and_save_element_order_man_fault(
        batches_ids, old_report_id, file_name=subject
    )
    report_file.send_as_email_attachment(
        emails=[
            email,
        ]
        if email
        else list(),  # noqa: C408
        body='Batch results.',
        subject=subject,
    )


def generate_report_and_save_element_order(
    batches_queryset, elements_order, old_report_id, file_name
):
    total_dict, workbook = generate_report(batches_queryset, file_name)
    total_cost = get_cash_flow_total_cost(total_dict)
    report_file = ReportFile.load_workbook(workbook, file_name)
    is_complaint = batches_queryset.filter(
        batch_type__exact=BatchType.COMPLAINTS
    ).exists()
    save_elements_order(
        file=ContentFile(report_file.content),
        elements_order=elements_order,
        batch_queryset=batches_queryset,
        manufactor=batches_queryset.first().manufactor,
        total_cost=total_cost,
        service_cost=total_dict.get('cost_service', 0),
        old_report_id=old_report_id,
        order_type=ElementsOrderType.COMPLAINT
        if is_complaint
        else ElementsOrderType.NEW_ORDER,
    )
    return report_file


class OrderXlsxForSelectedProductsByBatch:
    def __init__(self, products_queryset):
        self.manufactors = products_queryset.values_list(
            'manufactor_id', 'manufactor__name'
        ).distinct()
        self.products_queryset = products_queryset
        self.date = datetime.now().date()
        self.file_name = f'Order xlsx report for products {self.date}'

    def generate(self):
        file = BytesIO()
        with ZipFile(file, 'w') as zipfile:
            for manufactor_id, manufactor_name in self.manufactors:
                manufactor_products_queryset = self.products_queryset.filter(
                    manufactor=manufactor_id
                )
                worksheet_file_name = manufactor_name
                usage = get_usage_for_products_grouped_by_batches(
                    manufactor_products_queryset
                )
                workbook = self.save_usage_to_xlsx(usage, worksheet_file_name)
                report_file = BytesIO()
                workbook.save(report_file)
                f = report_file.getvalue()
                zipfile.writestr(f'{manufactor_name}/{worksheet_file_name}.xlsx', f)
        return ReportFile(name=f'{self.file_name}.zip', content=file.getvalue())

    def save_usage_to_xlsx(self, usage, file_name):
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = file_name
        batches_queryset = ProductBatch.objects.filter(
            id__in=usage['batches_to_products_map'].keys()
        )
        sheet_to_order = SheetToOrderSummaryByBatch()
        workbook, total_dict = sheet_to_order.add_sheet_to_order_summary(
            workbook,
            queryset=batches_queryset,
            usage=usage,
        )
        furniture_type = batches_queryset.first().product_type
        report_writer = WriteOrderBatchReport(
            batches_queryset,
            worksheet,
            total_dict,
            furniture_type,
            usage=usage,
        )
        report_writer.write_order_summary_xls()
        return workbook


class OrderXlsxForSelectedProducts(OrderXlsxForSelectedProductsByBatch):
    def generate(self):
        file = BytesIO()
        with ZipFile(file, 'w') as zipfile:
            for manufactor_id, manufactor_name in self.manufactors:
                manufactor_products_queryset = self.products_queryset.filter(
                    manufactor=manufactor_id
                )
                for product in manufactor_products_queryset:
                    file_name = (
                        f'{product.id}_{product.cached_shelf_type}_{manufactor_name}'
                    )
                    usage = get_usage_for_products([product])
                    workbook = self.save_usage_to_xlsx(usage, file_name)
                    report_file = BytesIO()
                    workbook.save(report_file)
                    f = report_file.getvalue()
                    zipfile.writestr(f'{manufactor_name}/{file_name}.xlsx', f)
        return ReportFile(name=f'{self.file_name}.zip', content=file.getvalue())

    def save_usage_to_xlsx(self, usage, file_name):
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = file_name
        products_queryset = Product.objects.filter(id__in=usage['product_ids'])
        sheet_to_order = SheetToOrderSummaryByProduct()
        workbook, total_dict = sheet_to_order.add_sheet_to_order_summary(
            workbook,
            queryset=products_queryset,
            usage=usage,
        )
        furniture_type = products_queryset.first().batch.product_type
        report_writer = WriteOrderProductReport(
            products_queryset,
            worksheet,
            total_dict,
            furniture_type,
            usage=usage,
        )
        report_writer.write_order_summary_xls()
        return workbook


def generate_report_and_save_element_order_man_fault(
    batches_ids, old_report_id=None, file_name=None
):
    batches_queryset = ProductBatch.objects.filter(pk__in=batches_ids)
    if old_report_id:
        old_report = ElementsOrder.objects.all_orders().get(id=old_report_id)
        guilty_manufactors = [old_report.guilty_manufactor]
    else:
        guilty_manufactors = get_guilty_manufactures_from_batches(batches_queryset)

    stream = BytesIO()
    with ZipFile(stream, 'w') as zipfile:
        for manufactor in guilty_manufactors:
            batches_guilty_manufactor = batches_queryset.filter(
                batch_items__copy_of__manufactor=manufactor,
                batch_items__reproduction_complaints__manufactor_fault=True,
            ).distinct()
            if not batches_guilty_manufactor.first():
                continue
            elements_order = ElementsOrder.objects.create(guilty_manufactor=manufactor)
            manufactor_file_name = (
                f'B{batches_guilty_manufactor.first().id} - '
                f'B{batches_guilty_manufactor.last().id}_zamowienie'
            )
            total_dict, workbook = generate_report(
                batches_guilty_manufactor, file_name, guilty_manufactor_id=manufactor.id
            )
            report_file = BytesIO()
            workbook.save(report_file)
            f = report_file.getvalue()
            zipfile.writestr(f'{manufactor.name}/{manufactor_file_name}.xlsx', f)

            save_elements_order(
                file=ContentFile(f),
                elements_order=elements_order,
                batch_queryset=batches_guilty_manufactor,
                manufactor=manufactor,
                total_cost=total_dict.get('man_fault_total_cost'),
                service_cost=0,
                old_report_id=old_report_id,
                order_type=ElementsOrderType.COMPLAINT_MANUFACTOR,
            )
    file_name = f'{file_name}.zip' if file_name else 'man_fault.zip'
    report_file = ReportFile(name=file_name, content=stream.getvalue())
    return report_file


def generate_report(batches_queryset, file_name, guilty_manufactor_id=None):
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = file_name

    sheet_to_order = SheetToOrderSummaryByBatch()
    workbook, total_dict = sheet_to_order.add_sheet_to_order_summary(
        workbook,
        batches_queryset,
        guilty_manufactor_id,
    )
    furniture_type = batches_queryset.first().product_type
    report_writer = WriteOrderBatchReport(
        batches_queryset,
        worksheet,
        total_dict,
        furniture_type,
        guilty_manufactor_id,
    )
    report_writer.write_order_summary_xls()
    total_dict.update(
        man_fault_total_cost=report_writer.get_man_fault_cash_flow_total_cost()
    )
    return total_dict, workbook


class SheetToOrderSummary:
    statement_class: str
    manufacturer_statement_class: str

    def get_generate_sheet_classes(self):
        base_path = 'producers.admin_actions.'
        statement_sheet = import_string(f'{base_path}{self.statement_class}')
        manufacturer_statement_sheet = import_string(
            f'{base_path}{self.manufacturer_statement_class}'
        )
        return statement_sheet, manufacturer_statement_sheet

    def add_sheet_to_order_summary(
        self, workbook, queryset=None, guilty_manufactor_id=None, usage=None
    ):
        (
            generate_statement_sheet,
            generate_manufacturer_statement_sheet,
        ) = self.get_generate_sheet_classes()

        worksheet_report = workbook.create_sheet('ZESTAWIENIE')
        summary_sheet = generate_statement_sheet(
            worksheet_report,
            queryset,
            guilty_manufactor_id,
            usage=usage,
        )
        total_dict = summary_sheet.write_data_to_sheet_and_get_total_costs()

        worksheet_report_manufacturer = workbook.create_sheet(
            'ZESTAWIENIE_MANUFACTURER'
        )
        manufacturer_summary_sheet = generate_manufacturer_statement_sheet(
            worksheet_report_manufacturer,
            queryset,
            guilty_manufactor_id,
            usage=usage,
        )
        manufacturer_summary_sheet.write_data_to_sheet_and_get_total_costs()
        return workbook, total_dict


class SheetToOrderSummaryByBatch(SheetToOrderSummary):
    statement_class = 'GenerateStatementBatchSheet'
    manufacturer_statement_class = 'GenerateManufacturerStatementBatchSheet'


class SheetToOrderSummaryByProduct(SheetToOrderSummary):
    statement_class = 'GenerateStatementProductSheet'
    manufacturer_statement_class = 'GenerateManufacturerStatementProductSheet'


def get_cash_flow_total_cost(total_dict):
    return sum(
        [
            total_dict.get('cost_service', 0),
            total_dict.get('total_netto_price', 0),
            total_dict.get('total_brutto_price', 0),
            total_dict.get('material_management_cost', 0),
        ]
    )


def save_elements_order(
    elements_order,
    file,
    batch_queryset,
    manufactor,
    total_cost,
    service_cost,
    old_report_id=None,
    order_type=ElementsOrderType.NEW_ORDER,
):
    elements_order.order_file.save(f'{manufactor}_{elements_order.id}.xlsx', file)
    for batch in batch_queryset:
        elements_order.batches.add(batch)
    elements_order.total_cost = total_cost
    elements_order.service_cost = service_cost
    elements_order.order_type = order_type.value
    elements_order.manufactor = manufactor
    elements_order.save()
    if old_report_id:
        old_element_order = ElementsOrder.objects.all_orders().get(id=old_report_id)
        old_element_order.new_report = elements_order
        old_element_order.save(update_fields=['new_report'])
    if settings.EXPORT_CASH_FLOW_TO_BIG_QUERY:
        export_cash_flow_to_big_query(elements_order.id)


class WriteOrderReport:
    """
    Write order report to sheet. Report looks roughly like this:

    ======================================= (order summary)
    Podsumowanie zamowienia
    1. column - element | 2. column - extra elements | 3. column - total costs
    ======================================= (furniture types)
    Furniture type 1.
    batch 1 | batch 2 | batch3 *
    ---------------------------------------
    batch 4 ...
    ---------------------------------------
    Furniture type 2.
    ...
    * batch: element, extra elements and costs for batch in one column
    """

    elements_queryset = None
    furniture_type = None
    usages = None

    def __init__(
        self,
        elements_queryset,
        worksheet,
        total_dict,
        furniture_type,
        guilty_manufactor_id=None,
        usage=None,
    ):
        self.usages = usage or get_usage_for_batches_raw(
            elements_queryset,
            guilty_manufactor=guilty_manufactor_id,
        )
        self.elements_queryset = elements_queryset.order_by('id')
        self.worksheet = worksheet
        self.total_dict = total_dict
        self.furniture_type = furniture_type
        self.summary_costs_column = (
            15 if self.furniture_type == Furniture.watty.value else 11
        )
        self.guilty_manufactor_id = guilty_manufactor_id
        self.total_dict.update(
            production_cost=self.get_data_for_element('production_cost')
        )
        self.is_complaint_type = self.is_complaint_type()

        self.set_cells_size()

    def is_complaint_type(self):
        raise NotImplementedError

    def write_order_summary_xls(self):
        last_row = self.write_order_summary()
        self.write_furniture_types_summary(last_row)

    def write_order_summary(self):
        row = 1
        row = self.write_order_summary_headers(row)

        row = row + 2

        first_column_last_row = self.write_first_order_summary_column(row)
        second_column_last_row = self.write_second_order_summary_column(row)
        if self.furniture_type == Furniture.watty.value:
            third_column_last_row = self.write_light_summary_column(row)
        else:
            third_column_last_row = 0
        fourth_column_last_row = self.write_fourth_order_summary_column(row)
        return max(
            first_column_last_row,
            second_column_last_row,
            third_column_last_row,
            fourth_column_last_row,
        )

    def write_furniture_types_summary(self, row):
        raise NotImplementedError

    def write_elements_summary(self, last_row, filtered_queryset, row):
        row += 2
        column_start_id = 0
        for element in filtered_queryset:
            temporary_row = self.write_batch_summary(row, element, column_start_id)

            last_row = max([temporary_row, last_row])

            column_start_id = column_start_id + 1
            if column_start_id == 3:
                column_start_id = 0
                row = last_row + 5
        return last_row

    def write_furniture_type_header(self, furniture_type, row):
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=2,
                value=furniture_type[0],
            ),
            openpyxl_styles.batch_type_header_style,
        )
        self.worksheet.merge_cells(
            start_row=row,
            end_row=row,
            start_column=2,
            end_column=13,
        )

    def write_batch_summary(self, row, element, column_start_id):
        column_start = [2, 6, 10][column_start_id]
        row = self.write_element_summary_header(element, column_start, row)
        row = self.write_products_list_to_batch_summary(element, column_start, row)
        row = self.write_elements_list_in_batch_summary_header(column_start, row)
        return self.write_element_summary(element, column_start, row)

    def write_element_summary(self, element, column_start, row):
        row += 1
        elements_data = self.get_elements_list_data(element)
        extra_elements_data = self.get_extra_elements_list_data(element)
        if self.furniture_type == Furniture.watty.value:
            light_elements_data = self.get_light_elements_list_data(element)
        else:
            light_elements_data = ()
        if self.furniture_type == Furniture.sotty.value:
            total_cost_data = self.get_element_total_cost_data_sotty(element)
        elif self.is_complaint_type:
            total_cost_data = self.get_complaint_element_total_cost_data(element)
        else:
            total_cost_data = self.get_element_total_cost_data(element)

        data = (
            elements_data + extra_elements_data + light_elements_data + total_cost_data
        )
        for key, value in data:
            row = self.write_batch_summary_key_value(key, value, column_start, row)
        return row

    def write_batch_summary_key_value(self, key, value, column, row):
        row += 1
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column,
                value='-',
            ),
            openpyxl_styles.summary_style,
        )
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column + 1,
                value=key,
            ),
            openpyxl_styles.summary_style,
        )
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column + 2,
                value=value,
            ),
            openpyxl_styles.summary_style,
        )
        return row

    def write_elements_list_in_batch_summary_header(self, column_start, row):
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column_start,
                value='lp',
            ),
            openpyxl_styles.summary_style,
        )
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column_start + 1,
                value='Podsumowanie',
            ),
            openpyxl_styles.summary_style,
        )
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column_start + 2,
                value='Ilość',
            ),
            openpyxl_styles.summary_style,
        )
        return row

    def write_products_list_to_batch_summary(self, element, column_start, row):
        lps = 1
        row += 1
        batch_products = self.usages_for_selected_batches['batches_to_products_map'][
            element.id
        ]
        for item in batch_products:
            apply_styles_to_cell(
                self.worksheet.cell(
                    row=row,
                    column=column_start,
                    value=lps,
                ),
                openpyxl_styles.border_style,
            )
            #  For all files used by manufacturer,
            #  we use T1 name for our actual veneer codename
            apply_styles_to_cell(
                self.worksheet.cell(
                    row=row,
                    column=column_start + 1,
                    value='{shelf_type}_{pk}'.format(
                        shelf_type='T1'
                        if item.cached_shelf_type == 'F1'
                        else item.cached_shelf_type,
                        pk=item.id,
                    ),
                ),
                openpyxl_styles.border_style,
            )
            apply_styles_to_cell(
                self.worksheet.cell(
                    row=row,
                    column=column_start + 2,
                    value=item.order_id,
                ),
                openpyxl_styles.border_style,
            )
            row += 1
            lps += 1
        return row

    def write_element_summary_header(self, element, column_start, row):
        actual_element_color = self.get_element_color_and_type(element)
        actual_element_header = self.get_element_header(actual_element_color)
        batch = self._get_batch(element)
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column_start,
                value='Batch {}'.format(batch.id),
            ),
            actual_element_header,
        )
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column_start + 2,
                value=element.material_description,
            ),
            actual_element_header,
        )
        self.worksheet.merge_cells(
            start_row=row,
            end_row=row,
            start_column=column_start,
            end_column=column_start + 1,
        )
        self.worksheet.row_dimensions[row].height = 56
        row += 1
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column_start,
                value='lp',
            ),
            openpyxl_styles.bold_style,
        )
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column_start + 1,
                value='ID',
            ),
            openpyxl_styles.bold_style,
        )
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column_start + 2,
                value='order',
            ),
            openpyxl_styles.bold_style,
        )
        return row

    @staticmethod
    def _get_batch(element):
        raise NotImplementedError

    def write_first_order_summary_column(self, start_row):
        data = self.get_elements_list_data()
        column = 3
        row = start_row
        for key, value in data:
            self.write_order_summary_key_value(row, column, key, value)
            row += 1
        return row

    def write_second_order_summary_column(self, start_row):
        data = self.get_extra_elements_list_data()
        column = 7
        row = start_row
        for key, value in data:
            self.write_order_summary_key_value(row, column, key, value)
            row += 1
        return row

    def write_light_summary_column(self, start_row):
        data = self.get_light_elements_list_data()
        column = 11
        row = start_row
        for key, value in data:
            self.write_order_summary_key_value(row, column, key, value)
            row += 1
        return row

    def get_light_elements_list_data(self, batch=None):
        return (
            (
                'Wall Light',
                self.get_usage_based_on_codenames(['wall-light'], element=batch),
            ),
            (
                'Slab Top Light',
                self.get_usage_based_on_codenames(['slab-top-light'], element=batch),
            ),
            (
                'Slab Standard Switch',
                self.get_usage_based_on_codenames(
                    ['slab-standard-switch'], element=batch
                ),
            ),
            (
                'Slab Base Switch',
                self.get_usage_based_on_codenames(
                    ['slab-base-hanger-light-switch'], element=batch
                ),
            ),
            (
                'Slab Standard Light',
                self.get_usage_based_on_codenames(
                    ['slab-standard-light'], element=batch
                ),
            ),
            (
                'Slab Base Light',
                self.get_usage_based_on_codenames(
                    ['slab-base-hanger-light'], element=batch
                ),
            ),
            (
                'Slab Push Light',
                self.get_usage_based_on_codenames(['slab-push-light'], element=batch),
            ),
            (
                'Back Bottom Light',
                self.get_usage_based_on_codenames(['back-bottom-light'], element=batch),
            ),
            (
                'Door Exterior Light',
                self.get_usage_based_on_codenames(
                    ['door-exterior-light'], element=batch
                ),
            ),
        )

    def get_extra_elements_list_data(self, element=None):
        if self.furniture_type == Furniture.jetty.value:
            return self.get_extra_elements_list_data_jetty(element)
        elif self.furniture_type == Furniture.watty.value:
            return self.get_extra_elements_list_data_watty(element)
        elif self.furniture_type == Furniture.sotty.value:
            return self.get_extra_elements_list_data_sotty(element)

    def get_extra_elements_list_data_watty(self, element=None):
        return (
            (
                'Drawer exterior',
                self.get_usage_based_on_codenames(['drawer-exterior'], element=element),
            ),
            (
                'Frame side',
                self.get_usage_based_on_codenames(['frame-side'], element=element),
            ),
            (
                'Frame side short',
                self.get_usage_based_on_codenames(
                    ['frame-side-short'], element=element
                ),
            ),
            (
                'Frame top',
                self.get_usage_based_on_codenames(['frame-top'], element=element),
            ),
            (
                'Door exterior',
                self.get_usage_based_on_codenames(['door-exterior'], element=element),
            ),
            (
                'Door exterior top',
                self.get_usage_based_on_codenames(
                    ['door-exterior-top'], element=element
                ),
            ),
            ('Bar', self.get_usage_based_on_codenames(['bar'], element=element)),
            ('Mask', self.get_usage_based_on_codenames(['mask'], element=element)),
            (
                'Door interior',
                self.get_usage_based_on_codenames(['door-interior'], element=element),
            ),
            (
                'Drawer interior with adapters',
                self.get_usage_based_on_codenames(
                    ['drawer-interior-with-adapters'], element=element
                ),
            ),
            (
                'Slab base mask',
                self.get_usage_based_on_codenames(['slab-base-mask'], element=element),
            ),
            (
                'Slab drawer short',
                self.get_usage_based_on_codenames(
                    ['slab-drawer-short'], element=element
                ),
            ),
            (
                'Wall plywood',
                self.get_usage_based_on_codenames(['wall-plywood'], element=element),
            ),
            (
                'Wall chipboard',
                self.get_usage_based_on_codenames(['wall-chipboard'], element=element),
            ),
            (
                'Slab top lock',
                self.get_usage_based_on_codenames(['slab-top-lock'], element=element),
            ),
            (
                'Cross bar',
                self.get_usage_based_on_codenames(['cross-bar'], element=element),
            ),
            (
                'Edge banding',
                self.get_usage_based_on_codenames(['edge-banding'], element=element),
            ),
            (
                'Back with grommet',
                self.get_usage_based_on_codenames(['grommet-cut-out'], element=element),
            ),
            (
                'Wall extension plywood',
                self.get_usage_based_on_codenames(
                    ['wall-extension-plywood'], element=element
                ),
            ),
            (
                'Wardrobe packaging',
                self.get_watty_packaging_item_count(
                    is_complaint=self.is_complaint_type, batch=element
                ),
            ),
            (
                'Complaint packaging',
                self.get_watty_complaint_packaging_item_count(
                    is_complaint=self.is_complaint_type, batch=element
                ),
            ),
        )

    def get_watty_packaging_item_count(self, is_complaint, batch):
        if is_complaint:
            return 0
        return self.get_data_for_element('item_count', element=batch)

    def get_watty_complaint_packaging_item_count(self, is_complaint, batch):
        if is_complaint:
            return self.get_data_for_element('item_count', element=batch)
        return 0

    def get_extra_elements_list_data_sotty(self, element=None):
        return (
            (
                'tkanina corduroy-blue-klein ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'corduroy-blue-klein',
                    element=element,
                ),
            ),
            (
                'tkanina corduroy-camouflage ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'corduroy-camouflage',
                    element=element,
                ),
            ),
            (
                'tkanina corduroy-dark-brown ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'corduroy-dark-brown',
                    element=element,
                ),
            ),
            (
                'tkanina corduroy-ecru ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'corduroy-ecru',
                    element=element,
                ),
            ),
            (
                'tkanina corduroy-pink ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'corduroy-pink',
                    element=element,
                ),
            ),
            (
                'tkanina corduroy-rock ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'corduroy-rock',
                    element=element,
                ),
            ),
            (
                'tkanina corduroy-steel ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'corduroy-steel',
                    element=element,
                ),
            ),
            (
                'tkanina corduroy-tobacco ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'corduroy-tobacco',
                    element=element,
                ),
            ),
            (
                'tkanina rewool2-baby-blue ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'rewool2-baby-blue',
                    element=element,
                ),
            ),
            (
                'tkanina rewool2-brown ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'rewool2-brown',
                    element=element,
                ),
            ),
            (
                'tkanina rewool2-butter-yellow ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'rewool2-butter-yellow',
                    element=element,
                ),
            ),
            (
                'tkanina rewool2-green ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'rewool2-green',
                    element=element,
                ),
            ),
            (
                'tkanina rewool2-light-gray ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'rewool2-light-gray',
                    element=element,
                ),
            ),
            (
                'tkanina rewool2-olive-green ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'rewool2-olive-green',
                    element=element,
                ),
            ),
            (
                'tkanina rewool2-shadow-pink ogółem mb',
                self.get_material_usage_for_color_sotty(
                    'rewool2-shadow-pink',
                    element=element,
                ),
            ),
        )

    def get_extra_elements_list_data_jetty(self, element=None):
        return (
            (
                'Połączenia czołowe 320/400mm',
                self.get_usage_based_on_codenames(
                    ['_connection-horizontal'],
                    element=element,
                    no_element_prefix=True,
                ),
            ),
            (
                'Połączenia czołowe 240mm',
                self.get_usage_based_on_codenames(
                    ['_connection-horizontal-240'],
                    element=element,
                    no_element_prefix=True,
                ),
            ),
            (
                'Frezowanie przelotki S+',
                self.get_usage_based_on_codenames(
                    ['back-grommet-cut-out', 'horizontal-grommet-cut-out'],
                    element=element,
                ),
            ),
            (
                'Elementy poziome z nogami S+',
                self.get_usage_based_on_codenames(['horizontal-leg'], element=element),
            ),
            (
                'Inserty S+',
                self.get_usage_based_on_codenames(['insert'], element=element),
            ),
            (
                'Belki cokołu S+',
                self.get_usage_based_on_codenames(['plinth-beam'], element=element),
            ),
            (
                'Poprzeczki cokołu S+',
                self.get_usage_based_on_codenames(['plinth-bar'], element=element),
            ),
            (
                'Zakończenia cokołu S+',
                self.get_usage_based_on_codenames(['plinth-end'], element=element),
            ),
            (
                'Belki cokołu short',
                self.get_usage_based_on_codenames(
                    ['plinth-short-beam'],
                    element=element,
                ),
            ),
            (
                'Poprzeczki cokołu short',
                self.get_usage_based_on_codenames(
                    ['plinth-short-end'],
                    element=element,
                ),
            ),
            (
                'Ściana tylna biurka',
                self.get_usage_based_on_codenames(['back-desk'], element=element),
            ),
            (
                'Vertical solo',
                self.get_usage_based_on_codenames(['vertical-solo'], element=element),
            ),
            (
                'Usługa obrzeżowania [mb]',
                self.get_usage_based_on_codenames(
                    ['_banding', '_walnut-banding', '_matte-banding'],
                    element=element,
                    no_element_prefix=True,
                ),
            ),
            (
                'Ściany tylne spinające',
                self.get_usage_based_on_codenames(
                    ['_element-supporting-back', '_matte-element-supporting-back'],
                    element=element,
                    no_element_prefix=True,
                ),
            ),
        )

    def get_elements_list_data(self, element=None):
        if self.furniture_type == Furniture.jetty.value:
            return self.get_elements_list_data_jetty(element)
        if self.furniture_type == Furniture.watty.value:
            return self.get_elements_list_data_watty(element)
        if self.furniture_type == Furniture.sotty.value:
            return self.get_elements_list_data_sotty(element)

    def get_elements_list_data_watty(self, batch=None):
        return (
            (
                'Szafy ogółem',
                self.get_data_for_element('item_count', element=batch),
            ),
            (
                'Wall',
                self.get_usage_based_on_codenames(
                    ['wall', 'wall-chipboard-extreme'], element=batch
                ),
            ),
            (
                'Wall extension',
                self.get_usage_based_on_codenames(['wall-extension'], element=batch),
            ),
            (
                'Back bottom',
                self.get_usage_based_on_codenames(['back-bottom'], element=batch),
            ),
            (
                'Back middle',
                self.get_usage_based_on_codenames(['back-middle'], element=batch),
            ),
            (
                'Back top a',
                self.get_usage_based_on_codenames(['back-top-a'], element=batch),
            ),
            (
                'Back top b',
                self.get_usage_based_on_codenames(['back-top-b'], element=batch),
            ),
            (
                'Slab base',
                self.get_usage_based_on_codenames(['slab-base'], element=batch),
            ),
            (
                'Slab base hanger',
                self.get_usage_based_on_codenames(['slab-base-hanger'], element=batch),
            ),
            (
                'Slab standard',
                self.get_usage_based_on_codenames(['slab-standard'], element=batch),
            ),
            (
                'Slab drawer',
                self.get_usage_based_on_codenames(['slab-drawer'], element=batch),
            ),
            (
                'Slab top',
                self.get_usage_based_on_codenames(['slab-top'], element=batch),
            ),
            (
                'Slab push',
                self.get_usage_based_on_codenames(['slab-push'], element=batch),
            ),
            (
                'Drawer interior',
                self.get_usage_based_on_codenames(['drawer-interior'], element=batch),
            ),
            (
                'Drawer interior with adapter',
                self.get_usage_based_on_codenames(
                    ['drawer-interior-with-adapter'], element=batch
                ),
            ),
        )

    def get_elements_list_data_jetty(self, element=None):
        return (
            (
                'Regały - ogółem',
                self.get_data_for_element('item_count', element=element),
            ),
            (
                'Elementy poziome',
                self.get_usage_based_on_codenames(
                    ['horizontal', 'horizontal-desk'], element=element
                ),
            ),
            (
                'Elementy pionowe bez wiercenia 240mm',
                self.get_usage_based_on_codenames(
                    ['vertical-standard-240'], element=element
                ),
            ),
            (
                'Elementy pionowe bez wiercenia 320mm',
                self.get_usage_based_on_codenames(
                    ['vertical-standard-320'], element=element
                ),
            ),
            (
                'Elementy pionowe bez wiercenia 400mm',
                self.get_usage_based_on_codenames(
                    ['vertical-standard-400'], element=element
                ),
            ),
            (
                'Elementy pionowe bez wiercenia 500mm',
                self.get_usage_based_on_codenames(
                    ['vertical-standard-500'], element=element
                ),
            ),
            (
                'Elementy pionowe z wierceniami 240mm',
                self.get_usage_based_on_codenames(
                    ['vertical-drilled-240'], element=element
                ),
            ),
            (
                'Elementy pionowe z wierceniami 320mm',
                self.get_usage_based_on_codenames(
                    ['vertical-drilled-320'], element=element
                ),
            ),
            (
                'Elementy pionowe z wierceniami 400mm',
                self.get_usage_based_on_codenames(
                    ['vertical-drilled-400'], element=element
                ),
            ),
            (
                'Elementy pionowe z wierceniami 500mm',
                self.get_usage_based_on_codenames(
                    ['vertical-drilled-500'], element=element
                ),
            ),
            (
                'Elementy wsporniki',
                self.get_usage_based_on_codenames(
                    ['support', 'support-chipboard'], element=element
                ),
            ),
            (
                'Drzwi T01',
                self.get_usage_based_on_codenames(
                    DOOR_VARIANT_LIST,
                    custom_manufacturer_list=['DTR', 'NOV'],
                    custom_product_names_list=['T1', 'F1'],
                    element=element,
                ),
            ),
            (
                'Drzwi T02',
                self.get_usage_based_on_codenames(
                    DOOR_VARIANT_LIST,
                    custom_product_names_list=['T6'],
                    element=element,
                ),
            ),
            (
                'Ściany tylne',
                self.get_usage_based_on_codenames(['back'], element=element),
            ),
            (
                'Szuflady',
                self.get_usage_based_on_codenames(
                    ['_drawer'],
                    element=element,
                    no_element_prefix=True,
                ),
            ),
            (
                'Ściany tylne DEFG',
                self.get_usage_based_on_codenames(
                    ['back-DEFG'],
                    element=element,
                    custom_manufacturer_list=['DTR'],
                    custom_product_names_list=['T1', 'T6', 'F1'],
                ),
            ),
            (
                'Drzwi DEFG',
                self.get_usage_based_on_codenames(
                    ['door-DEFG'],
                    element=element,
                    custom_manufacturer_list=['DTR'],
                    custom_product_names_list=['T1', 'T6', 'F1'],
                ),
            ),
            (
                'Elementy pionowe z wierceniami DEFG 240mm',
                self.get_usage_based_on_codenames(
                    ['vertical-drilled-240-DEFG'],
                    element=element,
                    custom_manufacturer_list=['DTR'],
                    custom_product_names_list=['T1', 'T6', 'F1'],
                ),
            ),
            (
                'Elementy pionowe z wierceniami DEFG 320mm',
                self.get_usage_based_on_codenames(
                    ['vertical-drilled-320-DEFG'],
                    element=element,
                    custom_manufacturer_list=['DTR'],
                    custom_product_names_list=['T1', 'T6', 'F1'],
                ),
            ),
            (
                'Elementy pionowe z wierceniami DEFG 400mm',
                self.get_usage_based_on_codenames(
                    ['vertical-drilled-400-DEFG'],
                    element=element,
                    custom_manufacturer_list=['DTR'],
                    custom_product_names_list=['T1', 'T6', 'F1'],
                ),
            ),
            (
                'Elementy pionowe z wierceniami DEFG 500mm',
                self.get_usage_based_on_codenames(
                    ['vertical-drilled-500-DEFG'],
                    element=element,
                    custom_manufacturer_list=['DTR'],
                    custom_product_names_list=['T1', 'T6', 'F1'],
                ),
            ),
            (
                'Elementy pionowe bez wiercenia DEFG 240mm',
                self.get_usage_based_on_codenames(
                    ['vertical-standard-240-DEFG'],
                    element=element,
                    custom_manufacturer_list=['DTR'],
                    custom_product_names_list=['T1', 'T6', 'F1'],
                ),
            ),
            (
                'Elementy pionowe bez wiercenia DEFG 320mm',
                self.get_usage_based_on_codenames(
                    ['vertical-standard-320-DEFG'],
                    element=element,
                    custom_manufacturer_list=['DTR'],
                    custom_product_names_list=['T1', 'T6', 'F1'],
                ),
            ),
            (
                'Elementy pionowe bez wiercenia DEFG 400mm',
                self.get_usage_based_on_codenames(
                    ['vertical-standard-400-DEFG'],
                    element=element,
                    custom_manufacturer_list=['DTR'],
                    custom_product_names_list=['T1', 'T6', 'F1'],
                ),
            ),
            (
                'Elementy pionowe bez wiercenia DEFG 500mm',
                self.get_usage_based_on_codenames(
                    ['vertical-standard-500-DEFG'],
                    element=element,
                    custom_manufacturer_list=['DTR'],
                    custom_product_names_list=['T1', 'T6', 'F1'],
                ),
            ),
        )

    def get_elements_list_data_sotty(self, element=None):
        return (
            (
                'Sofy - ogółem',
                self.get_data_for_element('item_count', element=element),
            ),
            (
                'seater - ogółem',
                self.get_semiproduct_usage_for_part_sotty(
                    part='seater', element=element
                ),
            ),
            (
                'chaise-lounge - ogółem',
                self.get_semiproduct_usage_for_part_sotty(
                    part='chaise-lounge', element=element
                ),
            ),
            (
                'corner - ogółem',
                self.get_semiproduct_usage_for_part_sotty(
                    part='corner', element=element
                ),
            ),
            (
                'footrest - ogółem',
                self.get_semiproduct_usage_for_part_sotty(
                    part='footrest', element=element
                ),
            ),
            (
                'armrest - ogółem',
                self.get_semiproduct_usage_for_part_sotty(
                    part='armrest', element=element
                ),
            ),
            (
                'backrest - ogółem',
                self.get_semiproduct_usage_for_part_sotty(
                    part='backrest', element=element
                ),
            ),
            (
                'cushion - ogółem',
                self.get_semiproduct_usage_for_part_sotty(
                    part='cushion', element=element
                ),
            ),
            (
                'corner backrest - ogółem',
                self.get_semiproduct_usage_for_part_sotty(
                    part='corner-backrest', element=element
                ),
            ),
            (
                'corner cusion - ogółem',
                self.get_semiproduct_usage_for_part_sotty(
                    part='corner-cusion', element=element
                ),
            ),
        )

    def write_order_summary_key_value(self, row, column, key, value):
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column,
                value=key,
            ),
            openpyxl_styles.summary_style,
        )
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column + 1,
                value=value,
            ),
            openpyxl_styles.summary_style,
        )

    def write_fourth_order_summary_column(self, row):
        if self.furniture_type == Furniture.sotty.value:
            return self.write_total_costs_to_fourth_order_summary_column_sotty(row)
        if self.is_complaint_type:
            return self.write_complaints_to_fourth_order_summary_column(row)
        else:
            return self.write_total_costs_to_fourth_order_summary_column(row)

    def write_total_costs_to_fourth_order_summary_column(self, row):
        column = self.summary_costs_column
        data = self.get_total_cost_data()
        for key, value in data:
            self.write_order_summary_key_value(row, column, key, value)
            row += 1
        return row

    def write_total_costs_to_fourth_order_summary_column_sotty(self, row):
        column = self.summary_costs_column
        data = (
            (
                'Koszt - ogółem',
                '{} zł'.format(
                    format(get_cash_flow_total_cost(self.total_dict), '.2f')
                ),
            ),
            (
                'Koszt - moduły',
                '{} zł'.format(format(self.total_dict.get('sofa_parts', 0), '.2f')),
            ),
            (
                'Koszt - pokrowcy',
                '{} zł'.format(format(self.total_dict.get('sofa_materials', 0), '.2f')),
            ),
            (
                'Koszt - serwisy',
                '{} zł'.format(format(self.total_dict.get('cost_service', 0), '.2f')),
            ),
            (
                'Koszt - fittings',
                '{} zł'.format(format(self.total_dict.get('fittings', 0), '.2f')),
            ),
        )
        for key, value in data:
            self.write_order_summary_key_value(row, column, key, value)
            row += 1
        return row

    def get_total_cost_data(self):
        data = (
            (
                'Koszt - ogółem',
                '{} zł'.format(
                    format(get_cash_flow_total_cost(self.total_dict), '.2f')
                ),
            ),
            (
                'Koszt - serwis',
                '{} zł'.format(format(self.total_dict.get('cost_service', 0), '.2f')),
            ),
            (
                'Koszt - materiał netto',
                '{} zł'.format(
                    format(self.total_dict.get('total_netto_price', 0), '.2f')
                ),
            ),
            (
                'Koszt - materiał straty',
                '{} zł'.format(
                    format(self.total_dict.get('total_brutto_price', 0), '.2f')
                ),
            ),
            (
                'Koszt zarządzania materiałem',
                '{} zł'.format(
                    format(self.total_dict.get('material_management_cost', 0), '.2f')
                ),
            ),
        )
        return data

    def get_element_total_cost_data(self, batch):
        data = (
            ('Koszt', format(self.get_cash_flow_total_cost_for_element(batch), '.2f')),
            (
                'Koszt - serwis',
                format(self.get_total_cost_for_element(batch, 'cost_service'), '.2f'),
            ),
            (
                'Koszt - materiał netto',
                format(
                    self.get_total_cost_for_element(batch, 'total_netto_price'), '.2f'
                ),
            ),
            (
                'Koszt - materiał straty',
                format(
                    self.get_total_cost_for_element(
                        batch,
                        'total_brutto_price',
                    ),
                    '.2f',
                ),
            ),
            (
                'Koszt zarządzania materiałem',
                format(
                    self.get_total_cost_for_element(
                        batch,
                        'material_management_cost',
                    ),
                    '.2f',
                ),
            ),
        )
        return data

    def get_element_total_cost_data_sotty(self, batch):
        data = (
            ('Koszt', format(self.get_cash_flow_total_cost_for_element(batch), '.2f')),
            (
                'Koszt - moduły',
                format(self.get_total_cost_for_element(batch, 'sofa_parts'), '.2f'),
            ),
            (
                'Koszt - pokrowcy',
                format(self.get_total_cost_for_element(batch, 'sofa_materials'), '.2f'),
            ),
            (
                'Koszt - serwisy',
                format(self.get_total_cost_for_element(batch, 'cost_service'), '.2f'),
            ),
            (
                'Koszt - fittings',
                format(self.get_total_cost_for_element(batch, 'fittings'), '.2f'),
            ),
        )
        return data

    def get_complaint_element_total_cost_data(self, batch):
        if self.guilty_manufactor_id:
            total_cost = self.get_batch_man_fault_total_cost(batch)
            service_cost = 0 - self.get_batch_man_fault_cost_service(batch)
            material_cost = self.get_batch_man_fault_complaint_material_cost(batch)
        else:
            total_cost = self.get_cash_flow_total_cost_for_element(batch)
            service_cost = self.get_total_cost_for_element(batch, 'cost_service')
            material_cost = self.get_batch_complaint_material_cost(batch)
        return (
            ('Koszt reklamacji - ogółem', '{:.2f} zł'.format(total_cost)),
            ('Koszt reklamacji - serwis', '{:.2f} zł'.format(service_cost)),
            ('Koszt reklamacji - materiał', '{:.2f} zł'.format(material_cost)),
        )

    def write_complaints_to_fourth_order_summary_column(self, row):
        column = self.summary_costs_column
        complaints_data = self.get_fourth_column_complaints_data()
        for key, value in complaints_data:
            self.write_order_summary_key_value(row, column, key, value)
            row += 1
        return row

    def get_fourth_column_complaints_data(self):
        if self.guilty_manufactor_id:
            total_cost = self.get_man_fault_cash_flow_total_cost()
            service_cost = self.get_total_man_fault_cost_service()
            material_cost = self.get_man_fault_complaint_material_cost()
        else:
            total_cost = get_cash_flow_total_cost(self.total_dict)
            service_cost = self.total_dict.get('cost_service', 0)
            material_cost = self.get_complaint_material_cost()
        return (
            ('Koszt reklamacji - ogółem', '{:.2f} zł'.format(total_cost)),
            ('Koszt reklamacji - serwis', '{:.2f} zł'.format(service_cost)),
            ('Koszt reklamacji - materiał', '{:.2f} zł'.format(material_cost)),
        )

    def get_total_man_fault_cost_service(self):
        cost_service = 0
        for batch in self.elements_queryset:
            cost_service += self.get_total_cost_for_element(batch, 'cost_service')
        return 0 - cost_service

    def get_batch_man_fault_cost_service(self, batch):
        return self.get_total_cost_for_element(batch, 'cost_service')

    def get_man_fault_cash_flow_total_cost(self):
        return 0 - sum(
            [
                0 - self.get_total_man_fault_cost_service(),
                self.total_dict.get('tylko_netto_price', 0),
                self.total_dict.get('total_netto_price', 0),
                self.total_dict.get('tylko_brutto_price', 0),
                self.total_dict.get('total_brutto_price', 0),
                self.total_dict.get('material_management_cost', 0),
            ]
        )

    def get_batch_man_fault_total_cost(self, batch):
        return 0 - sum(
            [
                self.get_batch_man_fault_cost_service(batch),
                self.get_total_cost_for_element(batch, 'tylko_netto_price'),
                self.get_total_cost_for_element(batch, 'tylko_brutto_price'),
                self.get_total_cost_for_element(batch, 'total_netto_price'),
                self.get_total_cost_for_element(batch, 'total_brutto_price'),
                self.get_total_cost_for_element(batch, 'material_management_cost'),
            ]
        )

    def get_complaint_material_cost(self):
        return sum(
            [
                self.total_dict.get('total_netto_price', 0),
                self.total_dict.get('total_brutto_price', 0),
                self.total_dict.get('material_management_cost', 0),
            ]
        )

    def get_batch_complaint_material_cost(self, batch):
        return sum(
            [
                self.get_total_cost_for_element(batch, 'total_netto_price'),
                self.get_total_cost_for_element(batch, 'total_brutto_price'),
                self.get_total_cost_for_element(batch, 'material_management_cost'),
            ]
        )

    def get_man_fault_complaint_material_cost(self):
        return 0 - sum(
            [
                self.total_dict.get('tylko_netto_price', 0),
                self.total_dict.get('total_netto_price', 0),
                self.total_dict.get('tylko_brutto_price', 0),
                self.total_dict.get('total_brutto_price', 0),
                self.total_dict.get('material_management_cost', 0),
            ]
        )

    def get_batch_man_fault_complaint_material_cost(self, batch):
        return 0 - sum(
            [
                self.get_total_cost_for_element(batch, 'tylko_netto_price'),
                self.get_total_cost_for_element(batch, 'total_netto_price'),
                self.get_total_cost_for_element(batch, 'tylko_brutto_price'),
                self.get_total_cost_for_element(batch, 'total_brutto_price'),
                self.get_total_cost_for_element(batch, 'material_management_cost'),
            ]
        )

    def write_order_summary_headers(self, actual_row):
        apply_styles_to_cell(
            self.worksheet.cell(
                row=actual_row,
                column=2,
                value='ZAMÓWIENIE Z DNIA {date}'.format(
                    date=datetime.now().strftime('%Y-%m-%d')
                ),
            ),
            openpyxl_styles.top_header_style,
        )
        apply_styles_to_cell(
            self.worksheet.cell(
                row=actual_row,
                column=self.summary_costs_column + 1,
                value='tylko',
            ),
            openpyxl_styles.top_header_style,
        )
        self.worksheet.merge_cells(
            start_row=actual_row,
            end_row=actual_row,
            start_column=2,
            end_column=self.summary_costs_column,
        )
        actual_row += 2
        apply_styles_to_cell(
            self.worksheet.cell(
                row=actual_row,
                column=2,
                value='PODSUMOWANIE ZAMÓWIENIA',
            ),
            openpyxl_styles.batch_type_header_style,
        )
        self.worksheet.merge_cells(
            start_row=actual_row,
            end_row=actual_row,
            start_column=2,
            end_column=self.summary_costs_column + 2,
        )
        return actual_row

    def set_cells_size(self):
        small_column = 3
        medium_column = 14
        big_column = 56
        self.worksheet.column_dimensions['A'].width = small_column
        self.worksheet.column_dimensions['B'].width = small_column
        self.worksheet.column_dimensions['C'].width = medium_column
        self.worksheet.column_dimensions['D'].width = medium_column
        self.worksheet.column_dimensions['E'].width = small_column
        self.worksheet.column_dimensions['F'].width = small_column
        self.worksheet.column_dimensions['G'].width = medium_column
        self.worksheet.column_dimensions['H'].width = medium_column
        self.worksheet.column_dimensions['I'].width = small_column
        self.worksheet.column_dimensions['J'].width = small_column
        self.worksheet.column_dimensions['K'].width = medium_column
        self.worksheet.column_dimensions['L'].width = medium_column
        self.worksheet.column_dimensions['M'].width = small_column
        for x in range(5, 14):
            self.worksheet.row_dimensions[x].height = big_column

    @staticmethod
    def get_element_header(material):
        return {
            'T1_0': openpyxl_styles.batch_white_header,
            'T1_1': openpyxl_styles.batch_black_header,
            'T1_3': openpyxl_styles.batch_gray_header,
            'T1_4': openpyxl_styles.batch_purple_header,
            'T1_5': openpyxl_styles.batch_yellow_header,
            'T1_6': openpyxl_styles.batch_red_header,
            'T1_7': openpyxl_styles.batch_yellow_header,
            'T1_8': openpyxl_styles.batch_pink_header,
            'T2_0': openpyxl_styles.batch_white_header,
            'T2_1': openpyxl_styles.batch_ceramic_header,
            'T2_2': openpyxl_styles.batch_indygo_header,
            'T2_3': openpyxl_styles.batch_beige_header,
            'T2_4': openpyxl_styles.batch_mint_header,
            'F1_0': openpyxl_styles.batch_ash_header,
            'F1_1': openpyxl_styles.batch_oak_header,
        }.get(material, openpyxl_styles.batch_white_header)

    @staticmethod
    def get_element_color_and_type(element):
        raise NotImplementedError

    def get_data_for_element(self, key, element=None, complaint=False):
        raise NotImplementedError

    def get_usage_based_on_codenames(
        self,
        list_of_names,
        no_element_prefix=False,
        element=None,
        custom_manufacturer_list=None,
        custom_product_names_list=None,
    ):
        if self.furniture_type == Furniture.jetty.value:
            return self.get_usage_based_on_codenames_jetty(
                list_of_names,
                no_element_prefix,
                element,
                custom_manufacturer_list,
                custom_product_names_list,
            )
        elif self.furniture_type == Furniture.watty.value:
            return self.get_usage_based_on_codenames_watty(
                list_of_names,
                element,
            )

    def get_usage_based_on_codenames_watty(
        self,
        list_of_names,
        element=None,
    ):
        total_value = 0
        manufacturer_codes_list = [
            'S93',
            'NOV',
            'DTR',
            'MPL',
            'INX',
            'AMI',
            'CML',
            'TLX',
        ]
        product_names_list = ['W3', 'W13', 'F13', 'T23', 'T24', 'T25']
        for name in list_of_names:
            for manufacturer_code in manufacturer_codes_list:
                for product_type in product_names_list:
                    total_value += self.get_data_for_element(
                        f'service_{product_type}_{manufacturer_code}_{name}',
                        element=element,
                    )
        return total_value

    def get_usage_based_on_codenames_jetty(
        self,
        list_of_names,
        no_element_prefix=False,
        element=None,
        custom_manufacturer_list=None,
        custom_product_names_list=None,
    ):
        total_value = 0
        product_names_list = ['T1', 'T6', 'F1']
        manufacturer_codes_list = ['S93', 'NOV', 'DTR', 'MPL', 'INX', 'AMI', 'TLX']
        for codename in list_of_names:
            # there is no need to check if custom entries are empty -
            # as its not valid case
            type_list = itertools.product(
                custom_product_names_list or product_names_list,
                custom_manufacturer_list or manufacturer_codes_list,
            )
            prefix = '' if no_element_prefix else '_element-'
            for product_type, manufacturer in type_list:
                total_value += self.get_data_for_element(
                    f'service_{product_type}_{manufacturer}{prefix}{codename}',
                    element=element,
                )
        return total_value

    def get_material_usage_for_color_sotty(self, color, element=None):
        base_codename = f'material_fabric_{color}_'
        category = f'B_{element.id}' if element else 'total'
        data = self.usages['wynik'].get(category, {})
        results = 0
        for key in data.keys():
            if key.startswith(base_codename):
                results += data.get(key, 0)
        return results

    def get_semiproduct_usage_for_part_sotty(self, part, element=None):
        prefix_codename = f'semiproduct_S1_blank_{part}'
        category = f'B_{element.id}' if element else 'total'
        data = self.usages['wynik'].get(category, {})
        results = 0
        for key in data.keys():
            if key.startswith(prefix_codename) and part in key:
                results += data.get(key, 0)
        return results

    def get_total_cost_for_element(self, element, key):
        raise NotImplementedError

    def get_cash_flow_total_cost_for_element(self, element):
        raise NotImplementedError


class WriteOrderBatchReport(WriteOrderReport):
    """
    Write order report to sheet. Report looks roughly like this:

    ======================================= (order summary)
    Podsumowanie zamowienia
    1. column - element | 2. column - extra elements | 3. column - total costs
    ======================================= (furniture types)
    Furniture type 1.
    batch 1 | batch 2 | batch3 *
    ---------------------------------------
    batch 4 ...
    ---------------------------------------
    Furniture type 2.
    ...
    * batch: element, extra elements and costs for batch in one column
    """

    def is_complaint_type(self):
        return self.elements_queryset.filter(
            batch_type__exact=BatchType.COMPLAINTS
        ).exists()

    def write_furniture_types_summary(self, row):
        row = row + 4
        last_row = row
        for furniture_type in FURNITURE_TYPES:
            filtered_queryset = self.elements_queryset.filter(
                batch_type=furniture_type[1]
            )
            if not filtered_queryset.exists():
                continue
            self.write_furniture_type_header(furniture_type, row)
            last_row = self.write_elements_summary(last_row, filtered_queryset, row)
            row = last_row + 7

    def write_products_list_to_batch_summary(self, element, column_start, row):
        lps = 1
        row += 1
        batch_products = self.usages['batches_to_products_map'][element.id]
        for item in batch_products:
            apply_styles_to_cell(
                self.worksheet.cell(
                    row=row,
                    column=column_start,
                    value=lps,
                ),
                openpyxl_styles.border_style,
            )
            #  For all files used by manufacturer,
            #  we use T1 name for our actual veneer codename
            apply_styles_to_cell(
                self.worksheet.cell(
                    row=row,
                    column=column_start + 1,
                    value='{shelf_type}_{pk}'.format(
                        shelf_type='T1'
                        if item.cached_shelf_type == 'F1'
                        else item.cached_shelf_type,
                        pk=item.id,
                    ),
                ),
                openpyxl_styles.border_style,
            )
            apply_styles_to_cell(
                self.worksheet.cell(
                    row=row,
                    column=column_start + 2,
                    value=item.order_id,
                ),
                openpyxl_styles.border_style,
            )
            row += 1
            lps += 1
        return row

    def get_data_for_element(self, key, element=None, complaint=False):
        category = 'B_{}'.format(element.id) if element else 'total'
        return self.usages['wynik'].get(category, {}).get(key, 0)

    @staticmethod
    def get_element_color_and_type(element):
        if element.has_complaints:
            return 'REKLAMACJA_0'
        try:
            item = element.batch_items.first()
            shelf_item = item.order_item.order_item
            if shelf_item.shelf_type == ShelfType.VENEER_TYPE01.value:
                return 'F1_{}'.format(shelf_item.material)
            else:
                return 'T{}_{}'.format(shelf_item.shelf_type + 1, shelf_item.material)
        # FIXME
        except Exception as e:
            logger.error(e, exc_info=True)
            return 'T1_0'

    @staticmethod
    def _get_batch(element):
        return element

    def get_total_cost_for_element(self, element, key):
        batches = self.total_dict.get('batches', {})
        batches_data = batches.get(f'B_{element.id}', {})
        value = batches_data.get(key, 0)
        return value

    def get_cash_flow_total_cost_for_element(self, element):
        batches = self.total_dict.get('batches', {})
        batch_name = f'B_{element.id}'
        batch_costs = batches.get(batch_name, defaultdict(int))
        return get_cash_flow_total_cost(batch_costs)


class WriteOrderProductReport(WriteOrderReport):
    def is_complaint_type(self):
        return self.elements_queryset.filter(
            batch__batch_type__exact=BatchType.COMPLAINTS
        ).exists()

    def write_furniture_types_summary(self, row):
        row = row + 4
        last_row = row
        for furniture_type in FURNITURE_TYPES:
            filtered_queryset = self.elements_queryset.filter(
                batch__batch_type=furniture_type[1]
            ).annotate(material_description=F('batch__material_description'))
            if not filtered_queryset.exists():
                continue
            self.write_furniture_type_header(furniture_type, row)
            last_row = self.write_elements_summary(last_row, filtered_queryset, row)
            row = last_row + 7

    def write_products_list_to_batch_summary(self, element, column_start, row):
        lps = 1
        row += 1
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column_start,
                value=lps,
            ),
            openpyxl_styles.border_style,
        )
        #  For all files used by manufacturer,
        #  we use T1 name for our actual veneer codename
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column_start + 1,
                value='{shelf_type}_{pk}'.format(
                    shelf_type='T1'
                    if element.cached_shelf_type == 'F1'
                    else element.cached_shelf_type,
                    pk=element.id,
                ),
            ),
            openpyxl_styles.border_style,
        )
        apply_styles_to_cell(
            self.worksheet.cell(
                row=row,
                column=column_start + 2,
                value=element.order_id,
            ),
            openpyxl_styles.border_style,
        )
        row += 1
        lps += 1
        return row

    def get_data_for_element(self, key, element=None, complaint=False):
        category = element.id if element else 'total'
        return self.usages['wynik'].get(category, {}).get(key, 0)

    @staticmethod
    def get_element_color_and_type(element):
        if element.batch.has_complaints:
            return 'REKLAMACJA_0'
        try:
            shelf_item = element.order_item.order_item
            if shelf_item.shelf_type == ShelfType.VENEER_TYPE01.value:
                return 'F1_{}'.format(shelf_item.material)
            else:
                return 'T{}_{}'.format(shelf_item.shelf_type + 1, shelf_item.material)
        # FIXME
        except Exception as e:
            logger.error(e, exc_info=True)
            return 'T1_0'

    @staticmethod
    def _get_batch(element):
        return element.batch

    def get_total_cost_for_element(self, element, key):
        products = self.total_dict.get('products', {})
        products_data = products.get(element.id, {})
        value = products_data.get(key, 0)
        return value

    def get_cash_flow_total_cost_for_element(self, element):
        products = self.total_dict.get('products', {})
        product_cost = products.get(element.id, defaultdict(int))
        return get_cash_flow_total_cost(product_cost)
