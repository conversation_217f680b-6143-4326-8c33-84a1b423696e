import logging

from datetime import datetime
from operator import attrgetter
from typing import Iterable

from django.db import transaction

from complaints.enums import ComplaintStatus
from complaints.tasks import update_complaint_cost
from producers.choices import (
    BatchNeededActions,
    BatchStatus,
    BatchType,
    ProductStatus,
)
from producers.enums import Manufacturers
from producers.errors import CannotBatchAbortedProducts
from producers.models import (
    Manufactor,
    Product,
    ProductBatch,
    ProductComplaint,
)
from producers.physical_version_adjustment import PhysicalVersionAdjustment
from producers.production_system_utils.client import ProductionSystemClient

logger = logging.getLogger('producers')


class ProductBatchService:
    def create(
        self,
        products: Iterable[Product],
        manufactor: Manufactor,
        batch_type: BatchType = BatchType.STANDARD,
        batch_status: BatchStatus = BatchStatus.NEW,
    ) -> ProductBatch:
        self.validate_aborted_products(products)
        batch = self.create_batch_with_products(
            manufactor, products, batch_type, batch_status
        )
        self.update_batch_related_objects(batch)
        return batch

    def update_products_serialization_and_files(
        self,
        products: Iterable[Product],
        batch_created_at: datetime,
    ) -> None:
        with ProductionSystemClient(suppress_errors=True) as ps_client:
            for product in products:
                self.update_estimated_production_date(product)
                PhysicalVersionAdjustment().apply_to(product)
                product.status_updater.change_status(
                    ProductStatus.ASSIGNED_TO_PRODUCTION
                )
                if product.is_complaint() and product.reproduction_origin_complaint:
                    self.update_complaint_object(product, batch_created_at)

                if not product.is_sotty:
                    self._remove_old_files(product)
                    try:
                        self._generate_files_for(product, ps_client)
                    except Exception:
                        logger.exception(
                            'Error while generating files '
                            'during batching for product %s',
                            product.id,
                            exc_info=True,
                        )

    def update_estimated_production_date(self, product: Product) -> None:
        estimated_production_date = (
            product.get_estimated_production_date_by_manufactor()
        )
        if estimated_production_date:
            product.estimated_production_date = estimated_production_date
            product.save(update_fields=['estimated_production_date'])

    @classmethod
    def update_complaint_object(
        cls, product: Product, batch_created_at: datetime
    ) -> None:
        complaint = product.reproduction_origin_complaint
        complaint.change_status(
            status_new=ComplaintStatus.IN_PROGRESS.value,
        )
        complaint.production_ordered_date = batch_created_at.date()
        complaint.save(update_fields=['production_ordered_date'])
        transaction.on_commit(
            lambda: update_complaint_cost.delay(complaint_id=complaint.id)
        )

    @classmethod
    def create_batch_with_products(
        cls,
        manufactor: Manufactor,
        products: Iterable[Product],
        batch_type: BatchType = BatchType.STANDARD,
        batch_status: BatchStatus = BatchStatus.NEW,
    ) -> ProductBatch:
        if isinstance(products[0], ProductComplaint):
            batch_type = BatchType.COMPLAINTS
        batch = ProductBatch.objects.create(
            manufactor=manufactor,
            material_description=cls._get_material_description(products),
            product_type=cls._get_product_type(products),
            batch_type=batch_type,
            status=batch_status,
        )
        Product.objects.filter(id__in=[p.id for p in products]).update(
            batch=batch, manufactor=manufactor
        )
        return batch

    def update_batch_related_objects(self, batch: ProductBatch) -> None:
        products = batch.batch_items.all()
        self.update_products_serialization_and_files(products, batch.created_at)
        self._update_order_notes(products)
        transaction.on_commit(lambda: batch.generate_all_files())
        if batch.actions_needed == BatchNeededActions.FIX_ERRORS:
            from producers.tasks import slack_notify_about_fix_errors

            slack_notify_about_fix_errors.delay(batch.id)

    @staticmethod
    def validate_aborted_products(products) -> None:
        aborted_products = [
            str(p.id) for p in products if p.status == ProductStatus.ABORTED
        ]
        if aborted_products:
            aborted_products_ids = ', '.join(aborted_products)
            raise CannotBatchAbortedProducts(
                f'You cannot batch aborted products. '
                f'Aborted products: {aborted_products_ids}.'
            )

    @staticmethod
    def _update_order_notes(products: Iterable[Product]) -> None:
        orders = set(map(attrgetter('order'), products))
        for order in orders:
            order.update_order_notes_production_mix()

    @staticmethod
    def _get_material_description(products: Iterable[Product]) -> str:
        product = products[0]  # only sotty can have batch from more than one material
        if product.is_sotty:
            material_names = set(  # noqa: C403
                [
                    m
                    for p in products
                    for m in p.details.get_material_name(get_for_batch=True).split(', ')
                ]
            )
            return ', '.join(material_names)
        return product.details.get_material_name(get_for_batch=True)

    @staticmethod
    def _generate_files_for(
        product: Product, ps_client: ProductionSystemClient
    ) -> None:
        with product.using_ps_client(ps_client):
            # upsert when batching with manufacturer
            product.serialization_updater.update_product_serialization()
            product.details.generate_connections_zip()
            product.details.generate_front_view()
            if product.product_type == 'jetty':
                product.details.generate_packaging_instruction(force_refresh=True)
                # TODO: remove generate_connections_dxf form creating Product
                #  and recalculations, otherwise we assume it is needed
                product.details.generate_connections_dxf()
                if product.manufactor_id == Manufacturers.TELMEX:
                    # NOTE: TELMEX needs drawing for blendes
                    product.details.generate_production_drawings()
            elif product.product_type == 'watty':
                product.details.generate_packaging_instruction()
                product.details.generate_production_drawings()

    @staticmethod
    def _remove_old_files(product) -> None:
        removed_files = set()
        for field_name in product.details._file_fields:
            if field_name == 'instruction':
                continue
            field = getattr(product.details, field_name)
            if field and field.storage.exists(field.name):
                field.storage.delete(field.name)
                setattr(product.details, field_name, None)
                removed_files.add(field_name)
        product.details.save(update_fields=removed_files)
        for field_name in removed_files:
            product.details.process_file_not_needed(field_name)

    @staticmethod
    def _get_product_type(products: Iterable[Product]) -> str:
        product_types = [p.product_type for p in products]
        if len(set(product_types)) == 1:
            return product_types[0]
        else:
            logger.warning(
                (
                    'Batch with products %s with following types: %s. '
                    'Choosing %s as batch product type.'
                )
                % (
                    ', '.join([str(p.id) for p in products]),
                    ', '.join(set(product_types)),
                    product_types[-1],
                )
            )
            return product_types[-1]
