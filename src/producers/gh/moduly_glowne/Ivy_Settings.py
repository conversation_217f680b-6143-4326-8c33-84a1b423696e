"""Mo<PERSON><PERSON> z ustawieniami - zmiennymi.

- parametry i wagi graniczne paczek;
- parametry materialow i opakowan
- parametry wymiarow elementow
"""  # noqa: N999

from past.utils import old_div

# ________ Parametry dla rysunkow connections ___________________________

# wartosci do nawiertow
element_nawiert_srednica = 2.5
dia = 14  # glebokosc wiercenia?
dia_mpr = [12.1, 14, 13]  # [nogi1, nogi2, modeez]
wied_num_legs = 9  # srednica wiercenia nogi bpp
wied_num_legs_mpr = 8  # [nogi1, nogi2]
# type 01, type 02
wied_num_modeez = [5, 4, 4]  # srednica wiercenia modeez bpp 4
wied_num_modeez_mpr = [5, 5]
wied_num_back_pin = [6, 5, 5]  # srednica wiercenia plecy bpp 6
wied_num_back_pin_mpr = [6, 6]
wied_num_bumper = [4.85, 4.85, 4.85]  # 3.7 nawiert pod odbojnik
# offsety w mm
vertical_offset_Y = [
    52.30,
    258.3,
]  # zmiana pod glebokosc 400, bylo [52.30, 61.70] 61.70 liczone w druga strone
support_distance_mid = 64  # zmiana (zatrzaski) 18.07.04 bylo 60
support_offset_Y = 0  # zmiana na 0 po liczeniu pozycji supportu z xyz domain
# odleglosc mocowania od krawedzi supportu dalszej od verticala (kolejno dol i gora)
support_offset_X = [
    30.50,
    30.50,
]  # zmiana (zatrzaski) 18.07.04 bylo [54.7, 46.7] zmiana 16.07.11 bylo: [36.70, 46.70]
# korekta y trzpienia blizszego do verticala aby wzmocnic osadzenie supportu 16.07.11
support_y_nudge = 0  # zmiana (zatrzaski) 18.07.04 bylo 1.5
# wartosci wymiarow dla rysunku zbiorczego connections
markers_length = 250  # dlugosc wasow
infobox_size = 245
text_frame_size_big = 75
text_frame_size_small = 45
frame_insert_X = 3000  # wsp X wstawienia ramek z opisem
frame = [
    {'insert': (frame_insert_X, 0), 'size': (infobox_size, infobox_size)},
    {
        'insert': (frame_insert_X + infobox_size, 0),
        'size': (infobox_size, infobox_size),
    },
    {
        'insert': (frame_insert_X + 2 * infobox_size, 0),
        'size': (infobox_size, infobox_size),
    },
]  # ramki na nr hori
text_hori_num_insert = (
    frame[0]['insert'][0] + old_div(infobox_size, 3),
    old_div(infobox_size, 4),
    text_frame_size_big,
)
text_hori_quantity_insert = (
    frame[1]['insert'][0] + old_div(infobox_size, 3),
    old_div(infobox_size, 4),
    text_frame_size_big,
)
text_prod_id_insert = (
    frame[2]['insert'][0] + old_div(infobox_size, 3) - 30,
    old_div(infobox_size, 4),
    text_frame_size_big,
)

# text opisu w frame
text_frame_descript = ('Element', 'Quantity', 'Shelf ID')
# pozycja tekstu w ramce
text_frame_descript_insert = (
    frame[0]['insert'][0] + 15,
    infobox_size - 45,
    text_frame_size_small,
    frame[1]['insert'][0] + 15,
    infobox_size - 45,
    text_frame_size_small,
    frame[2]['insert'][0] + 15,
    infobox_size - 45,
    text_frame_size_small,
)
text_hori_side = ('Bottom', 'Top')

text_fl = 'FL'

# NAWIERTY POD DRZWI
handle_gap = 3
bumper_offset = 30  # offset od krawedzi plyty
handle_narrow = 10  # dystans na ile przedluza sie plyta o uchwyt waski
handle_wide = 30  # dystans na ile przedluza sie plyta o uchwyt szeroki
# odbojniki pod drzwi
# type 01
bumper_wide_offset = (
    bumper_offset + handle_wide + handle_gap
)  # nawiert pod odbojnik przy szerokim uchwycie
bumper_narrow_offset = (
    bumper_offset + handle_narrow
)  # nawiert pod odbojnik przy waskim uchwycie
bumper_y_offset = [
    20.5,
    21.5,
]  # 19.5  # dystans nawiertu do krawedzi horizontala, stare/nowe wysokosci rzedow
# type 02
bumper_y_offset_type_02 = [23, 21.5]  # lewe / prawe drzwi (patrzac od frontu)
bumper_x_offset_type_02 = 68.5
#  forniry
bumper_y_offset_veneer = 21.5

# NAWIERTY POD PLECY
back_pin_x_offset = [50, 50, 50.04]
back_pin_y_offset = [
    [8, 8.5],
    [11],
    [11],
]  # [type 01 chipboard, type 01 pozostale] , type 02, fornir
back_pin_y_offset_chipboard = 8  # plecy z wiora, inna pozycja nawiertu

# NAWIERTY PO LAMELLO MEBLEPL
lamello_mpr = [
    7.5,
    94,
]  # dystans w x, y osi nawiertu lamello od krawedzi horizontala
# (wiercenie pionowe), os kluczyka

# _______ Nazwy elementow ________________________________________
# Do nazwy A, B, C... koncowki _s, _d, _b
# sa dodawane w klasach elementow w set_surname() !!
nazwy_elementow = {
    'V': [
        (190, 'A'),
        (260, 'B'),
        (380, 'C'),
        (398, 'D'),
        (468, 'E'),
        (538, 'F'),
        (588, 'G'),
        (658, 'H'),
        (778, 'J'),
    ],
    'S': [
        (190, 'As'),
        (260, 'Bs'),
        (380, 'Cs'),
        (398, 'Ds'),
        (468, 'Es'),
        (538, 'Fs'),
        (588, 'Gs'),
        (658, 'Hs'),
        (778, 'Js'),
    ],
}

nazwy_elementow_custom = {
    'V': ['K', 'L', 'M', 'N', 'O', 'P', 'R', 'S', 'T', 'U', 'V'],
    'S': ['Ks', 'Ls', 'Ms', 'Ns', 'Os', 'Ps', 'Rs', 'Ss', 'Ts', 'Us', 'Vs'],
}


# _______ Etykiety elementow _________________________________________________
# szablony do etykiet, wartosci w pixelach w domenie A4 300 DPI
# (38.065, 33.937) # pkt lewy dolny wstawienia pierwszej etykiety, pierwszy pkt siatki
label_XY = (
    80,
    182,
)
label_grid_distance = (779, 450)  # (65.934, 38.135) # dystans X i Y etykiet na siatce
label_size_WH = (750, 450)  # (63.5, 38.1)
label_grid = []

for j in range(3):  # na A4 miesci sie 7 rzedow po 3 etykiety
    for i in range(7):
        label_grid.append(  # noqa: PERF401
            (
                label_XY[0] + j * label_grid_distance[0],
                label_XY[1] + i * label_grid_distance[1],
            )
        )

label_template_V = 'label_template_V.svg'
label_template_H = 'label_template_H.svg'
label_template_S = 'label_template_S.svg'
labels_matrice = ''
# polozenie tekstow w domenie labelki label_size_WH
label_text_big_XY = {
    'H': (75, 260),  # (31, 196) (2.6, 16.6),
    'V': (100, 50),  # (111, 0) (9.4, -0.7),
    'S': (75, 50),  # (48, 0) (4.1, -0.7)
}
label_text_small_XY = {
    'H': (650, 265),  # (635, 261) (53.8, 22.1),
    'V': (650, 85),  # (635, 74) (53.8, 6.3),
    'S': (650, 85),  # (635,74) (53.8, 6.3)
}

label_text_big_size = 200  # 667 # 56.5
label_text_small_size = 30  # 77 # 6.5
label_font = 'Lettera Text Pro'

# etykiety na paczki
package_label_info = 'Shelf ID:'
package_label_xy = (
    old_div(label_size_WH[0], 6),
    old_div(label_size_WH[1], 2),
)  # - label_text_big_size / 4)


# _______ Etykiety paczek _________________________________________________
# ____________ INSTRUKCJA _________________________________________________

'''
ustawienia parametrow do rysowania instrukcji
'''
# kat nachylenia bazowej osi horizontala do osi w widoku izometrycznym z
# instrukcji w GH3, w radianach
alpha = 0.279253
beta = 0.383972
gamma = 1.57079633  # 90 stopni
iso_angles = (alpha, beta, gamma)

# wymiary do explodowanych rysunkow
elem_explode = {'D': 400, 'T': 400, 'I': 350}
module_explode = 125
backs_assembly = 15
backs_assembly_angle = -0.879253
doors_assembly_angle = 0  # 0.279253  # -2.1
# cliparty do rys szafki w svg
clipart_base = [(0, 0), (1, 0), (1, 1), (0, 1)]  # podstawowy clipart do transformacji
clipart_front = []  # clipart na przod szafki, krawedz boczna vert i hori, przod supp
clipart_side = []  # na bok vert, hori i supp
clipart_top = []  # na gore vert, hori i supp

# rozmiar strony instrukcji
page_size = (2970, 2100)  # A4
front_viewbox = (
    1500.0,
    1400.0,
)  # rozmiar obszaru w jakim rysuje sie szafka na stronie front
# tag_text_size_small = 50
tag_text_size_small = 28
tag_text_size_medium = tag_text_size_small * 1.3

tag_circle_radius_small = tag_text_size_small * 1.0
tag_circle_radius_medium = tag_text_size_medium * 1.1
tag_offset = 350
# ustawienia dla rysunkow

drawing_x_margin = old_div(page_size[0], 20)  # odstep rysunkow od krawedzi strony

# ustawienia dla tekstu
text_regular_size = 55
text_header1_size = 55
text_header2_size = 65
text_title_size = 95
text_font = 'Px Grotesk'
text_style = 'font-family: PxGrotesk-RegularIta'
text_leading = round(text_regular_size * 1.20)  # dystans lini tekstu w y

text_offset = (
    0,
    tag_circle_radius_medium * 0.35,
)  # offset tekstu na potrzeby LATEXU, przesuniecie tekstu w tagach kolek
header_insert = (150, 150)
# offset tekstu 'x1' itp od srodka tagu z nazwa elementu, zeby byl niezalezny od skali
additional_text_offset = (
    10,
    0,
)
# kolory dla elementow
color_standard = 'rgb(0,0,100)'
colors = ['rgb(150,100,0)', 'rgb(150,0,150)', 'rgb(0,200,100)']
color_selected = 'lightgrey'
elements_colors = {
    'A': color_selected,
    'B': color_selected,
    'C': color_selected,
    'As': color_selected,
    'Bs': color_selected,
    'Cs': color_selected,
}

# stojaca
# 0.279253 Y
# 0.383972 X

# lezaca
# 0.5 * Pi X
# 0.279253 Y
# 0.383972 X

# DOORS settings

# offset drzwi od verticali na rysunku frontu, nie produkcyjny dystans, wizualny offset
doors_front_offset = 5
doors_instruction_offset = doors_front_offset
doors_handle_width = handle_wide
doors_handle_offset = 15
doors_handle_length_type02 = 160

# BOLCE
pin_offset = 50  # offset bolcow w rzucie od krawedzi horizontala
pin_length = 100  # dlugosc bolca
# ustawienia skali rysunkow w instrukcji
scale_parts = 4.0

# scales settings in CM for manual drawings
scales = [
    dict(scale=1.5, name='sc1', width=list(range(150)), height=list(range(150))),  # noqa: C408
    dict(  # noqa: C408
        scale=1.95, name='sc2', width=list(range(150)), height=list(range(150, 240))
    ),
    dict(  # noqa: C408
        scale=2.0, name='sc3', width=list(range(150)), height=list(range(240, 320))
    ),
    dict(  # noqa: C408
        scale=2.25, name='sc4', width=list(range(150, 241)), height=list(range(150))
    ),
    dict(  # noqa: C408
        scale=2.25,
        name='sc5',
        width=list(range(150, 241)),
        height=list(range(150, 240)),
    ),
    dict(  # noqa: C408
        scale=2.25,
        name='sc6',
        width=list(range(150, 241)),
        height=list(range(240, 320)),
    ),
    dict(  # noqa: C408
        scale=2.5, name='sc7', width=list(range(241, 300)), height=list(range(150))
    ),
    dict(  # noqa: C408
        scale=2.5, name='sc8', width=list(range(241, 300)), height=list(range(150, 240))
    ),
    dict(  # noqa: C408
        scale=2.5, name='sc9', width=list(range(241, 300)), height=list(range(240, 320))
    ),
    dict(  # noqa: C408
        scale=3.00,
        name='sc10',
        width=list(range(300, 1000)),
        height=list(range(150)),
    ),
    dict(  # noqa: C408
        scale=3.00,
        name='sc11',
        width=list(range(300, 1000)),
        height=list(range(150, 240)),
    ),
    dict(  # noqa: C408
        scale=3.00,
        name='sc12',
        width=list(range(300, 1000)),
        height=list(range(240, 320)),
    ),
    dict(  # noqa: C408
        scale=3.00,
        name='sc13',
        width=list(range(1000)),
        height=list(range(320, 1000)),
    ),
]  # very high shelf

scales_other = [
    dict(scale=1.10, name='sc1', width=list(range(150)), height=list(range(150))),  # noqa: C408
    dict(  # noqa: C408
        scale=2.0, name='sc2', width=list(range(150)), height=list(range(150, 240))
    ),
    dict(  # noqa: C408
        scale=2.2, name='sc3', width=list(range(150)), height=list(range(240, 320))
    ),
    dict(  # noqa: C408
        scale=1.9, name='sc4', width=list(range(150, 241)), height=list(range(150))
    ),
    dict(  # noqa: C408
        scale=2.15,
        name='sc5',
        width=list(range(150, 241)),
        height=list(range(150, 240)),
    ),
    dict(  # noqa: C408
        scale=2.3, name='sc6', width=list(range(150, 241)), height=list(range(240, 320))
    ),
    dict(  # noqa: C408
        scale=2.75, name='sc7', width=list(range(241, 1000)), height=list(range(150))
    ),
    dict(  # noqa: C408
        scale=2.75,
        name='sc8',
        width=list(range(241, 1000)),
        height=list(range(150, 240)),
    ),
    dict(  # noqa: C408
        scale=2.75,
        name='sc9',
        width=list(range(241, 1000)),
        height=list(range(240, 320)),
    ),
    dict(  # noqa: C408
        scale=3.00,
        name='sc10',
        width=list(range(1000)),
        height=list(range(320, 1000)),
    ),
]  # very high shelf


'''
ustawienia do rysowania labelek na paczki
'''
x_offset = 100
text_insert = (x_offset, 350)
text_label_leading = 35
text_lab_regular_size = 50
text_lab_title_size = 50
text_box_insert = (725, 140)
text_address_insert = (x_offset, 650)
const_y_offset = 75  # 'name:' itp stale teksty w labelce offset w y

id_insert = (x_offset, 1425)
cells_div = [200, 500, 1250]  # wsp y lini dzielacych pola labelki
box_label_size = (1020, 1520)


# working HEX color codes in svglib http://www.december.com/html/spec/colorsvghex.html

# etykiety tylko z nr paczek, na paczki
package_label_nr_size = (1500, 1000)
package_label_nr_radius = 180  # 255
package_label_tag_font_size = package_label_nr_radius * 0.85
package_label_nr_insert = (
    package_label_nr_size[0] * 0.7,
    package_label_nr_size[1] * 0.2,
)

# etykiety na owijki do paczek szuflad
drawer_wrapper_label_size = (1000, 1500)

meblepl_boards_codenames = {
    'material_chipboard_melamine-snow-white_18': 'EDITO_P18ST41MEB11.X8685T.STSM',
    'material_chipboard_melamine-ceramic-red_18': 'EDITO_P18ST41MEB11.WK098.STSU',
    'material_chipboard_melamine-indigo-blue_18': 'EDITO_P18ST41MEB11.U599.ST9',
    'material_chipboard_melamine-sand-beige_18': 'P18ST41MEB11.U156.ST9',
    'material_chipboard_melamine-pastel-green_18': 'EDITO_P18ST41MEB11.W7063.STSU',
    'material_chipboard_melamine-black-mat_18': 'EDITO_P18.1ST101MEB11.U7322T.VELVET.F',
    'material_plywood_clear-overlay_12': 'EDITO_S12ST02BRZ22.ME.SW',
    'material_chipboard_melamine-burgundy_18': 'EDITO_P18ST41MEB11.U399T.ST9',
    'material_chipboard_melamine-blue_18': 'EDITO_P18ST41MEB11.U0121T.BS.KRO',
    'material_chipboard_melamine-cotton_18': 'EDITO_P18ST41MEB11.U113T.ST9',
    'material_chipboard_melamine-black_12': 'EDITO_P12ST41MEB11.U999T.ST9',
    'material_chipboard_melamine-indigo-blue_12': 'EDITO_P12ST41MEB11.U599T.ST9',
    'material_chipboard_melamine-blue_12': 'EDITO_P12ST41MEB11.U540.ST9',
    'material_chipboard_melamine-black-mat_19': 'EDITO_P19ST101MEB11.U7322T.VELVET.F',
}
meblepl_banding_codenames = {
    'material_chipboard_melamine-snow-white_18': (
        'EDITO_O0.8WD92ABS11.W101000T.PEXG.R',
        'EDITO_O0.8WD92ABS11.W101000T.PEXG.R',
    ),
    'material_chipboard_melamine-ceramic-red_18': (
        'O0.8WD92ABS11.U140704.STEM.R',
        'O0.8WD92ABS11.U140704.STEM.R',
    ),
    'material_chipboard_melamine-indigo-blue_18': (
        'O0.8WD92ABS11.U599.ST9.E',
        'O0.8WD92ABS11.U599.ST9.E',
    ),
    'material_chipboard_melamine-sand-beige_18': (
        'O0.8WD92ABS11.U599.ST9.E',
        'O0.8WD92ABS11.U156.ST9.E',
    ),
    'material_chipboard_melamine-pastel-green_18': (
        'O0.8WD92ABS11.U140502.STEM.R',
        'EDITO_O1WD94ABS11.X167063.STSM.R',
    ),
    'material_chipboard_melamine-black-mat_18': (
        'EDITO_O1WD94ABS11.U190720T.STHU.F',
        'EDITO_O1WD94ABS11.U190720T.STHU.F',
    ),
    'material_chipboard_melamine-burgundy_18': (
        'EDITO_O0.8WD92ABS11.U72781T.ST9.R',
        'EDITO_O0.8WD92ABS11.U72781T.ST9.R',
    ),
    'material_chipboard_melamine-blue_18': (
        'EDITO_O0.8WD92ABS11.U17845T.STBS.R',
        'EDITO_O0.8WD92ABS11.U17845T.STBS.R',
    ),
    'material_chipboard_melamine-cotton_18': (
        'EDITO_O0.8WD92ABS11.U77033T.ST9.R',
        'EDITO_O0.8WD92ABS11.U77033T.ST9.R',
    ),
    'material_chipboard_melamine-black-mat_19': (
        'EDITO_O1WD94ABS11.U190720T.STHU.F',
        'EDITO_O1WD94ABS11.U190720T.STHU.F',
    ),
}

meblepl_drawer_banding_codenames = {
    'material_ABS_black_16-08': (
        'EDITO_O0.8WD246ABS11.U98522T.ST9.R',
        'EDITO_O0.8WD246ABS11.U98522T.ST9.R',
    ),
    'material_ABS_indigo-blue_16-08': (
        'EDITO_O0.8WD246ABS11.U71213T.ST9.R',
        'EDITO_O0.8WD246ABS11.U71213T.ST9.R',
    ),
    'material_ABS_blue_16-08': (
        'EDITO_O0.8WD246ABS11.U17508T.ST9.R',
        'EDITO_O0.8WD246ABS11.U17508T.ST9.R',
    ),
}

material_type01_polish = {
    0: 'sklejka biala',
    1: 'sklejka czarna',
    2: 'klejonka',
    3: 'sklejka szara',
    4: 'sklejka oberzynowa',
    5: 'sklejka fornirowana',
    6: 'sklejka czerwona',
    7: 'sklejka zolta',
    8: 'sklejka rozowa',
    9: 'sklejka niebieska',
    10: 'sklejka ciemnobrazowa',
    11: 'Green agava - EGGER green agava U645 st9',
}
material_type02_polish = {
    0: 'Snow White - Kronospan 8685 BS',
    1: 'Ceramic Red - Kronospan K098 SU',
    2: 'Indigo Blue - Egger U599 ST9',
    3: 'Sand Beige - Egger U156 ST9',
    4: 'Pastel Green - Kronospan 7063 SU',
    6: 'Black mat - VELVET 7322',
    7: 'Sky Blue - Kronospan Capri Blue 0121 BS',
    8: 'Burgundy Red - EGGER Burgundy U399 ST9',
    9: 'Cotton Beige - EGGER Cotton U113 ST9',
    10: 'Gray - EGGER U773 ST9',
    11: 'Gray + Dark Gray - EGGER U773 ST9',
    12: 'Sand + Mustard Yellow - Egger U156 ST9',
    13: 'Forest Green',
    14: 'Lilac',
    15: 'Pink - Egger Różowy Flamingo U363 ST9',
    16: 'Sage Green - Egger Zielony Pistacja U608 ST9',
    17: 'Stone Gray - Egger Szary Kamienny U727 ST9',
    18: 'Stone Gray - Egger U727 ST9 + obrzeże orzechowe',
    19: 'Black - Egger U999 ST9',
}
material_type02_polish_short = {
    0: 'biale',
    1: 'czerwone',
    2: 'niebieskie',
    3: 'bezowe',
    4: 'zielone',
    6: 'czarne',
    7: 'jasnoniebieskie',
    8: 'burgundowe',
    9: 'cotton',
    10: 'szare',
    11: 'ciemnoszare',
    12: 'musztardowe',
    13: 'lesne',
    14: 'liliowe',
    15: 'rozowe',
    16: 'pistacjowe',
    17: 'kamienne szare',
    18: 'kamienne szare',
    19: 'czarne',
}

material_veneer_type01_polish = {
    0: 'Fornir Jesion',
    1: 'Fornir Dab',
    2: 'Orzech',
}

material_names_polish_by_shelf_type = {
    0: material_type01_polish,
    1: material_type02_polish,
    2: material_veneer_type01_polish,
}

material_names_polish_by_shelf_type_short = {
    0: material_type01_polish,
    1: material_type02_polish_short,
    2: material_veneer_type01_polish,
}

front_view_surname_text_size = 25
