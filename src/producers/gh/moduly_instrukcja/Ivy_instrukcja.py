"""Modu<PERSON> klas instruk<PERSON>, korzysta z modulow pages i z nich sklada json z danymi
do przeslania do latexu

NOTE:
- svg do wrzucania na konkretne strony maja juz w sobie cliparty, tzn circle i tekst
np jako oznaczenie elementu, oraz fille na wybranych elementach
-

"""  # noqa: N999

from importlib import reload

from django.utils.translation import gettext as _

from past.utils import old_div

from custom.enums import (
    LanguageEnum,
    PhysicalProductVersion,
    ShelfType,
)
from producers.gh.moduly_export import Ivy_export_SVG
from producers.gh.moduly_glowne import Ivy_Settings
from producers.gh.moduly_instrukcja.manual_manager import (
    ManualManager,
    VerticalSolo,
)
from producers.gh.moduly_instrukcja.qrcode_generator import get_qrcode_rectangles
from producers.gh.moduly_instrukcja.svg_render_from_template import render_svg

from . import Ivy_pages
from .enums import AssemblyStyle

reload(Ivy_pages)
reload(Ivy_export_SVG)
reload(Ivy_Settings)

# stara Instruction
SVG = Ivy_export_SVG.ExportSVG
MANUAL_VERSION = 'm1.5'


class Instrukcja(object):
    def __init__(
        self,
        ivy,
        user_name='-',
        page_size=(2970, 2100),
        language=LanguageEnum.EN,
    ):
        self.page_size = page_size
        self.prod_id = ivy.id_production  # NOTE: Do uzyskania poprzez ivy.id_production
        self.user_name = user_name
        self.language = language
        _dna = ['slant', 'gradient', 'pattern', 'grid', '']
        self.dna = _dna[
            min(4, ivy.gallery_parameters['pattern'])
        ]  # NOTE skip dna name if shelf is from Cape
        _material = [
            [
                _('manual_white_plywood'),
                _('manual_black_plywood'),
                _('manual_solid_sycamore'),
                _('manual_grey_plywood'),
                _('manual_aubergine_plywood'),
                _('manual_natural_plywood'),
                _('manual_classic_red'),
                _('manual_yellow'),
                _('manual_dusty_Pink'),
                _('manual_blue_plywood'),
                _('manual_dark_brown_plywood'),
                _('manual_green_agava_plywood'),
            ],
            [
                _('manual_white_particle_board'),
                _('manual_terracotta_particle_board'),
                _('manual_midnight_blue_particle_board'),
                _('manual_sand_midnight_blue_particle_board'),
                _('manual_mint_forest_green_particle_board'),
                '',
                _('manual_matte_black_particle_board'),
                _('manual_sky_blue_particle_board'),
                _('manual_burgundy_red_particle_board'),
                _('manual_cotton_beige_particle_board'),
                _('manual_grey'),
                _('manual_grey_dark_grey'),
                _('manual_sand_mustard_yellow'),
                '',
                '',
                _('manual_reisingers_pink_particle_board'),
                _('manual_sage_green_particle_board'),
                _('manual_stone_grey_particle_board'),
                _('manual_grey_walnut_particle_board'),
                _('manual_black_particle_board'),
            ],
            [
                _('manual_ash_veneer'),
                _('manual_oak_veneer'),
                _('manual_dark_oak_veneer'),
            ],
        ]

        self.material = _material[ivy.shelf_type][ivy.gallery_parameters['material']]
        self.ivy = ivy
        self.scale = 3 if ivy.width <= 2400 else 5

    def get_page(
        self,
        page_number=1,
        page_name='page',
        clipart_name=None,
        text_group=None,
        shelf_data=None,
    ):
        page = Ivy_pages.Page()
        page_name = Ivy_pages.Page.get_page_name(
            self.prod_id,
            page_name,
            page_number,
        )
        if shelf_data:  # add shelf svg standing or laying on the floor
            page_svg = page.get_page_svg(
                ivy=self.ivy,
                page_size=self.page_size,
                page_name=page_name,
                direction=shelf_data.get('direction', 0),
                elements_to_generate=shelf_data.get('element_filter', 'HVSDTLPB'),
                svg_name=shelf_data.get('svg_name', 'svg'),
                shelf_insert_in_page=shelf_data.get('insert', (0.7, 0.4)),
                module_filter=shelf_data.get('module_filter'),
                shelf_direction=shelf_data.get('shelf_direction', 'standing'),
            )
        else:  # if no shelf in drawing add basic svg structure for clipart
            page_svg = [
                page.get_base_svg(page_name=page_name, page_num=page_number),
            ]

        if text_group:
            page_svg[0]['graphic'] += text_group['graphic']

        clipart = [Ivy_pages.Page.get_page_image(clipart_name)]
        page_json = page.get_page_json(
            main_image_name=page_name, page_number=page_number, images=clipart
        )
        return page_svg, page_json

    def get_page_care(
        self,
        page_number=1,
        page_name='page',
        clipart_name=None,
    ):
        page = Ivy_pages.Page()
        page_name = Ivy_pages.Page.get_page_name(
            self.prod_id,
            page_name,
            page_number,
        )
        form_url = LanguageEnum(self.language).get_referral_form_url()
        qr_code_url_str = f'https://{form_url}?source=qrcode'
        qrcode = get_qrcode_rectangles(qr_code_url_str)

        svg_data = {
            'version': MANUAL_VERSION,
            'qrcode': qrcode,
            'url': form_url,
        }
        svg = render_svg(svg_data=svg_data, template_name='manual_last_page.svg')
        clipart = [Ivy_pages.Page.get_page_image(clipart_name)]
        page_json = page.get_page_json(
            main_image_name=page_name, page_number=page_number, images=clipart
        )
        return (svg, page_name), page_json

    def get_page_front(self, page_number=1):
        page_front = Ivy_pages.PageFront()
        page_front_name = Ivy_pages.Page.get_page_name(
            self.prod_id, 'front', page_number
        )
        page_front_svg = page_front.get_front_svg(
            self.ivy,
            self.prod_id,
            self.user_name,
            self.dna,
            self.material,
            page_size=self.page_size,
            svg_name=page_front_name,
        )
        page_front_json = page_front.get_page_json(
            main_image_name=page_front_name, page_number=page_number
        )
        return page_front_svg, page_front_json

    def get_page_assembly_tips(self, fixing, start_page, ivy_height):
        page_assembly_tips = Ivy_pages.Page()
        page_svg = []
        pages_names = []
        for i in range(2):
            _page_num = start_page + i
            _page_name = Ivy_pages.Page.get_page_name(
                self.prod_id, 'assembly_tips', _page_num
            )
            page_svg.append(
                page_assembly_tips.get_base_svg(
                    page_name=_page_name, page_num=_page_num
                )
            )
            pages_names.append(_page_name)
        clipart_1 = [Ivy_pages.Page.get_page_image('assemblyTips1')]
        page_json_1 = page_assembly_tips.get_page_json(
            main_image_name=pages_names[0], page_number=start_page, images=clipart_1
        )
        if fixing == 0 and ivy_height < 300:
            clipart_2 = [Ivy_pages.Page.get_page_image('assemblyTips2v02')]
        elif (
            ivy_height >= 300
        ):  # szafka wyzsza niz 300 cm, wex clipart z info o 4 osobach do skladania
            clipart_2 = [Ivy_pages.Page.get_page_image('assemblyTips2v03')]
        elif fixing > 0:
            clipart_2 = [Ivy_pages.Page.get_page_image('assemblyTips2v01')]

        page_json_2 = page_assembly_tips.get_page_json(
            main_image_name=pages_names[1], page_number=start_page + 1, images=clipart_2
        )
        page_json = [page_json_1, page_json_2]
        return page_svg, page_json

    def get_page_assembly_part(self, page_num, shelf_module, module_name):
        page_part = Ivy_pages.PagePart()
        page_part_name = Ivy_pages.Page.get_page_name(
            self.prod_id, 'part{}'.format(module_name), page_num
        )
        clipart = [Ivy_pages.Page.get_page_image('part')]
        page_part_svg = page_part.get_page_part_svg(
            self.ivy,
            page_size=self.page_size,
            page_name=page_part_name,
            shelf_module=shelf_module,
        )
        page_part_json = page_part.get_page_json(
            page_part_name, page_number=page_num, images=clipart
        )
        return page_part_svg, page_part_json

    def get_assembly_clipart_name(self, assembly_type, product_version):
        """
        Sprawdza jakie elementy sa w danym kroku assembly i przyporzadkowuje
        po nazwie odpowiedni clipart do wczytania
        :param assembly_type:
        :return:
        """
        clipart = None
        if assembly_type == ['H']:
            clipart = 'assemblyHorizontals'
        elif assembly_type == ['B', 'H', 'V']:
            clipart = 'assembly_charlie'
        elif assembly_type == ['V'] or ('V' in assembly_type and 'H' in assembly_type):
            clipart = 'assemblyVerticals'
        elif assembly_type == ['S']:
            clipart = 'assemblySupports'
            if product_version >= PhysicalProductVersion.BRONTO:
                clipart = 'assemblySupports_bronto'
        elif assembly_type == ['B']:
            if product_version == PhysicalProductVersion.TREX:
                clipart = 'assembly_backs'
            elif product_version >= PhysicalProductVersion.BRONTO:
                clipart = 'assembly_backs_bronto'
            else:
                # TODO distinguish between backs with and without grommet
                clipart = 'assembly_backs_sideboard'
        elif 'B' in assembly_type and 'S' in assembly_type:
            clipart = 'assembly_backs_supports'
            if product_version >= PhysicalProductVersion.BRONTO:
                clipart = 'assembly_backs_supports_bronto'
        elif assembly_type == 'assembly_horizontal_charlie':
            clipart = 'assembly_horizontal_charlie'
        return clipart

    def get_page_assembly(
        self,
        scale,
        start_page,
        module_index=0,
        shelf_module=None,
        modules_amount=1,
        long_legs_modules=False,
        has_plinth=False,
        is_charlie=False,
    ):
        pages_assembly_json = []
        pages_assembly_svg = []
        pages_assembly = Ivy_pages.PageAssembly()
        _page_num = pages_assembly.get_assembly_page_number(
            self.ivy,
            shelf_module,
        )
        # HACK gdy wiecej niz 2 moduly -1 od strony start dla modulu > 2
        _add_page = -1 if shelf_module > 2 else 0
        start_page += 1 * module_index + _add_page

        assembly_types = pages_assembly.get_assembly_types(
            self.ivy,
            shelf_module,
        )
        if modules_amount > 1:  # jesli szafka ma moduly to ma strony part
            _module_name = shelf_module
            _part_page_num = start_page
            page_part_svg, page_part_json = self.get_page_assembly_part(
                page_num=_part_page_num,
                shelf_module=shelf_module,
                module_name=_module_name,
            )
            pages_assembly_svg.append(page_part_svg)
            pages_assembly_json.append(page_part_json)
            start_page += 1  # +1 do numeracji stron bo jest strona PART

        else:
            _module_name = None

        if (
            self.ivy.physical_product_version != PhysicalProductVersion.TREX
            and shelf_module in long_legs_modules
        ):
            # add long legs assembly page
            page_legs_svg, page_legs_json = self.get_page_legs(
                start_page,
                shelf_module=shelf_module,
            )
            pages_assembly_svg.append(page_legs_svg)
            pages_assembly_json.append(page_legs_json)
            start_page += 1

        if (
            self.ivy.physical_product_version != PhysicalProductVersion.TREX
            and has_plinth
        ):
            # add plinth assembly page
            page_plinth_svg, page_plinth_json = self.get_pages_plinth(
                start_page,
                shelf_module=shelf_module,
            )
            pages_assembly_svg += page_plinth_svg
            pages_assembly_json += page_plinth_json
            start_page += len(page_plinth_json)

        pages_assembly_names = []
        page_offset = 0
        for page_index in range(_page_num):
            _ass_t = assembly_types[page_index]

            if is_charlie and page_index == 1:
                (
                    page_legs_charlie_svg,
                    page_legs_charlie_json,
                ) = self.get_pages_legs_charlie(
                    start_page + page_index,
                )
                pages_assembly_svg += page_legs_charlie_svg
                pages_assembly_json += page_legs_charlie_json
                page_offset = 2  # increment the start page
            # calculate _num after the check
            _num = start_page + page_index + page_offset

            if page_index == 1 and _ass_t == ['H'] and is_charlie:
                _ass_t = 'assembly_horizontal_charlie'
            _clipart = self.get_assembly_clipart_name(
                _ass_t,
                self.ivy.physical_product_version,
            )
            clipart = [Ivy_pages.Page.get_page_image(_clipart)]
            _page_name = Ivy_pages.Page.get_page_name(
                self.prod_id,
                'assembly{}'.format(module_index + 1),
                _num,
            )
            pages_assembly_names.append(_page_name)
            pages_assembly_json.append(
                pages_assembly.get_page_json(
                    main_image_name=_page_name,
                    page_number=_num,
                    images=clipart,
                )
            )
        pages_assembly_svg += pages_assembly.get_assembly_svg(
            self.ivy,
            scale=scale,
            page_size=self.page_size,
            start_page=start_page,
            page_names=pages_assembly_names,
            shelf_module=shelf_module,
            module_name=_module_name or 0,
            is_charlie=is_charlie,
        )
        return pages_assembly_svg, pages_assembly_json

    def get_page_raising(self, page_num, shelf_module, is_desk=False):
        page_raising = Ivy_pages.PageRaisingShelf()
        page_raising_name = Ivy_pages.Page.get_page_name(
            self.prod_id, 'raising', page_num
        )
        page_raising_svg = page_raising.get_raising_svg(
            self.ivy,
            page_size=self.page_size,
            page_name=page_raising_name,
            shelf_module=shelf_module,
        )
        clipart_name = 'raising_main_desk' if is_desk else 'raising_main'
        clipart = [Ivy_pages.Page.get_page_image(clipart_name)]
        page_raising_json = page_raising.get_page_json(
            page_raising_name, page_number=page_num, images=clipart
        )
        return page_raising_svg, page_raising_json

    def get_page_inserts(self, page_num):
        page_inserts = Ivy_pages.PageAddingInserts()
        page_amount = len(
            page_inserts.get_element_names_chunks(
                self.ivy,
                elem_type='I',
                chunks_capacity=7,
            )
        )
        names = []
        for p in range(page_amount):
            page_inserts_name = Ivy_pages.Page.get_page_name(
                self.prod_id,
                'inserts{}'.format(p + 1),
                page_number=page_num + p,
            )
            names.append(page_inserts_name)
        clipart = [Ivy_pages.Page.get_page_image('adding_inserts')]
        page_inserts_svg = page_inserts.get_inserts_svg(
            self.ivy, self.page_size, page_name=names
        )
        json = []
        for p in range(page_amount):
            page_inserts_name = Ivy_pages.Page.get_page_name(
                self.prod_id,
                'inserts{}'.format(p + 1),
                page_number=page_num + p,
            )
            page_inserts_json = page_inserts.get_page_json(
                page_inserts_name,
                page_number=page_num + p,
                images=clipart,
            )
            json.append(page_inserts_json)
        return page_inserts_svg, json

    def get_page_doors(self, page_num, has_high_doors):
        page_doors = Ivy_pages.PageAddingDoors()
        # fewer doors listed on left side because of overlap with asset when high doors
        doors_per_page = 6 if has_high_doors else 10
        page_amount = len(
            page_doors.get_element_names_chunks(
                self.ivy,
                elem_type='D',
                chunks_capacity=doors_per_page,
            )
        )
        names = []
        for p in range(page_amount):
            page_doors_name = Ivy_pages.Page.get_page_name(
                self.prod_id, 'doors{}'.format(p + 1), page_number=page_num + p
            )
            names.append(page_doors_name)
        clipart_name = 'adding_doors'
        if has_high_doors:
            clipart_name = 'adding_doors_high'
        clipart = [Ivy_pages.Page.get_page_image(clipart_name)]
        page_doors_svg = page_doors.get_doors_svg(
            self.ivy,
            self.page_size,
            page_name=names,
            doors_per_page=doors_per_page,
        )
        json = []
        for p in range(page_amount):
            page_doors_name = Ivy_pages.Page.get_page_name(
                self.prod_id, 'doors{}'.format(p + 1), page_number=page_num + p
            )
            page_doors_json = page_doors.get_page_json(
                page_doors_name, page_number=page_num + p, images=clipart
            )
            json.append(page_doors_json)
        return page_doors_svg, json

    def get_page_assembly_drawers(
        self, page_num, first_drawer, rows_type=None, shelf_type=None
    ):
        page_assembly_drawers = Ivy_pages.Page()
        drawer_type = ''
        if rows_type == 0 and shelf_type == 0:
            drawer_type = ''
        elif rows_type == 1 and shelf_type == 0:
            drawer_type = 'type01_'
        elif rows_type == 1 and shelf_type == 1:
            drawer_type = 'type02_'
        elif rows_type == 1 and shelf_type == 2:
            drawer_type = 'typeF1_'

        if 'A' in first_drawer:
            assembly_drawers = [f'assembly_drawers_{drawer_type}A']
        elif 'B' in first_drawer:
            assembly_drawers = [f'assembly_drawers_{drawer_type}B']
        elif 'C' in first_drawer:
            assembly_drawers = [f'assembly_drawers_{drawer_type}C']
        else:
            assembly_drawers = ['assembly_drawers']

        header_text = ''
        is_veneer_with_blende = (
            self.ivy.physical_product_version >= PhysicalProductVersion.TRICE
            and shelf_type == 2
        )
        is_t01_unified_drawer = (
            self.ivy.physical_product_version >= PhysicalProductVersion.KARNO
            and shelf_type == 0
        )
        is_t02_unified_drawer = (
            self.ivy.physical_product_version >= PhysicalProductVersion.TRICE
            and shelf_type == 1
        )
        # temporarily (XDDDD) use F1 assets in unified drawers for all types
        if is_veneer_with_blende or is_t01_unified_drawer or is_t02_unified_drawer:
            assembly_drawers = [
                'assembly_drawers_F1_trice1',
                'assembly_drawers_F1_trice2',
            ]
        elif (
            self.ivy.physical_product_version >= PhysicalProductVersion.PACHY
            and shelf_type == 2
        ):
            # temporarily (yeah, right xD) use t02 assets in veneers
            assembly_drawers = [
                'assembly_drawers_type02_stego1',
                'assembly_drawers_type02_stego2',
            ]
        elif (
            self.ivy.physical_product_version >= PhysicalProductVersion.STEGO
            and shelf_type == 1
        ):
            # chipboard drawers in type02, two pages
            assembly_drawers = [
                f'assembly_drawers_{drawer_type}stego1',
                f'assembly_drawers_{drawer_type}stego2',
            ]
        elif self.ivy.physical_product_version >= PhysicalProductVersion.PTERO:
            # ptero drawers have two assembly pages
            assembly_drawers = [
                f'assembly_drawers_{drawer_type}ptero1',
                f'assembly_drawers_{drawer_type}ptero2',
            ]

        elif self.ivy.physical_product_version >= PhysicalProductVersion.RAPTOR:
            # raptors drawers have one page
            assembly_drawers = [f'assembly_drawers_{drawer_type}sideboard']
            header_text = 'Drawer assembly'

        pages_assembly_svg = []
        pages_assembly_json = []
        for assembly in assembly_drawers:
            page_assembly_drawers_name = Ivy_pages.Page.get_page_name(
                self.prod_id,
                assembly,
                page_num,
            )
            page_assembly_drawers_svg = [
                page_assembly_drawers.get_base_svg(
                    page_name=page_assembly_drawers_name,
                    page_num=page_num,
                    header_text=header_text,
                )
            ]
            clipart = [Ivy_pages.Page.get_page_image(assembly)]
            page_assembly_drawers_json = page_assembly_drawers.get_page_json(
                main_image_name=page_assembly_drawers_name,
                page_number=page_num,
                images=clipart,
            )
            pages_assembly_svg.append(page_assembly_drawers_svg)
            pages_assembly_json.append(page_assembly_drawers_json)
            page_num += 1
        return pages_assembly_svg, pages_assembly_json

    def get_page_drawers(self, page_num, shelf_type=None):
        page_drawers = Ivy_pages.PageAddingDrawers()
        page_amount = len(page_drawers.get_drawers_names_chunks(self.ivy))
        names = []
        for p in range(page_amount):
            page_drawers_name = Ivy_pages.Page.get_page_name(
                self.prod_id, 'drawers{}'.format(p + 1), page_number=page_num + p
            )
            names.append(page_drawers_name)

        clipart = (
            [Ivy_pages.Page.get_page_image('adding_drawers')]
            if shelf_type in [0, 2]
            else [Ivy_pages.Page.get_page_image('adding_drawers_type02')]
        )
        page_drawers_svg = page_drawers.get_drawers_svg(
            self.ivy, self.page_size, page_name=names
        )
        json = []
        for p in range(page_amount):
            page_drawers_name = Ivy_pages.Page.get_page_name(
                self.prod_id, 'drawers{}'.format(p + 1), page_number=page_num + p
            )
            page_drawers_json = page_drawers.get_page_json(
                page_drawers_name, page_number=page_num + p, images=clipart
            )
            json.append(page_drawers_json)
        return page_drawers_svg, json

    def get_page_backs(self, page_num):
        page_backs = Ivy_pages.PageAddingBacks()
        page_backs_name = Ivy_pages.Page.get_page_name(
            self.prod_id, 'backs', page_number=page_num
        )
        if self.ivy.assembly_style == AssemblyStyle.DORIC:
            # if standart shelf backs added on a standing shelf after raising
            clipart_name = 'adding_backs'  # standart shelf, '
        else:
            # if sideboard+ backs added when shelf still on the floor as usual
            clipart_name = 'assembly_backs_sideboard'
        clipart = [Ivy_pages.Page.get_page_image(clipart_name)]
        page_backs_svg = page_backs.get_backs_svg(
            self.ivy, self.page_size, page_backs_name
        )
        page_backs_json = page_backs.get_page_json(
            page_backs_name, page_number=page_num, images=clipart
        )
        return page_backs_svg, page_backs_json

    def get_page_desk(
        self,
        page_num,
        elem_type='H',
        clipart_name='adding_desk_top',
    ):
        page_element = Ivy_pages.PageAddingDeskElement()
        page_element_name = Ivy_pages.Page.get_page_name(
            self.prod_id,
            f'{elem_type}_desk',
            page_number=page_num,
        )
        clipart = [Ivy_pages.Page.get_page_image(clipart_name)]
        page_element_svg = page_element.get_desk_element_svg(
            self.ivy,
            self.page_size,
            page_element_name,
            elem_type=elem_type,
        )
        page_element_json = page_element.get_page_json(
            page_element_name,
            page_number=page_num,
            images=clipart,
        )
        return page_element_svg, page_element_json

    def get_page_element_without_module(
        self,
        page_num,
        elem_type='L',
        clipart_name='adding_legs',
        header_text='Adding legs',
    ):
        page_element = Ivy_pages.PageAddingElementWithoutModule()
        page_element_name = Ivy_pages.Page.get_page_name(
            self.prod_id,
            'legs',
            page_number=page_num,
        )
        clipart = [Ivy_pages.Page.get_page_image(clipart_name)]
        page_element_svg = page_element.get_last_element_svg(
            self.ivy,
            self.page_size,
            page_element_name,
            elem_type=elem_type,
            header_text=header_text,
        )
        page_element_json = page_element.get_page_json(
            page_element_name,
            page_number=page_num,
            images=clipart,
        )
        return page_element_svg, page_element_json

    def get_page_supports(self, page_num):
        # TODO switch to get_page_element_without_module()
        page_supports = Ivy_pages.PageAddingSupports()
        page_supports_name = Ivy_pages.Page.get_page_name(
            self.prod_id, 'supports', page_number=page_num
        )
        clipart = [Ivy_pages.Page.get_page_image('adding_supports')]
        page_supports_svg = page_supports.get_supports_svg(
            self.ivy, self.page_size, page_supports_name
        )
        page_supports_json = page_supports.get_page_json(
            page_supports_name, page_number=page_num, images=clipart
        )
        return page_supports_svg, page_supports_json

    def get_page_legs(self, page_num, shelf_module=1):
        page_legs = Ivy_pages.PageAddingBase()
        page_legs_name = Ivy_pages.Page.get_page_name(
            self.prod_id, 'legs', page_number=page_num
        )
        clipart = [Ivy_pages.Page.get_page_image('adding_legs')]
        page_legs_svg = page_legs.get_legs_svg(
            self.ivy, self.page_size, page_legs_name, shelf_module=shelf_module
        )
        page_legs_json = page_legs.get_page_json(
            page_legs_name, page_number=page_num, images=clipart
        )
        return page_legs_svg, page_legs_json

    def get_pages_legs_charlie(self, page_num):
        page = Ivy_pages.Page()
        page_legs_name1 = Ivy_pages.Page.get_page_name(
            self.prod_id, 'assembly_charlie_leg1', page_number=page_num
        )
        page_legs_name2 = Ivy_pages.Page.get_page_name(
            self.prod_id, 'assembly_charlie_leg1', page_number=page_num + 1
        )
        clipart1 = [Ivy_pages.Page.get_page_image('assembly_charlie_leg1')]
        clipart2 = [Ivy_pages.Page.get_page_image('assembly_charlie_leg2')]
        page_legs_svg1 = page.get_base_svg(page_name=page_legs_name1, page_num=page_num)
        page_legs_svg2 = page.get_base_svg(
            page_name=page_legs_name2, page_num=page_num + 1
        )
        page_legs_json1 = page.get_page_json(
            page_legs_name1, page_number=page_num, images=clipart1
        )
        page_legs_json2 = page.get_page_json(
            page_legs_name2, page_number=page_num + 1, images=clipart2
        )
        return [[page_legs_svg1, page_legs_svg2], [page_legs_json1, page_legs_json2]]

    def get_pages_plinth(self, page_number, shelf_module=1):
        page_plinth = Ivy_pages.PageAddingBase()
        return page_plinth.get_plinth_svg_and_clipart(
            self.ivy,
            self.page_size,
            page_number=page_number,
            shelf_module=shelf_module,
            prod_id=self.prod_id,
        )

    def get_page_joints(self, page_num):
        page_connecting = Ivy_pages.PageJoints()
        page_connecting_name = Ivy_pages.Page.get_page_name(
            self.prod_id, 'joints', page_num
        )
        page_connecting_svg = page_connecting.get_joints_svg(
            self.ivy, page_size=self.page_size, page_name=page_connecting_name
        )
        if self.ivy.assembly_style == AssemblyStyle.DORIC:
            clipart_name = 'inserting_pins'
        else:
            _depth = '_240_320' if self.ivy.depth_mm < 400 else ''
            clipart_name = f'inserting_pins_sideboard{_depth}'
        clipart = [Ivy_pages.Page.get_page_image(clipart_name)]
        page_connecting_json = page_connecting.get_page_json(
            page_connecting_name, page_number=page_num, images=clipart
        )
        return page_connecting_svg, page_connecting_json

    def get_page_connecting(self, page_num):
        page_connecting = Ivy_pages.PageConnectingParts()
        page_connecting_name = Ivy_pages.Page.get_page_name(
            self.prod_id, 'connecting', page_num
        )
        page_connecting_svg = page_connecting.get_connecting_svg(
            self.ivy, page_size=self.page_size, page_name=page_connecting_name
        )

        if self.ivy.assembly_style == AssemblyStyle.DORIC:
            clipart_name = 'connecting'
        else:
            _depth = '_240_320' if self.ivy.depth_mm < 400 else ''
            clipart_name = f'connecting_sideboard{_depth}'
        clipart = [Ivy_pages.Page.get_page_image(clipart_name)]
        page_connecting_json = page_connecting.get_page_json(
            page_connecting_name, page_number=page_num, images=clipart
        )
        return page_connecting_svg, page_connecting_json

    def get_page_fixing(self, page_num):
        cliparts = []
        page_fixing = Ivy_pages.PageFixingTheShelf()
        page_fixing_name = Ivy_pages.Page.get_page_name(
            self.prod_id, 'fixing', page_num
        )
        page_fixing_svg = page_fixing.get_fixing_svg(
            self.ivy, page_size=self.page_size, page_name=page_fixing_name
        )
        raising_svg_data = page_fixing.get_fixing_svg_data(
            self.ivy, page_size=self.page_size
        )
        has_new_fixing = page_fixing.has_sideboard_type_fixing(self.ivy)

        # TODO: Drawing new fixings should work with this flow but for some reason they
        #       aren't properly aligned so we skip drawing them until it is resolved
        if not has_new_fixing:
            _f = [
                x
                for x in raising_svg_data[0]['graphic'][2:]
                if x['group_data']['id'] == 'fixing'
            ]
            fix = [x['center'] for x in _f[0]['circle']]
            _name = (
                'fixingTop'
                if _f[0]['group_data']['fixing-side'] == 'top'
                else 'fixingBottom'
            )
            for pt in fix:
                clip = Ivy_pages.Page.get_page_image(_name, image_insert=pt)
                cliparts.append(clip)

        if not has_new_fixing:
            _name_fix = (
                'assemblyFixing'
                if _f[0]['group_data']['fixing-side'] == 'top'
                else 'assemblyFixing_down'
            )
            clip_fix = Ivy_pages.Page.get_page_image(_name_fix)
            cliparts.append(clip_fix)

        fixing_key = 'fixing_main_sideboard' if has_new_fixing else 'fixing_main'
        cliparts += [Ivy_pages.Page.get_page_image(fixing_key)]
        page_fixing_json = page_fixing.get_page_json(
            page_fixing_name, page_number=page_num, images=cliparts
        )
        return page_fixing_svg, page_fixing_json

    def get_shelf_flow_data(self):
        elements = self.ivy.get_elements(only_elements_to_be_produced=False)
        fixing = sum(
            [  # noqa: C419
                len(x.additional['wall_fittings']['points'])
                if 'wall_fittings' in x.additional
                else 0
                for x in elements['H']
            ]
        )
        # shelf modules that have long legs
        long_legs_modules = {
            leg.module for leg in elements['L'] if leg.additional['elem_type'] == 'Ll'
        }
        has_plinth = 'P' in elements
        has_high_doors = False
        for door in elements.get('D', []):
            if door.additional['height_name'] not in 'ABC':
                has_high_doors = True
                break

        _modules = list(self.ivy.get_modules())
        modules = _modules[1:] if 0 in _modules else _modules

        module_zero = [
            f"{x.ELEM_TYPE}{x.subtype or ''}"
            for x in elements.get('S', [])
            + elements.get('B', [])
            + elements.get('L', [])
            + elements.get('P', [])
            if x.module == 0
        ]
        # charlie has legs base instead of first row
        is_charlie = any(element.subtype == 's' for element in elements.get('B', []))
        # desk furniture has special back element under desk top
        is_desk = any(element.subtype == 'd' for element in elements.get('B', []))
        desk_solo_vertical = VerticalSolo.DOES_NOT_EXIST
        solo_vertical_element = [elem for elem in elements['V'] if elem.subtype == 'd']
        if solo_vertical_element:
            if solo_vertical_element[0].x1 == 0:  # axis mm from shelf start
                desk_solo_vertical = VerticalSolo.LEFT
            else:
                desk_solo_vertical = VerticalSolo.RIGHT

        try:
            fixing_raptor = 'wall_fitting' in [
                fitting.get('name')
                for horizontal in elements['H']
                for fitting in horizontal.fittings
            ]
        except AttributeError:
            # in old shelves fittings in horizontals are strings, not dicts
            # todo: do it nicer, maybe check if the shelf is a RAPTOR first
            fixing_raptor = False

        has_hafele_hinges = (
            (
                self.ivy.shelf_type == ShelfType.VENEER_TYPE01
                and self.ivy.physical_product_version >= PhysicalProductVersion.BRACHIO
            )
            or (
                self.ivy.shelf_type == ShelfType.TYPE01
                and self.ivy.physical_product_version >= PhysicalProductVersion.TRICE
            )
            or (
                self.ivy.shelf_type == ShelfType.TYPE02
                and self.ivy.physical_product_version >= PhysicalProductVersion.PACHY
            )
        )

        return ManualManager(
            has_doors='D' in elements,
            has_drawers='T' in elements,
            has_inserts='I' in elements,
            has_hafele_hinges=has_hafele_hinges,
            module_zero=module_zero,
            modules=modules,
            modules_amount=len(modules),
            rows_type=self.ivy.serialized_ivy.get('rows_type', 0),
            shelf_type=self.ivy.shelf_type,
            fixing=fixing,
            fixing_raptor=fixing_raptor,
            long_legs_modules=long_legs_modules,
            has_plinth=has_plinth,
            assembly_style=self.ivy.assembly_style,
            has_high_doors=has_high_doors,
            is_desk=is_desk,
            desk_solo_vertical=desk_solo_vertical,
            is_charlie=is_charlie,
        )

    def gen_pages(self):
        # TODO this is a nightmare def, too loong, split into smaller defs and
        # refactor appending svg and json logic
        pages = {'svg': [], 'json': []}
        pages_svg = []
        ivy = self.ivy
        product_version = ivy.physical_product_version
        shelf_flow_data = self.get_shelf_flow_data()
        # 1. "Front"
        page_front_svg, page_front_json = self.get_page_front()
        pages_svg.append(page_front_svg)
        # 3. "Assembly tips"
        _page = len(pages_svg) + 1

        page_assembly_tips = self.get_page_assembly_tips(
            shelf_flow_data.fixing or shelf_flow_data.fixing_raptor,
            _page,
            ivy_height=old_div(ivy.height, 1000),
        )
        page_assembly_tips_svg, page_assembly_tips_json = page_assembly_tips
        pages_svg += page_assembly_tips_svg

        # 4. "Assembly & Raising"
        _page += len(page_assembly_tips_svg)

        page_adjust_feet_json = None
        if (
            not shelf_flow_data.has_plinth
            and not shelf_flow_data.is_desk
            and not shelf_flow_data.long_legs_modules
        ):
            # add page with feet adjustments info
            # _page += 1
            page_adjust_feet_svg, page_adjust_feet_json = self.get_page(
                page_number=_page,
                page_name='adjust_feet',
                clipart_name='adjust_feet',
            )
            pages_svg += page_adjust_feet_svg
            _page = len(pages_svg) + 1

        pages_assembly_json = []
        for m, module in enumerate(shelf_flow_data.modules):
            # 4.1 "Assembly" assembly steps
            pages_assembly_svg, page_assembly_json = self.get_page_assembly(
                1,
                _page,
                m,
                module,
                shelf_flow_data.modules_amount,
                shelf_flow_data.long_legs_modules,
                shelf_flow_data.has_plinth,
                shelf_flow_data.is_charlie,
            )
            pages_svg += pages_assembly_svg
            pages_assembly_json += page_assembly_json
            _page = len(pages_svg) + 1

            # 4.2 "Raising" page raising added after every shelf module assembly
            if (
                shelf_flow_data.assembly_style == AssemblyStyle.DORIC
                and not shelf_flow_data.is_desk
            ):
                page_raising_svg, page_raising_json = self.get_page_raising(
                    _page,
                    module,
                    shelf_flow_data.is_desk,
                )
                pages_svg += page_raising_svg
                pages_assembly_json.append(page_raising_json)
        _page = pages_assembly_json[-1]['pageNumber']

        # 6. "Joints"
        # if shelf has modules, make joint page and connecting page
        page_joints_json = None
        if shelf_flow_data.modules_amount > 1 and not shelf_flow_data.is_desk:
            _page += 1
            page_joints_svg, page_joints_json = self.get_page_joints(_page)
            pages_svg += page_joints_svg
            # 7. "Connecting"
            _page += 1
            page_connecting_svg, page_connecting_json = self.get_page_connecting(_page)
            pages_svg += page_connecting_svg

        page_desk_horizontal_json = None
        if shelf_flow_data.is_desk:
            # position solo vertical
            if shelf_flow_data.desk_solo_vertical:
                _page += 1
                if shelf_flow_data.desk_solo_vertical == VerticalSolo.LEFT:
                    _clipart = 'adding_desk_vertical_left'
                else:
                    _clipart = 'adding_desk_vertical_right'
                (
                    page_desk_vertical_svg,
                    page_desk_vertical_json,
                ) = self.get_page_desk(
                    _page,
                    elem_type='V',
                    clipart_name=_clipart,
                )
                pages_svg += page_desk_vertical_svg
            # add desk top
            if shelf_flow_data.desk_solo_vertical:
                if shelf_flow_data.desk_solo_vertical == VerticalSolo.LEFT:
                    _clipart_name = 'adding_desk_top_and_vertical_left'
                else:
                    _clipart_name = 'adding_desk_top_and_vertical_right'

            else:
                _clipart_name = 'adding_desk_top'
            _page += 1
            (
                page_desk_horizontal_svg,
                page_desk_horizontal_json,
            ) = self.get_page_desk(
                _page,
                clipart_name=_clipart_name,
            )
            pages_svg += page_desk_horizontal_svg

            # desk back
            _page += 1
            (
                page_desk_back_svg,
                page_desk_back_json,
            ) = self.get_page_desk(
                _page,
                elem_type='B',
                clipart_name='adding_desk_back',
            )
            pages_svg += page_desk_back_svg

        # Multimodule elements, assembled at the end
        # Last pairs of legs or plinth
        page_last_legs_json = None
        if shelf_flow_data.modules_amount > 1 and (
            'L' in shelf_flow_data.module_zero or 'P' in shelf_flow_data.module_zero
        ):
            _page += 1
            base_elem_type = None
            # last part of plinth page
            if shelf_flow_data.has_plinth and 'P' in shelf_flow_data.module_zero:
                base_elem_type = 'P'
                header_text = _('manual_plinth')
                clipart_name = 'adding_last_plinth'
            # last legs page
            if shelf_flow_data.long_legs_modules and 'L' in shelf_flow_data.module_zero:
                base_elem_type = 'L'
                header_text = _('manual_adding_legs')
                clipart_name = 'adding_last_legs'
            if base_elem_type:
                (
                    page_last_legs_svg,
                    page_last_legs_json,
                ) = self.get_page_element_without_module(
                    _page,
                    elem_type=base_elem_type,
                    clipart_name=clipart_name,
                    header_text=header_text,
                )
                pages_svg += page_last_legs_svg
        page_supports_json = None
        if (
            shelf_flow_data.assembly_style == AssemblyStyle.DORIC
            and not shelf_flow_data.is_desk
        ):
            if 'B' in shelf_flow_data.module_zero:
                _page += 1
                page_backs_svg, page_backs_json = self.get_page_backs(_page)
                pages_svg += page_backs_svg

            # If 'support' element has to be assembled after raising the module
            if 'S' in shelf_flow_data.module_zero:
                _page += 1
                page_supports_svg, page_supports_json = self.get_page_supports(
                    _page,
                )
                pages_svg += page_supports_svg
        # add multimodule backs after slightly lifting shelf, before raising
        page_backs_json = None
        if 'B' in shelf_flow_data.module_zero and not shelf_flow_data.is_desk:
            _page += 1
            page_backs_svg, page_backs_json = self.get_page_backs(_page)
            pages_svg += page_backs_svg

        if (
            shelf_flow_data.assembly_style != AssemblyStyle.DORIC
            or shelf_flow_data.is_desk
        ):
            _page += 1
            page_raising_svg, page_raising_json = self.get_page_raising(
                _page,
                shelf_module=False,
                is_desk=shelf_flow_data.is_desk,
            )
            pages_svg += page_raising_svg

        # add desk backs after raising shelf
        # TODO: top level desk supports are never added, but perhaps we don't need it
        if 'B' in shelf_flow_data.module_zero and shelf_flow_data.is_desk:
            _page += 1
            page_backs_svg, page_backs_json = self.get_page_backs(_page)
            pages_svg += page_backs_svg

        # add page with elements adjustments tips info before doors/drawers/fixing
        _page += 1
        page_adjust_verticals_svg, page_adjust_verticals_json = self.get_page(
            page_number=_page,
            page_name='adjust_verticals',
            clipart_name='adjust_verticals',
        )
        pages_svg += page_adjust_verticals_svg

        # "Adding Inserts"
        page_inserts_json = None
        if shelf_flow_data.has_inserts:
            _page += 1
            page_inserts_svg, page_inserts_json = self.get_page_inserts(_page)
            pages_svg += page_inserts_svg
            _page += len(page_inserts_svg) - 1

        # "Adding Doors"
        page_doors_json = None
        if shelf_flow_data.has_doors:
            _page += 1
            page_doors_svg, page_doors_json = self.get_page_doors(
                _page,
                shelf_flow_data.has_high_doors,
            )
            pages_svg += page_doors_svg
            _page += len(page_doors_svg) - 1
            # "Adjusting doors"
            _page += 1
            if shelf_flow_data.has_hafele_hinges:
                page_adjusting_doors_svg, page_adjusting_doors_json = self.get_page(
                    page_number=_page,
                    page_name='adjusting_doors',
                    clipart_name='adjusting_hafele_hinges',
                )
            else:
                page_adjusting_doors_svg, page_adjusting_doors_json = self.get_page(
                    page_number=_page,
                    page_name='adjusting_doors',
                    clipart_name='adjusting_doors',
                )
            pages_svg += page_adjusting_doors_svg

        # "Adding Drawers"
        page_drawers_json = None
        if shelf_flow_data.has_drawers:
            _page += 1
            # first drawer name, to check height for picking
            # assembly clipart proper version
            first_drawer = [  # noqa: RUF015
                x
                for x in ivy.get_elements(
                    elements_to_generate='T',
                    get_flat_element_list=True,
                    only_elements_to_be_produced=False,
                )[0].components
                if x['type'] == 'T_side1'
            ][0]['surname']
            (
                pages_assembly_drawers_svg,
                pages_assembly_drawers_json,
            ) = self.get_page_assembly_drawers(
                _page,
                first_drawer=first_drawer,
                rows_type=shelf_flow_data.rows_type,
                shelf_type=shelf_flow_data.shelf_type,
            )
            for page_assembly_drawers_svg in pages_assembly_drawers_svg:
                pages_svg += page_assembly_drawers_svg
                _page += 1
            page_drawers_svg, page_drawers_json = self.get_page_drawers(
                _page, shelf_type=shelf_flow_data.shelf_type
            )
            pages_svg += page_drawers_svg
            _page += len(page_drawers_svg) - 1

            # add page with drawers adjustments tips
            map_type_suffix = {
                0: 'T01',
                1: 'T02',
                2: 'T01',
            }[shelf_flow_data.shelf_type]
            _page += 1
            page_adjust_drawers1_svg, page_adjust_drawers1_json = self.get_page(
                page_number=_page,
                page_name=f'adjust_drawers_{map_type_suffix}_1',
                clipart_name=f'adjust_drawers_{map_type_suffix}_1',
            )
            pages_svg += page_adjust_drawers1_svg

            _page += 1
            page_adjust_drawers2_svg, page_adjust_drawers2_json = self.get_page(
                page_number=_page,
                page_name=f'adjust_drawers_{map_type_suffix}_2',
                clipart_name=f'adjust_drawers_{map_type_suffix}_2',
            )
            pages_svg += page_adjust_drawers2_svg

            _page += 1
            page_level_drawers_svg, page_level_drawers_json = self.get_page(
                page_number=_page,
                page_name=f'level_drawers_{map_type_suffix}',
                clipart_name=f'level_drawers_{map_type_suffix}',
            )
            pages_svg += page_level_drawers_svg

        # "Fixing"
        # shelf has wall fixings, make fixing page (different for product types)
        page_fixing_json = []
        if (
            shelf_flow_data.fixing > 0
            and product_version == PhysicalProductVersion.TREX
        ):
            _page += 1
            page_fixing_svg, page_fixing_json = self.get_page_fixing(_page)
            pages_svg += page_fixing_svg
        elif (
            shelf_flow_data.fixing_raptor
            and product_version != PhysicalProductVersion.TREX
        ):
            _page += 1
            page_fixing_svg, page_fixing_json = self.get_page_fixing(_page)
            pages_svg += page_fixing_svg

        # Disassembly verticals
        if shelf_flow_data.has_doors:
            _page += 1
            (
                page_disassembly_hinges_verticals_svg,
                page_disassembly_hinges_verticals_json,
            ) = self.get_page(
                page_number=_page,
                page_name='disassembly_hinges_verticals',
                clipart_name='disassembly_hinges_verticals',
            )
            pages_svg += page_disassembly_hinges_verticals_svg

        if shelf_flow_data.has_drawers:
            _page += 1
            (
                page_disassembly_slides_verticals_svg,
                page_disassembly_slides_verticals_json,
            ) = self.get_page(
                page_number=_page,
                page_name='disassembly_slides_verticals',
                clipart_name='disassembly_slides_verticals',
            )
            pages_svg += page_disassembly_slides_verticals_svg

        # Disassembly backs
        pages_disassembly_backs_json = []
        backs_clipart_name = 'disassembly_backs_low'
        backs_page_index_max = 2
        if self.ivy.height > 135000:
            # there are 3 different pages for high shelves
            backs_clipart_name = 'disassembly_backs_high'
            backs_page_index_max = 3
        for backs_page_index in range(backs_page_index_max):
            _page += 1
            (
                page_disassembly_backs_svg,
                page_disassembly_backs_json,
            ) = self.get_page(
                page_number=_page,
                page_name=f'disassembly_backs_{backs_page_index + 1}',
                clipart_name=f'{backs_clipart_name}_{backs_page_index + 1}',
            )
            pages_svg += page_disassembly_backs_svg
            pages_disassembly_backs_json.append(page_disassembly_backs_json)

        # "Congratulations"
        _page += 1
        page_congratulations_svg, page_congratulations_json = self.get_page(
            page_number=_page,
            page_name='congratulations',
            clipart_name='congratulations3',
            shelf_data={
                'insert': (0.7, 0.5),
                'svg_name': 'cong_iso',
                'shelf_direction': 'standing',
            },
        )
        pages_svg += page_congratulations_svg
        _svg = []
        for index, page in enumerate(pages_svg):
            if index > 0:
                # except front page add page number to footer
                page[0]['graphic'] += [Ivy_pages.Page.get_page_footer(str(index + 1))]
            # generate svg to string for every page data
            _svg_str, _svg_name = SVG.gen_drawings(page)[0]
            _svg.append((_svg_str.tostring(), _svg_name))

        # NOTE "Care" added last with custom qrcode svg
        _page += 1
        page_care_svg_and_name, page_care_json = self.get_page_care(
            page_number=_page,
            page_name='care',
            clipart_name='care',
        )
        _svg.append(page_care_svg_and_name)

        pages['svg'] = _svg
        # JSON_________________________________
        pages['json'].append(page_front_json)
        pages['json'] += page_assembly_tips_json
        pages['json'] += pages_assembly_json
        if page_adjust_feet_json:
            pages['json'].append(page_adjust_feet_json)
        if page_joints_json:
            pages['json'].append(page_joints_json)
            pages['json'].append(page_connecting_json)
        if shelf_flow_data.is_desk:
            if page_desk_horizontal_json:
                pages['json'].append(page_desk_horizontal_json)
            if shelf_flow_data.desk_solo_vertical:
                pages['json'].append(page_desk_vertical_json)
            pages['json'].append(page_desk_back_json)
        if product_version != PhysicalProductVersion.TREX:
            if page_last_legs_json:
                pages['json'].append(page_last_legs_json)
        pages['json'].append(page_adjust_verticals_json)
        if (
            shelf_flow_data.assembly_style == AssemblyStyle.IONIC
            or shelf_flow_data.is_desk
        ):
            pages['json'].append(page_raising_json)
        if page_backs_json:
            pages['json'].append(page_backs_json)
        if page_supports_json:
            pages['json'].append(page_supports_json)
        if page_inserts_json:
            pages['json'] += page_inserts_json
        if page_doors_json:
            pages['json'] += page_doors_json
            pages['json'].append(page_adjusting_doors_json)
        if page_drawers_json:
            pages['json'] += pages_assembly_drawers_json
            pages['json'] += page_drawers_json
            pages['json'].append(page_adjust_drawers1_json)
            pages['json'].append(page_adjust_drawers2_json)
            pages['json'].append(page_level_drawers_json)

        if page_fixing_json:
            pages['json'].append(page_fixing_json)
        if page_doors_json:
            pages['json'].append(page_disassembly_hinges_verticals_json)
        if page_drawers_json:
            pages['json'].append(page_disassembly_slides_verticals_json)
        pages['json'] += pages_disassembly_backs_json
        pages['json'].append(page_congratulations_json)
        pages['json'].append(page_care_json)

        return pages
