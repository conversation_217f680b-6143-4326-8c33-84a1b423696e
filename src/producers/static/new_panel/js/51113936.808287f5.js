(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["51113936"],{"01a7":function(t,e,a){"use strict";var i=a("1f0c"),n=a.n(i);n.a},"19f3":function(t,e,a){},"1f0c":function(t,e,a){},"2a43":function(t,e,a){"use strict";var i=a("19f3"),n=a.n(i);n.a},"2fa3":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return l}));a("4917");var i=function(t){var e=window.URL.createObjectURL(new Blob([t.data])),a=document.createElement("a");a.href=e;var i=t.headers["content-disposition"],n=i.match(/filename=(.+)/);a.setAttribute("download",n[1]),document.body.appendChild(a),a.click(),a.remove(),window.URL.revokeObjectURL(e)},n=function(t){var e=document.createElement("a");e.href=t,e.click(),e.remove()},l=function(t){var e=t.completed,a=t.all;if(0===a)return"100% (0/0)";var i=(e/a*100).toFixed(2);return"".concat(i,"% (").concat(e,"/").concat(a,")")}},"3de1":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"q-pa-md"},[a("q-table",t._b({staticClass:"sticky-header-table",attrs:{title:t.title,"row-key":t.rowKey,selection:"multiple",selected:t.selected,pagination:t.pagination,"loading-label":"Pobieranie batchy...","rows-per-page-label":"Ilość na stronie:",flat:"",bordered:"",separator:"cell"},on:{"update:selected":[function(e){t.selected=e},t.updateSelected],"update:pagination":function(e){t.pagination=e},request:t.reloadTable},scopedSlots:t._u([t._l(t.$scopedSlots,(function(e,a){return{key:a,fn:function(e){return[t._t(a,null,null,e)]}}})),{key:"loading",fn:function(){return[a("q-inner-loading",{attrs:{showing:"",color:"primary"}})]},proxy:!0}],null,!0)},"q-table",{data:t.tableData,columns:t.columns,loading:t.loading,rowsPerPageOptions:[50,100,250,500,1e3]},!1))],1)},n=[],l=(a("8e6e"),a("8a81"),a("ac6a"),a("cadf"),a("06db"),a("456d"),a("c47a")),s=a.n(l);function r(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function o(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?r(a,!0).forEach((function(e){s()(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):r(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}var c={name:"BaseTable",props:{apiUrl:{type:String,required:!0},title:{type:String,required:!0},columns:{type:Array,required:!0},rowKey:{type:String,required:!0},responseKey:{type:String,required:!0},updateLoading:{type:Function,required:!0},updateSelected:{type:Function,required:!0},filters:{type:Object,default:function(){}}},data:function(){return{pagination:{sortBy:"desc",descending:!1,page:1,rowsPerPage:100,rowsNumber:10},loading:!1,selected:[],tableData:[]}},computed:{tableFilters:function(){return this.filters}},watch:{loading:function(){this.updateLoading(this.loading)}},created:function(){this.getData(this.pagination)},methods:{reloadTable:function(t){this.getData(t.pagination);var e=t.pagination,a=e.page,i=e.rowsPerPage,n=e.sortBy,l=e.descending;this.pagination.page=a,this.pagination.rowsPerPage=i,this.pagination.sortBy=n,this.pagination.descending=l},resetSelected:function(){this.selected=[],this.updateSelected([])},getData:function(t,e){var a=this,i=t||this.pagination,n=o({},this.filters,{},e||{});this.loading=!0,this.$axios.get(this.apiUrl,{params:o({page_number:i.page,page_size:i.rowsPerPage},n)}).then((function(t){a.tableData=t.data[a.responseKey],a.pagination.rowsNumber=t.data.count,a.loading=!1}))}}},u=c,d=(a("2a43"),a("2877")),p=a("fe09"),f=Object(d["a"])(u,i,n,!1,null,null,null);e["a"]=f.exports;f.options.components=Object.assign({QTable:p["x"],QInnerLoading:p["l"]},f.options.components||{})},4106:function(t,e,a){"use strict";var i,n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"circle",style:"background: "+t.backgroundColor})},l=[],s=(a("c5f6"),a("c47a")),r=a.n(s),o=a("4c6b"),c=(i={},r()(i,o["a"].INITIAL,"white"),r()(i,o["a"].DOWNLOADED,"green"),r()(i,o["a"].RECALCULATED_AFTER_DOWNLOAD,"red"),i),u={name:"FileStatusCircle",props:{status:{type:[Number,null],default:null}},computed:{backgroundColor:function(){return c[this.status]}}},d=u,p=(a("01a7"),a("2877")),f=Object(p["a"])(d,n,l,!1,null,null,null);e["a"]=f.exports},"4c6b":function(t,e,a){"use strict";a.d(e,"g",(function(){return i})),a.d(e,"e",(function(){return n})),a.d(e,"a",(function(){return l})),a.d(e,"c",(function(){return s})),a.d(e,"b",(function(){return r})),a.d(e,"d",(function(){return o})),a.d(e,"f",(function(){return c}));var i=[{label:"DT",value:1},{label:"TNT",value:2},{label:"UPS",value:3},{label:"DPD",value:4},{label:"FEDEX",value:5}],n=[{label:"Styczeń",value:1},{label:"Luty",value:2},{label:"Marzec",value:3},{label:"Kwiecień",value:4},{label:"Maj",value:5},{label:"Czerwiec",value:6},{label:"Lipiec",value:7},{label:"Sierpień",value:8},{label:"Wrzesień",value:9},{label:"Październik",value:10},{label:"Listopad",value:11},{label:"Grudzień",value:12}],l={INITIAL:0,DOWNLOADED:1,RECALCULATED_AFTER_DOWNLOAD:2},s={NOT_SET:0,STANDARD:1,EXTENDED:2,CUSTOM:3,DRAWERS:4,DRAWERS_EXTENDED:5,COMPLAINTS:10},r={ABORTED:0,NEW:1,IN_PRODUCTION:2,SENT_TO_CUSTOMER:3},o={NEW:0,ACCEPTED:1,REJECTED:2,CHANGES_REQUESTED:3},c={ABORTED:0,NEW:1,UTILIZATION:2,UTILIZATION_DONE:3,ASSIGNED_TO_PRODUCTION:4,IN_PRODUCTION:5,SENT_TO_CUSTOMER:6,SENT_TO_WAREHOUSE:7,QUALITY_BLOCKER:8,INTERNAL_USAGE:9,TO_BE_SHIPPED:10,QUALITY_CONTROL:11,ABORTED_DONE:12,SHELFMARKET:13,SHELFMARKET_DONE:14,DELIVERED_TO_CUSTOMER:15,CM_UTILIZATION_DONE:16,TO_BE_SHIPPED_AWIZATION:17,INTERNAL_SHIPMENT:18}},"504c":function(t,e,a){var i=a("9e1e"),n=a("0d58"),l=a("6821"),s=a("52a7").f;t.exports=function(t){return function(e){var a,r=l(e),o=n(r),c=o.length,u=0,d=[];while(c>u)a=o[u++],i&&!s.call(r,a)||d.push(t?[a,r[a]]:r[a]);return d}}},"5cc7":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("q-page",{staticClass:"q-pa-lg"},[a("div",{staticClass:"q-pa-md"},[a("div",{staticClass:"q-pa-md"},[a("BatchFilterBar",{attrs:{filters:t.filters},on:{"filter-changed":t.handleFilterChange,"filter-applied":t.filterData}}),a("BaseTable",t._b({ref:"tableRef",attrs:{title:t.complaintList?"Reklamacje":"Batche","row-key":"batch_id","response-key":"batches"},scopedSlots:t._u([{key:"body-cell-actions_needed",fn:function(e){return[a("q-td",t._b({},"q-td",{props:e,class:{"actions-needed-warning":["recalculate files","fix errors"].includes(e.value)}},!1),[t._v("\n            "+t._s(e.value)+"\n          ")])]}},{key:"body-cell-delayed_items",fn:function(e){return[a("q-td",t._b({},"q-td",{props:e,class:{"single-cell-with-warning":0!==e.value.length}},!1),[t._v("\n            "+t._s(e.value)+"\n          ")])]}},{key:"top",fn:function(){return[a("div",{staticClass:"row q-gutter-sm"},[a("q-btn",t._b({attrs:{flat:"",dense:"",color:"primary",label:"Pobierz zużycia - xml"},on:{click:t.getUsageForSelected}},"q-btn",{disable:t.loading||0===t.selected.length},!1)),a("q-btn",t._b({attrs:{flat:"",dense:"",color:"primary",label:"Pobierz pliki na kartoniarke"},on:{click:t.getFilesForCardboard}},"q-btn",{disable:t.loading||0===t.selected.length},!1)),a("q-btn",t._b({attrs:{flat:"",dense:"",color:"primary",label:"Pobierz programy CNC"},on:{click:t.getCncPrograms}},"q-btn",{disable:t.loading||0===t.selected.length},!1)),a("q-btn",t._b({attrs:{flat:"",dense:"",color:"primary",label:"Pobierz pliki pakowania"},on:{click:t.getPackagingFiles}},"q-btn",{disable:t.loading||0===t.selected.length},!1)),a("q-btn",t._b({attrs:{flat:"",dense:"",color:"primary",label:"Pobierz pliki produkcyjne"},on:{click:t.getProductionFiles}},"q-btn",{disable:t.loading||0===t.selected.length},!1))],1)]},proxy:!0},{key:"body-cell-actions",fn:function(e){return[a("q-td",t._b({},"q-td",{props:e},!1),[a("div",{staticClass:"flex column items-end"},[a("div",{staticClass:"flex flex-center"},[a("a",t._b({staticClass:"q-pa-xs"},"a",{href:"/pages/producer_batch/"+e.row.batch_id},!1),[a("q-btn",{staticClass:"q-mr-lg",attrs:{color:"primary",label:"Szczegóły"}})],1)]),a("BatchActionButton",{attrs:{label:"Pobierz pliki produkcyjne",href:"/pages/api/production_files/?batch_ids="+e.row.batch_id,status:e.row.production_files_status}}),a("BatchActionButton",{attrs:{label:"Pobierz pliki od pakowania",href:"/pages/api/packaging_files/?batch_ids="+e.row.batch_id,status:e.row.packaging_files_status}}),a("BatchActionButton",{attrs:{label:"Pobierz programy CNC",href:"/pages/api/cnc_connections/?batch_ids="+e.row.batch_id,status:e.row.cnc_files_status}}),t.hasQualityControlItems(e.row)?a("div",{staticClass:"flex flex-center"},[a("q-btn",{staticClass:"q-mr-lg",attrs:{color:"primary",label:e.row.pending_quality_hold_requests?"Żądanie zwolnienia w toku":"Zwolnij ze statusu QC",disable:e.row.pending_quality_hold_requests},on:{click:function(a){t.releaseFromQualityControl(e.row.batch_id,t.getQualityControlItems(e.row))}}})],1):t._e(),t.hasQualityBlockerItems(e.row)?a("div",{staticClass:"flex flex-center"},[a("q-btn",{staticClass:"q-mr-lg",attrs:{color:"primary",label:e.row.pending_quality_hold_requests?"Żądanie zwolnienia w toku":"Zwolnij ze statusu QUALITY BLOCKER",disable:e.row.pending_quality_hold_requests},on:{click:function(a){t.releaseFromQualityBlocker(e.row.batch_id,t.getQualityBlockerItems(e.row))}}})],1):t._e()],1)])]}},{key:"body-cell-items",fn:function(e){return[a("q-td",t._b({},"q-td",{props:e,class:{"has-quality-hold":e.row.items.some((function(e){return e.status===t.PRODUCT_STATUS.QUALITY_CONTROL||e.status===t.PRODUCT_STATUS.QUALITY_BLOCKER}))}},!1),[t._v("\n            "+t._s(e.value)+"\n          ")])]}}])},"BaseTable",{columns:t.columns,loading:t.loading,filters:t.filters,updateLoading:t.updateLoading,updateSelected:t.updateSelected,apiUrl:t.apiUrl},!1))],1)])])},n=[],l=a("967e"),s=a.n(l),r=(a("96cf"),a("f751"),a("f3e3")),o=a.n(r),c=(a("ac6a"),a("cadf"),a("06db"),a("ffc1"),a("4db1")),u=a.n(c),d=a("3de1"),p=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"row"},[a("q-input",{staticClass:"q-ml-md q-pa-sm col-1",attrs:{label:"Batch ID",filled:""},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.applyFilter(e)}},model:{value:t.filterData.batchIds,callback:function(e){t.$set(t.filterData,"batchIds",e)},expression:"filterData.batchIds"}}),a("q-input",{staticClass:"q-ml-md q-pa-sm col-1",attrs:{label:"Szafki",filled:""},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.applyFilter(e)}},model:{value:t.filterData.items,callback:function(e){t.$set(t.filterData,"items",e)},expression:"filterData.items"}}),a("q-select",{staticClass:"q-pa-sm col-1",attrs:{clearable:"",label:"Typ batchy",options:t.batchTypeOptions,filled:""},model:{value:t.filterData.batchType,callback:function(e){t.$set(t.filterData,"batchType",e)},expression:"filterData.batchType"}}),a("q-select",{staticClass:"q-pa-sm col-1",attrs:{clearable:"",label:"Status",options:t.batchStatusOptions,filled:""},model:{value:t.filterData.batchStatus,callback:function(e){t.$set(t.filterData,"batchStatus",e)},expression:"filterData.batchStatus"}}),a("q-input",{staticClass:"q-pa-sm col-1",attrs:{label:"Kolor",type:"searchActive",filled:""},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.applyFilter(e)}},model:{value:t.filterData.materialDescription,callback:function(e){t.$set(t.filterData,"materialDescription",e)},expression:"filterData.materialDescription"}}),a("q-checkbox",{staticClass:"q-pa-sm",attrs:{label:"opóźnienia",filled:""},model:{value:t.filterData.isDelayed,callback:function(e){t.$set(t.filterData,"isDelayed",e)},expression:"filterData.isDelayed"}}),a("div",{staticClass:"q-pa-sm"},[a("q-btn",{staticClass:"q-pa-sm",attrs:{color:"primary",label:"Filtruj"},on:{click:t.applyFilter}})],1)],1)},f=[],b=(a("7514"),a("4c6b")),h={name:"BatchFilterBar",props:{filters:{type:Object,required:!0}},data:function(){return{filterData:{batchIds:"",items:"",batchType:"",batchStatus:"",materialDescription:"",isDelayed:!1},batchTypeOptions:[{label:"NOT_SET",value:b["c"].NOT_SET},{label:"STANDARD",value:b["c"].STANDARD},{label:"EXTENDED",value:b["c"].EXTENDED},{label:"CUSTOM",value:b["c"].CUSTOM}],batchStatusOptions:[{label:"Aborted",value:b["b"].ABORTED},{label:"Waiting for nesting",value:b["b"].NEW},{label:"In production",value:b["b"].IN_PRODUCTION},{label:"Sent to customer",value:b["b"].SENT_TO_CUSTOMER}]}},watch:{filterData:{handler:function(t){this.$emit("filter-changed",{batchIds:t.batchIds,items:t.items,batchType:t.batchType,batchStatus:t.batchStatus,materialDescription:t.materialDescription,isDelayed:t.isDelayed})},deep:!0},filters:{handler:function(t){this.filterData.batchIds=t.batch_ids||"",this.filterData.items=t.items||"",this.filterData.materialDescription=t.material_description||"",this.filterData.isDelayed=t.is_delayed||!1,t.batch_type?this.filterData.batchType=this.batchTypeOptions.find((function(e){return e.value===t.batch_type}))||"":this.filterData.batchType="",t.status?this.filterData.batchStatus=this.batchStatusOptions.find((function(e){return e.value===t.status}))||"":this.filterData.batchStatus=""},deep:!0}},created:function(){var t=this;this.filterData.batchIds=this.filters.batch_ids||"",this.filterData.items=this.filters.items||"",this.filterData.materialDescription=this.filters.material_description||"",this.filterData.isDelayed=this.filters.is_delayed||!1,this.filters.batch_type&&(this.filterData.batchType=this.batchTypeOptions.find((function(e){return e.value===t.filters.batch_type}))||""),this.filters.status&&(this.filterData.batchStatus=this.batchStatusOptions.find((function(e){return e.value===t.filters.status}))||"")},methods:{applyFilter:function(){this.$emit("filter-applied")}}},m=h,_=a("2877"),y=a("fe09"),g=Object(_["a"])(m,p,f,!1,null,null,null),T=g.exports;g.options.components=Object.assign({QInput:y["m"],QSelect:y["w"],QCheckbox:y["e"],QBtn:y["b"]},g.options.components||{});var D=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex flex-center"},[a("a",t._b({staticClass:"q-pa-xs"},"a",{href:t.href},!1),[a("q-btn",t._b({attrs:{color:"primary"}},"q-btn",{label:t.label},!1))],1),a("FileStatusCircle",{attrs:{status:t.status}})],1)},O=[],v=(a("c5f6"),a("4106")),E={name:"BatchActionButton",components:{FileStatusCircle:v["a"]},props:{label:{type:String,required:!0},href:{type:String,required:!0},status:{type:[Number,null],default:null}}},S=E,w=Object(_["a"])(S,D,O,!1,null,null,null),C=w.exports;w.options.components=Object.assign({QBtn:y["b"]},w.options.components||{});var k=a("2fa3"),q={name:"BatchesView",components:{BaseTable:d["a"],BatchFilterBar:T,BatchActionButton:C},props:{complaintList:Boolean,isHistory:{type:Boolean,default:!1}},data:function(){return{apiUrl:"/pages/api/producer_batches_rest/",loading:!1,selected:[],batchType:"",isDelayed:!1,items:"",batchIds:"",batchTypeOptions:[{label:"NOT_SET",value:b["c"].NOT_SET},{label:"STANDARD",value:b["c"].STANDARD},{label:"EXTENDED",value:b["c"].EXTENDED},{label:"CUSTOM",value:b["c"].CUSTOM}],batchStatus:"",batchStatusOptions:[{label:"Aborted",value:b["b"].ABORTED},{label:"Waiting for nesting",value:b["b"].NEW},{label:"In production",value:b["b"].IN_PRODUCTION},{label:"Sent to customer",value:b["b"].SENT_TO_CUSTOMER}],materialDescription:"",PRODUCT_STATUS:b["f"]}},computed:{filters:function(){return{batch_ids:this.batchIds,items:this.items,status:this.batchStatus&&this.batchStatus.value,batch_type:this.batchType&&this.batchType.value,material_description:this.materialDescription,complaints_list:this.complaintList,is_history:this.isHistory,is_delayed:this.isDelayed}},columns:function(){return[{name:"actions_needed",align:"center",label:"File alert!",field:function(t){return t.actions_needed}},{name:"name",required:!0,label:"Batch",align:"left",field:"batch_id"}].concat(u()(this.isHistory?[{name:"days",align:"center",label:"Dni realizacji",field:function(t){var e=t.days_of_production;return e}}]:[{name:"production_progress",align:"center",label:"Stopień wykonania",field:function(t){var e=t.completed_products;return Object(k["c"])(e)}}]),[{name:"batch_status",align:"center",label:"Status",field:function(t){return t.batch_status}}],u()(this.complaintList?[]:[{name:"batch_type",align:"center",label:"Typ",field:function(t){return"".concat(t.batch_type,"\n").concat(t.is_sidebohr?"S+":"")},classes:"break-lines"}]),u()(this.complaintList?[{name:"express_replacement",align:"center",label:"Express",field:function(t){return"".concat(t.express_replacement?"Tak":"Nie")}}]:[]),[{name:"area",align:"center",label:"Płyta [m^2]",field:function(t){return t.area.toFixed(2)}},{name:"banding_length",align:"center",label:"Obrzeże [mb]",field:function(t){return t.banding_length}},{name:"material_description",align:"center",label:"Kolor",field:function(t){return t.material_description}},{name:"created_at",label:"Dodano",field:"created_at"},{name:"items",label:"ID produktów",field:function(t){return t.items.map((function(t){return t.status===b["f"].QUALITY_CONTROL?"".concat(t.id," (QC)"):t.status===b["f"].QUALITY_BLOCKER?"".concat(t.id," (QB)"):t.id})).join(", ")},classes:"items-column"},{name:"priorities",label:"Priorytety",field:function(t){return Object.entries(t.priorities).map((function(t){var e=o()(t,2),a=e[0],i=e[1];return"".concat(a,": ").concat(i)})).join("\n")},classes:"break-lines"},{name:"element_order_ids",label:"Zamówienie",field:function(t){return t.element_order_ids.join(", ")}},{name:"delayed_items",label:"Opóźnienia",field:function(t){return t.delayed_items.join(", ")},classes:"items-column"},{name:"actions",label:"Akcje",style:"min-width: 300px"}])}},watch:{complaintList:function(){this.refreshTable()},isHistory:function(){this.refreshTable()}},methods:{updateSelected:function(t){this.selected=t},updateLoading:function(t){this.loading=t},filterData:function(){this.$refs.tableRef.getData()},refreshTable:function(){this.$refs.tableRef.getData(null,{is_history:this.isHistory,complaints_list:this.complaintList})},handleFilterChange:function(t){Object.assign(this,t)},getApiUrlWithBatchesIds:function(t){return"/pages/api/".concat(t,"/?batch_ids=").concat(this.selected.map((function(t){return t.batch_id})))},downloadFileFromApi:function(t){var e=this;this.$axios({method:"GET",url:t,responseType:"blob"}).then((function(t){Object(k["a"])(t),e.filterData()})).catch((function(t){return e.showErrorNotification(t)}))},getUsageForSelected:function(){var t=this;this.$axios({method:"POST",url:"/pages/api/producer_actions_rest/",data:{action:"getUsageXml",batches:this.selected.map((function(t){return t.batch_id}))},responseType:"blob"}).then((function(t){Object(k["a"])(t)})).catch((function(e){return t.showErrorNotification(e)}))},getFilesForCardboard:function(){var t=this;this.$axios({method:"POST",url:"/pages/api/producer_actions_rest/",data:{action:"getCardboardFile",batches:this.selected.map((function(t){return t.batch_id}))},responseType:"blob"}).then((function(t){Object(k["a"])(t)})).catch((function(e){return t.showErrorNotification(e)}))},getCncPrograms:function(){this.downloadFileFromApi(this.getApiUrlWithBatchesIds("cnc_connections"))},getPackagingFiles:function(){this.downloadFileFromApi(this.getApiUrlWithBatchesIds("packaging_files"))},getProductionFiles:function(){this.downloadFileFromApi(this.getApiUrlWithBatchesIds("production_files"))},hasQualityControlItems:function(t){return t.items.some((function(t){return t.status===b["f"].QUALITY_CONTROL}))},hasQualityBlockerItems:function(t){return t.items.some((function(t){return t.status===b["f"].QUALITY_BLOCKER}))},getQualityControlItems:function(t){return t.items.filter((function(t){return t.status===b["f"].QUALITY_CONTROL})).map((function(t){return t.id}))},getQualityBlockerItems:function(t){return t.items.filter((function(t){return t.status===b["f"].QUALITY_BLOCKER})).map((function(t){return t.id}))},requestQualityRelease:function(t,e,a){var i=this;return s.a.async((function(n){while(1)switch(n.prev=n.next){case 0:this.$q.dialog({title:"Potwierdź zwolnienie",message:"Czy na pewno chcesz wysłać żądanie zwolnienia ze statusu?",cancel:!0,persistent:!0}).onOk((function(){var n;return s.a.async((function(l){while(1)switch(l.prev=l.next){case 0:return l.prev=0,l.next=3,s.a.awrap(i.$axios.post("/pages/api/quality_hold_release/".concat(t,"/"),{products:e,status:a}));case 3:n=l.sent,i.showSuccessNotification("Utworzono żądania zwolnienia dla ".concat(n.data.items_count," produktów")),i.filterData(),l.next=11;break;case 8:l.prev=8,l.t0=l["catch"](0),i.showErrorNotification(l.t0);case 11:case"end":return l.stop()}}),null,null,[[0,8]])}));case 1:case"end":return n.stop()}}),null,this)},releaseFromQualityControl:function(t,e){return this.requestQualityRelease(t,e,b["f"].QUALITY_CONTROL)},releaseFromQualityBlocker:function(t,e){return this.requestQualityRelease(t,e,b["f"].QUALITY_BLOCKER)},showSuccessNotification:function(t){this.$q.notify({message:t,type:"positive"})},showErrorNotification:function(t){this.$q.notify({message:t.message,type:"negative"})}}},A=q,N=Object(_["a"])(A,i,n,!1,null,null,null);e["default"]=N.exports;N.options.components=Object.assign({QPage:y["s"],QTd:y["y"],QBtn:y["b"]},N.options.components||{})},ffc1:function(t,e,a){var i=a("5ca1"),n=a("504c")(!0);i(i.S,"Object",{entries:function(t){return n(t)}})}}]);