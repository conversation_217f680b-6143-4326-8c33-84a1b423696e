import uuid

from abc import ABC

from django.contrib import admin
from django.contrib.admin import <PERSON><PERSON><PERSON><PERSON><PERSON>er

from django_filters.filterset import FilterSetMetaclass
from django_requestlogging.logging_filters import RequestFilter


class MoreVerboseFilter(RequestFilter):
    """self.request set by middleware on this filter init"""

    def get_request_id(self):
        return uuid.uuid4().hex

    def filter(self, record):
        """
        Adds request_id and user_id to available LogRecord attributes
        """
        request = self.request
        user = getattr(request, 'user', None)
        if user and not user.is_anonymous:
            record.user_id = user.id
        else:
            record.user_id = '-'
        META = getattr(request, 'META', {})  # noqa: N806
        if record.levelno > 30 and request:  # WARNING
            record.user_agent = META.get('HTTP_USER_AGENT', '')
        else:
            record.user_agent = ''
        record.remote_addr = META.get('REMOTE_ADDR', '-')
        record.request_id = self.get_request_id()
        return True


class MultipleChoiceDropdownFilter(admin.SimpleListFilter, ABC):
    """
    Custom multiple choice filter for django admin.
    To use you need to inherit the class and implement the basic variables and
    methods:
    - title (variable) - name of the filtering field in label
    - parameter_name (variable) - lookup expression of the filter in url
    - lookups (method) - list of tuples with filter values and values for display
     in dropdown
    Example in MultipleProductPriorityFilter.
    If you use long lookup f.e. product__status__in you should probably add it
    to lookup_allowed in your admin.
    For custom filtering you can override choices and queryset methods.
    """

    template = 'admin_custom/multiple_choice_dropdown_filter.html'

    def choices(self, changelist):
        for lookup, title in self.lookup_choices:
            yield {
                'value': lookup,
                'display': title,
                'parameter_name': self.parameter_name,
            }

    def queryset(self, request, queryset):
        if self.value():
            filter_values = self.value().split(',')
            filters_dict = {
                self.parameter_name: filter_values,
            }
            return queryset.filter(**filters_dict)
        return queryset


class BaseMultipleChoiceListFilter(admin.SimpleListFilter, ABC):
    def value_as_list(self):
        return self.value().split(',') if self.value() else []

    def choices(self, changelist):
        yield {
            'selected': self.value() is None,
            'query_string': changelist.get_query_string(remove=[self.parameter_name]),
            'display': 'All',
            'reset': True,
        }
        for lookup, title in self.lookup_choices:
            yield {
                'selected': str(lookup) in self.value_as_list(),
                'query_string': changelist.get_query_string(
                    {self.parameter_name: lookup}
                ),
                'include_query_string': self._amend_query_string(
                    changelist,
                    include=str(lookup),
                ),
                'exclude_query_string': self._amend_query_string(
                    changelist,
                    exclude=str(lookup),
                ),
                'display': title,
            }

    def _amend_query_string(self, changelist, include=None, exclude=None):
        selections = self.value_as_list()
        if include and include not in selections:
            selections.append(include)
        if exclude in selections:
            selections.remove(exclude)
        if selections:
            csv = ','.join(selections)
            return changelist.get_query_string({self.parameter_name: csv})
        else:
            return changelist.get_query_string(remove=[self.parameter_name])


class ExpressReplacementFilter(SimpleListFilter):
    title = 'Express Replacement'
    parameter_name = 'exprep'

    def lookups(self, request, model_admin):
        return [('yes', 'Yes'), ('no', 'No')]

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        is_express_replacement = self.value() == 'yes'
        return queryset.filter(
            order__complaints__express_replacement=is_express_replacement
        )


class IsBatchedFilter(SimpleListFilter):
    title = 'Is Batched'
    parameter_name = 'is_batched'

    def lookups(self, request, model_admin):
        return [('yes', 'Yes'), ('no', 'No')]

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        is_batched = self.value() == 'yes'
        return queryset.exclude(batch__isnull=is_batched)


class CamelCaseFilterSetMetaclass(FilterSetMetaclass):
    """
    Metaclass for django-filter FilterSet that translates snake_case fields
    into camelCase query parameters.

    Make your Meta class inherit this one to achieve this behavior.
    """

    @staticmethod
    def camelify(name):
        first, *others = name.split('_')
        return ''.join([first, *[other.capitalize() for other in others]])

    @classmethod
    def get_declared_filters(cls, bases, attrs):
        filters = super().get_declared_filters(bases, attrs)
        return {cls.camelify(k): v for k, v in filters.items()}
