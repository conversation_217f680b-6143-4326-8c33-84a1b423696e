from decimal import Decimal

from django.conf import settings
from django.contrib import admin
from django.contrib.admin import RelatedOnlyFieldListFilter
from django.contrib.sites.models import Site

from actstream.models import Follow
from rangefilter.filters import DateRangeFilter

from custom.models import (
    ExchangeRate,
    GlobalSettings,
)
from custom.models.models import DocumentRequest
from logger.choices import Source
from logger.models import Log


class SingletonAdmin:
    def changelist_view(self, request, extra_context=None):
        if not extra_context:
            extra_context = dict()  # noqa: C408
        extra_context['title'] = self.model._meta.verbose_name_plural
        singleton_model = self.model.objects.first()
        # Singleton is showing first item's
        # changeform view instead of the list view
        return super().changeform_view(
            request,
            str(singleton_model.id),
            extra_context=extra_context,
        )

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False


class GlobalSettingsAdmin(SingletonAdmin, admin.ModelAdmin):
    model = GlobalSettings

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        serializable_cleaned_data = []
        for key, value in form.cleaned_data.items():
            serializable_value = float(value) if isinstance(value, Decimal) else value
            serializable_cleaned_data.append({key: serializable_value})

        Log.objects.create(
            user=request.user,
            logger_source=Source.LOGGER_ADMIN,
            action='change_global_settings',
            model=obj._meta.label,
            model_id=obj.pk,
            data=serializable_cleaned_data,
        )

    def get_readonly_fields(self, request, obj=None):
        return ('is_live_payment',) if settings.IS_PRODUCTION else tuple()  # noqa: C408


class ExchangeRateAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'exchange_date',
        'weekday',
        'number_of_entries',
        'set_with',
        'created_at',
    )
    date_hierarchy = 'exchange_date'


class DocumentRequestAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'action_name',
        'file',
        'requested_by',
        'status',
        'created_at',
    )

    list_filter = (
        ('created_at', DateRangeFilter),
        ('requested_by', RelatedOnlyFieldListFilter),
        'status',
        'action_name',
    )

    autocomplete_fields = ('requested_by',)


admin.site.unregister(Follow)
admin.site.register(Site)
admin.site.register(GlobalSettings, GlobalSettingsAdmin)
admin.site.register(ExchangeRate, ExchangeRateAdmin)
admin.site.register(DocumentRequest, DocumentRequestAdmin)
