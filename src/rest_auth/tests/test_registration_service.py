from unittest.mock import patch

import pytest

from requests_mock import mock as requests_mock

from rest_auth.services.registration_services import (
    apply_promo_code_after_registration,
    migrate_guest_profile,
    set_users_profile_data,
)
from user_profile.choices import UserType


@pytest.mark.django_db
class TestRegistrationService:
    def test_registration_guest_profile_for_user(self, user_factory, mocker):
        user = user_factory(profile__user_type=UserType.GUEST_CUSTOMER)
        user1 = user_factory(profile__user_type=UserType.GUEST_CUSTOMER)
        transfer_to_profile_mock = mocker.patch(
            'user_profile.services.user_profile_transfer.'
            'UserProfileTransferService.transfer'
        )
        result = migrate_guest_profile(user, user1)
        assert transfer_to_profile_mock.call_count == 1
        assert result is None

    def test_set_users_profile_data(self, user):
        request = requests_mock()
        request.session = {
            'user_utm_source': 'test',
            'HTTP_REFERER': 'test',
            'contact_ts': '11111.22',
            'advertising_consents': True,
        }
        request.META = {'HTTP_X_REAL_IP': '************', 'HTTP_USER_AGENT': 'iphone'}
        request.COOKIES = {'test': 'test'}
        serialized_data = {
            'name': 'test',
            'newsletter': True,
        }
        with patch(
            'rest_auth.services.registration_services.set_localization_for_user_profile'
        ) as set_localization_for_user_profile_mock:
            set_users_profile_data(request, user.profile, serialized_data)
            assert set_localization_for_user_profile_mock.call_count == 1
        assert user.profile.first_name == 'test'
        assert user.profile.registration_referrer_uri == 'test'
        assert user.profile.additional_data.get('advertising_consents') is True

    @patch('custom.metrics.metrics_client')
    @patch('django.contrib.auth')
    def test_apply_promo_code_after_registration(
        self,
        login_mock,
        _,  # noqa: PT019
        cart,
        voucher_factory,
    ):
        request = requests_mock()
        request.session = {'promo_code': 'test'}
        voucher = voucher_factory(active=True, quantity_left=10, code='test')
        login_mock.login.return_value = True
        assert 'promo_code' in request.session
        with patch('django.contrib.auth.login'):
            apply_promo_code_after_registration(cart.owner, request)

        assert 'promo_code' not in request.session

        cart.refresh_from_db()
        assert voucher in cart.vouchers.all()
        assert cart.promo_text == 'test'
