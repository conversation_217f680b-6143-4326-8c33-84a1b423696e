from unittest import mock

import pytest

from zeep.exceptions import Fault

from checkout.enums import (
    ViesResponseStatus,
    ViesValidationTriggerAction,
)
from checkout.vat.validation import (
    VatFormatError,
    decompose_vat_number,
)
from checkout.vat.vies_client import (
    FAULT_ERROR_STRINGS,
    validate_in_vies,
)


class TestDecomposeVatNumber:
    @pytest.mark.parametrize(
        'vat_number, expected_decomposed',  # noqa: PT006
        [
            ('EL123456789', ('EL', '123456789')),
            ('FR 12 123 456 789', ('FR', '12123456789')),
            ('GB 123456789', ('GB', '123456789')),
        ],
    )
    def test_validate_vat_number(self, vat_number, expected_decomposed):
        country_code, vat_number = decompose_vat_number(vat_number)
        assert country_code == expected_decomposed[0]
        assert vat_number == expected_decomposed[1]

    def test_raises_error_when_no_country_code(self):
        with pytest.raises(VatFormatError):
            decompose_vat_number('123456789')

    def test_raises_error_for_non_existent_country(self):
        with pytest.raises(VatFormatError):
            decompose_vat_number('XX123456789')


@pytest.mark.parametrize(
    'mock_get_vies_response, expected_status, expected_validity',  # noqa: PT006
    [
        (
            mock.patch(
                'checkout.vat.vies_client.get_vies_response',
                return_value={'valid': True},
            ),
            ViesResponseStatus.VALID.value,
            True,
        ),
        (
            mock.patch(
                'checkout.vat.vies_client.get_vies_response',
                side_effect=ConnectionError,
            ),
            ViesResponseStatus.UNAVAILABLE.value,
            None,
        ),
        (
            mock.patch(
                'checkout.vat.vies_client.get_vies_response',
                side_effect=Fault(message='RANDOM FAULT'),
            ),
            ViesResponseStatus.UNAVAILABLE.value,
            None,
        ),
        (
            mock.patch(
                'checkout.vat.vies_client.get_vies_response',
                side_effect=Fault(message=FAULT_ERROR_STRINGS['INVALID_INPUT']),
            ),
            ViesResponseStatus.INVALID.value,
            False,
        ),
    ],
)
@pytest.mark.django_db
@mock.patch('checkout.tasks.create_and_save_vies_confirmation_task.delay')
def test_validate_in_vies(
    mock_confirmation_task, mock_get_vies_response, expected_status, expected_validity
):
    with mock_get_vies_response:
        is_valid = validate_in_vies(
            'PL', '1234567890', ViesValidationTriggerAction.PAYMENT
        )
        mock_confirmation_task.assert_called_once_with(
            'PL', '1234567890', expected_status, ViesValidationTriggerAction.PAYMENT
        )
        assert is_valid == expected_validity
