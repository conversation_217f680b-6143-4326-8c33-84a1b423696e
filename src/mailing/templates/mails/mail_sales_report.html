{% extends 'mails/_base_templates/base_flow_admin.html' %}
{% load i18n %}

{% block title %}{% endblock %}

{% block preheader %}
    {% blocktrans %}Sales report{% endblocktrans %}
{% endblock %}

{% block content %}
	<p style="margin: 30px 60px 0 60px; font-size: 14px; color:#7c7d81; font-family: Helvetica, Arial, sans-serif; line-height: 24px;">
        Tylko Sales report for {{ data.report_date }}
    </p>

    <table width="100%" style="font-size:11px; width: 630px; background-color: #edf0f0; margin: 10px 60px 0px 60px"
           cellpadding="0" cellspacing="0">
        <thead width="100%">
        <tr>
            <th>KPI</th>
            <th>Yesterday</th>
            <th>Last 7d</th>
            <th>Last 28d</th>
            <th>Predicted this week</th>
            <th>Predicted this month</th>
        </tr>
        </thead>
        <tbody>
        {% for result in data.results %}
          <tr>
              <td style="border-top: 1px solid #000000;">{{ result.kpi }}</td>
              <td style="border-top: 1px solid #000000;">{{ result.yesterday }}</td>
              <td style="border-top: 1px solid #000000;">{{ result.last_7d }}</td>
              <td style="border-top: 1px solid #000000;">{{ result.last_28d }}</td>
              <td style="border-top: 1px solid #000000;">{{ result.predicted_week }}</td>
              <td style="border-top: 1px solid #000000;">{{ result.predicted_month }}</td>
          </tr>
        {% endfor %}
        </tbody>
    </table>
    <br/>
    <table width="100%" style="font-size:11px; width: 630px; background-color: #edf0f0; margin: 10px 60px 0px 60px"
           cellpadding="0" cellspacing="0">
        <thead width="100%">
        <tr>
            <th>AB test</th>
            <th>Split</th>
            <th>Days from start</th>
            <th>Carts</th>
            <th>Orders</th>
            <th>CR cart/order</th>
        </tr>
        </thead>
        <tbody>
        {% for test in data.abtests %}
          <tr>
              <td style="border-top: 1px solid #000000;">{{ test.test }}</td>
              <td style="border-top: 1px solid #000000;">{{ test.split }}</td>
              <td style="border-top: 1px solid #000000;">{{ test.days }}</td>
              <td style="border-top: 1px solid #000000;">{{ test.carts }}</td>
              <td style="border-top: 1px solid #000000;">{{ test.orders }}</td>
              <td style="border-top: 1px solid #000000;">{{ test.ctr }}</td>
          </tr>
        {% endfor %}
        </tbody>
    </table>


    <br/>

                <table width="100%" style="font-size:11px; width: 600px; background-color: #edf0f0; margin: 10px 60px 0px 60px" cellpadding="0" cellspacing="0">
                    <thead width="100%"><tr>
                        <th width="">Id</th>
                        <th>Client</th>
                        <th>Items</th>
                        <th>Settled at</th>
                        <th>Promo</th>
                        <th>Total</th>
                    </tr></thead>
                    <tbody>
                    {% for sale in data.sales %}
                    <tr>
                        <td style="border-top: 1px dotted;padding: 3px;">{{ sale.id }}</td>
                        <td style="border-top: 1px dotted;padding: 3px;">{% if sale.is_company %}<span style="color: red">&#9733;</span> {{ sale.company_name }}, {% endif %}{% if sale.first_name and sale.last_name %}{{ sale.first_name }} {{ sale.last_name }}{% endif %}{% if self.country %}, {{ sale.country|title }}{% endif %}</td>
                        <td style="border-top: 1px dotted;padding: 3px;">{% for item in sale.items %}{{ item.name }}: {{ item.price_eur|floatformat:-2 }}€{% if not forloop.last %}, {% endif %}{% endfor %}{% if sale.voucher %}<br/>{{ sale.voucher.code }} ({{ sale.voucher.get_origin_display }}){% endif %}</td>
                        <td style="border-top: 1px dotted;padding: 3px;">{% if sale.settled_at %}{{ sale.settled_at }}{% endif %}</td>
                        <td style="border-top: 1px dotted;padding: 3px;">{{ sale.promo_amount|floatformat:-2 }}€</td>
                        <td style="border-top: 1px dotted;padding: 3px;">{{ sale.total_price|floatformat:-2 }}€</td>
                    </tr>
                    {% endfor %}
                    <tr>
                        <td style="border-top: 1px solid #000000;"></td>
                        <td style="border-top: 1px solid #000000;">Total sales</td>
                        <td style="border-top: 1px solid #000000;">Countries</td>
                        <td style="border-top: 1px solid #000000;">Items</td>
                        <td style="border-top: 1px solid #000000;">Total promo</td>
                        <td style="border-top: 1px solid #000000;">Total price</td>
                    </tr>
                    <tr>
                        <td style="border-top: 1px dotted;padding: 3px;"></td>
                        <td style="border-top: 1px dotted;padding: 3px;">{{ data.sales|length }}</td>
                        <td style="border-top: 1px dotted;padding: 3px;">{% for country,country_count in data.countries.items %}{{ country|title }} x{{ country_count }}{% if not forloop.last %}, {% endif %}{% endfor %}</td>
                        <td style="border-top: 1px dotted;padding: 3px;">{{ data.items }} items</td>
                        <td style="border-top: 1px dotted;padding: 3px;">{{ data.promo_amount }}€</td>
                        <td style="border-top: 1px dotted;padding: 3px;">{{ data.total_price }}€</td>
                    </tr>
                    </tbody>
                </table>

{% endblock %}
