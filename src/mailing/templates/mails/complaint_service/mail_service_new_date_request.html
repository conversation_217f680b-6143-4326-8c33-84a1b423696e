{% extends 'mails/_base_templates/base_flow.html' %}
{% load i18n static mailing_tags %}

{% block content %}
    {% trans 'mail_complaint_service_new_date_request_preheader' as header_1 %}
    {% trans 'mail_complaint_service_new_date_request_cta' as button_text %}

    {% include 'mails/_components/transaction-header.html' with utm='utm_campaign=transaction_order_placed' header=header_1 paragraph='' %}

    {% mail_paragraph '' join='lower' margin_bottom=0 font_size=17 font_height=24 paragraph_color='#7c7d81' paragraph_align='left'%}
    {% blocktrans with user=user_name %}mail_complaint_service_new_date_request_welcome_{{ user }}{% endblocktrans %}
    <br><br>
    {% trans 'mail_complaint_service_new_date_request_paragraph_1_1' %}
    <br><br>
    {% mail_button button_text url='complaint_service_new_date_request' service_id=service_id button_align='center' %}
    {% trans 'mail_complaint_service_new_date_request_paragraph_2_1' %}
    <br><br>
    {% trans 'mail_complaint_service_new_date_request_signature' %}
{% endblock %}

{% block unsubscribe %}
    {% include 'mails/_base_templates/_base_includes/_unsubscribe.html' %}
{% endblock %}
