{"asset": {"version": "2.0", "generator": "babylon.js glTF exporter for 3dsmax 2019 v20210212.2"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"mesh": 0, "rotation": [0.0, 0.0, -2.77555756e-17, 1.0], "name": "T03_handle_shadow"}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "TEXCOORD_0": 3, "TEXCOORD_1": 4}, "indices": 0}], "name": "T03_handle_shadow"}], "accessors": [{"bufferView": 0, "componentType": 5123, "count": 6, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "componentType": 5126, "count": 6, "max": [19.47773, 138.2282, 6.44464e-23], "min": [-19.47773, -138.2282, -6.44464e-23], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 72, "componentType": 5126, "count": 6, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "componentType": 5126, "count": 6, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 2, "byteOffset": 48, "componentType": 5126, "count": 6, "type": "VEC2", "name": "accessorUV2s"}], "bufferViews": [{"buffer": 0, "byteLength": 12, "name": "bufferViewScalar"}, {"buffer": 0, "byteOffset": 12, "byteLength": 144, "byteStride": 12, "name": "bufferViewFloatVec3"}, {"buffer": 0, "byteOffset": 156, "byteLength": 96, "byteStride": 8, "name": "bufferViewFloatVec2"}], "buffers": [{"uri": "T03_handle_shadow.bin", "byteLength": 252}]}