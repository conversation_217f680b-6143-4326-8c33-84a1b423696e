from django.contrib.auth.hashers import make_password
from django.utils import translation

import pytest

from custom.enums import LanguageEnum
from frontend_cms.utils import get_reversed_url_for_language
from frontend_cms.views.account import change_password


@pytest.mark.django_db
class TestChangePassword:
    def test_guest_cannot_reset_password(self, guest_user):
        assert not change_password(
            guest_user,
            {'password': 'testpassword', 'new_password': 'newpassword'},
        )

    def test_logged_in_user_can_change_password(self, user_factory):
        user = user_factory(password=make_password('testpassword'))
        assert change_password(
            user,
            {'password': 'testpassword', 'new_password': 'newpassword'},
        )

    def test_logged_in_user_cannot_change_password_with_wrong_old_password(
        self,
        user_factory,
    ):
        user = user_factory(password=make_password('testpassword'))
        assert not change_password(
            user,
            {'password': 'wrongpassword', 'new_password': 'newpassword'},
        )

    def test_change_password_for_user_from_password_reset_token(
        self,
        password_reset_token_factory,
        guest_user,
        user,
    ):
        """
        guest_user: the one who forgot the password to his actual
        account and requests password change.
        user: the actual user whose password is being changed.
        """
        reset_token = password_reset_token_factory(user=user)
        assert change_password(
            guest_user,
            {
                'password': 'testpassword',
                'new_password': 'newpassword',
                'token': reset_token.token,
            },
        )
        user.refresh_from_db()
        assert user.check_password('newpassword')


@pytest.mark.django_db
class TestGetReversedUrlForLanguage:
    @pytest.mark.parametrize(
        ('lang', 'expected'),
        (  # noqa: PT007
            (LanguageEnum.EN, '/en/shelves/bookcases/'),
            (LanguageEnum.DE, '/de/regale/bucherregale/'),
            (LanguageEnum.FR, '/fr/etageres/bibliotheques/'),
        ),
    )
    def test_returns_translated_urls(self, lang, expected):
        path = '/en/shelves/bookcases/'

        # In order to work properly, we need to have English language activated
        # for resolving a URL. When all tests are running, a different language might be
        # activated which results in failing tests.
        with translation.override(LanguageEnum.EN):
            reversed_url = get_reversed_url_for_language(path, lang)

        assert reversed_url == expected

    def test_returns_input_path_if_no_resolve_found(self):
        path = '/en/some_incorrect_path/'

        reversed_url = get_reversed_url_for_language(path, LanguageEnum.DE)

        assert path == reversed_url

    def test_current_language_doesnt_change_after_call(self):
        lang_before_call = translation.get_language()

        get_reversed_url_for_language('/shelves/', LanguageEnum.DE)

        assert translation.get_language() == lang_before_call
