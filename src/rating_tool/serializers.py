from typing import Dict

from rest_framework import serializers
from rest_framework.fields import ReadOnly<PERSON>ield
from rest_framework.relations import StringRelatedField
from rest_framework.serializers import ModelSerializer

from gallery import enums
from gallery.enums import FurnitureStatusEnum
from gallery.models import Jetty
from gallery.serializers import JettySerializer
from rating_tool.models import (
    Board,
    BoardCategory,
)


class JettyInContextSerializer(JettySerializer):
    region_price_display = serializers.SerializerMethodField()
    region_price = serializers.SerializerMethodField()
    currency_symbol = serializers.SerializerMethodField()

    def get_region_price(self, obj):
        region = self._get_region(obj)
        return str(
            obj.get_regionalized_price(region=region)
        )  # string, like in FurnitureSerializer

    def get_currency_symbol(self, obj):
        region = self.get_region()
        return region.currency.symbol

    def get_region_price_display(self, obj):
        region = self.get_region()
        return obj.get_regionalized_price_display(region=region)

    class Meta(JettySerializer.Meta):
        fields = JettySerializer.Meta.fields + (  # noqa: RUF005
            'region_price_display',
            'region_price',
            'currency_symbol',
        )


class BoardCategoryBoardSerializer(serializers.Serializer):
    id = serializers.IntegerField(label='ID', read_only=False)
    boards_data = serializers.JSONField(allow_null=True, required=False)

    class Meta:
        fields = ('id', 'boards_data')


class BoardCategorySerializer(ModelSerializer):
    enabled_regions = StringRelatedField(many=True)

    class Meta:
        model = BoardCategory
        fields = (
            'id',
            'name',
            'description',
            'for_board_grid',
            'enabled_for_test',
            'enabled_regions',
            'parent',
        )


class BoardCategoryDetailSerializer(ModelSerializer):
    enabled_regions = StringRelatedField(many=True)
    board_raw_data = ReadOnlyField(source='get_board_raw_data')

    class Meta:
        model = BoardCategory
        fields = (
            'id',
            'name',
            'description',
            'for_board_grid',
            'enabled_for_test',
            'enabled_regions',
            'parent',
            'board_raw_data',
        )


class BoardChangesSerializer(serializers.Serializer):
    board = serializers.CharField()
    position = serializers.IntegerField()
    id = serializers.IntegerField()
    shelf_type = serializers.IntegerField()
    material = serializers.IntegerField()
    copy_from_id = serializers.IntegerField(required=False)

    class Meta:
        fields = (
            'board',
            'position',
            'id',
            'shelf_type',
            'material',
            'copy_from_id',
        )


class BoardCategoryChangesSerializer(ModelSerializer):
    changes = BoardChangesSerializer(
        many=True,
        write_only=True,
    )
    saved = serializers.SerializerMethodField()

    def create(self, validated_data):
        incoming_changes = validated_data.pop('changes')
        user = validated_data.pop('user', None)
        new_category = BoardCategory.objects.create(**validated_data)
        validated_boards = self.validate_boards(
            incoming_changes,
            validated_data['parent'],
        )
        for change in incoming_changes:
            board = validated_boards[change['board']]
            if not new_category.boards.filter(filter_descr=change['board']).exists():
                board.pk = None
                board.category = new_category

            if 'copy_from_id' in change:
                new_shelf = Jetty.objects.get(pk=change['copy_from_id'])
                new_shelf.owner = user
                new_shelf.furniture_status = FurnitureStatusEnum.SPECIAL
                new_shelf.preset = True
            else:
                new_shelf = Jetty.objects.get(pk=change['id'])

            new_shelf.pk = None
            new_shelf.grid_all_colors = None
            new_shelf.grid_all_colors_webp = None
            new_shelf.preview = None
            new_shelf.material = change['material']
            # clear and recalculate geometry only for ROW configurator
            if new_shelf.configurator_type == enums.ConfiguratorTypeEnum.ROW:
                new_shelf.shelf_type = change['shelf_type']
                new_shelf.horizontals = []
            new_shelf.save()
            board.order[change['position']][0] = new_shelf.id
            board.order[change['position']][1] = change['material']
            board.save()
        return new_category

    @staticmethod
    def validate_boards(
        incoming_changes: list,
        parent: BoardCategory,
    ) -> Dict[str, Board]:
        """
        Iterate over all incoming changes and validate them before modifying boards
        """
        errors = []
        validated_boards = {}
        for incoming_change in incoming_changes:
            # TODO: add option to change position of USP, that is why its str not int
            board = parent.lookup_board_in_parent_tree(incoming_change['board'])
            if board:
                expected_jetty_id = int(incoming_change['id'])
                actual_jetty_id = board.order[incoming_change['position']][0]
            else:
                errors.append(
                    'Board {} missing from parent tree'.format(incoming_change['board'])
                )
                continue

            if expected_jetty_id != actual_jetty_id:
                errors.append(
                    '{} position/id mismatch. Position: {},should be'
                    ' (from changes): {}, is: {}'.format(
                        board.filter_descr,
                        incoming_change['position'],
                        expected_jetty_id,
                        actual_jetty_id,
                    )
                )

            if 'copy_from_id' in incoming_change:
                try:
                    Jetty.objects.get(pk=incoming_change['copy_from_id'])
                except Jetty.DoesNotExist:
                    errors.append(
                        'Jetty {} does not exist'.format(
                            incoming_change['copy_from_id'],
                        )
                    )
            validated_boards[board.filter_descr] = board

        if len(errors) > 0:
            raise serializers.ValidationError(
                {'detail': 'Errors: {}'.format(', '.join(errors))}
            )
        return validated_boards

    def get_saved(self, *args):
        if 'save' in self.context['request'].data:
            return self.context['request'].data['save']
        return False

    class Meta:
        model = BoardCategory
        fields = ('name', 'description', 'parent', 'changes', 'saved')
