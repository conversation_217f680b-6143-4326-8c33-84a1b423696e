import logging

from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from typing import (
    TYPE_CHECKING,
    Optional,
)

from django.utils import timezone
from django.utils.functional import cached_property

from custom.utils.decorators import (
    cache_model_method,
    clear_cache_for_kwargs,
)
from regions.cached_region import CachedRegionData
from regions.constants import (
    REGION_CACHE_PERIOD,
    REGION_RATE_CACHE_PERIOD,
)

if TYPE_CHECKING:
    from regions.models import Region
    from regions.types import RegionLikeObject

logger = logging.getLogger('cstm')


class RegionalizedMixin:
    @property
    def _region(self):
        if hasattr(self, 'region'):
            return self.region
        raise NotImplementedError

    def change_region(self, new_region):
        if hasattr(self, 'region'):
            self.region = new_region
            return self
        raise NotImplementedError

    @cache_model_method(cache_period=REGION_CACHE_PERIOD)
    def get_region(self, dont_use_cache=False):
        from regions.models import Region

        if self._region:
            return self._region
        return Region.get_other()

    def display_regionalized(self, value: Decimal | int | float) -> str:
        cached_region_data = self.get_region().cached_region_data
        return cached_region_data.get_format_price(value)

    def clear_methods_cache(self):
        clear_cache_for_kwargs(self, self.get_region)


class RateCopyOnSaveMixin(object):
    def save(self, *args, **kwargs):
        self_class = self.__class__
        try:
            if self.id:
                o = self_class.objects.get(pk=self.id)
                o_copy_dict = dict(  # noqa: C404
                    [
                        (f.name, getattr(o, f.name))
                        for f in self_class._meta.fields
                        if f.name not in ['id']
                    ]
                )
                o_copy = self_class(**o_copy_dict)
                o_copy.save_base()
            self.time = timezone.now()
        except self_class.DoesNotExist:
            logger.warning(
                'Trying to manually change the id? Alright. '
                'Take this sword and go! o--[=====/'
            )
        super(RateCopyOnSaveMixin, self).save(*args, **kwargs)


class RatedInTimeMixin(object):
    @cached_property
    @cache_model_method(cache_period=REGION_RATE_CACHE_PERIOD)
    def current_rate(self):
        assert hasattr(self, 'rates')
        rate = self.rates.first()
        if rate is None:
            rate = self.rates.model.objects.create(
                **{
                    'rate': 1,
                    list(self.rates.core_filters.keys())[0]: self,  # noqa: RUF015
                }
            )
        return rate


class RegionCalculationsObject(RegionalizedMixin):
    def __init__(self, region: Optional['RegionLikeObject'] = None) -> None:
        self.region = self.get_region(region)

        self.currency_rate = self.region.get_currency().current_rate.rate
        self.region_rate = self.region.current_rate.rate

    def get_region(self, region: Optional['RegionLikeObject'] = None) -> 'Region':
        from regions.models import Region

        if not region:
            return Region.get_other()
        elif isinstance(region, CachedRegionData):
            return Region.objects.get(id=region.id)

        return region

    def calculate_base(self, regionalized_value: Decimal | int | float) -> Decimal:
        value = Decimal(regionalized_value)
        result = value / self.currency_rate / self.region_rate
        return result.quantize(Decimal('1'), rounding=ROUND_HALF_UP)

    def calculate_regionalized(self, base_value: Decimal | int | float) -> Decimal:
        value = Decimal(base_value)
        result = value * self.currency_rate * self.region_rate
        return result.quantize(Decimal('1'), rounding=ROUND_HALF_UP)
