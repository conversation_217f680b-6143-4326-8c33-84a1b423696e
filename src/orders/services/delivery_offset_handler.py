from django.core.cache import cache

from ..internal_api.clients import ShippersApiClient
from .serializers import DeliveryOffsetSerialized

DEFAULT_DELIVERY_OFFSET_WEEKS: tuple[int, int] = (3, 3)


class DeliveryOffsetHandler:
    country: str
    postal_code: str
    delivery_data: list[dict]

    def __init__(self, country: str, postal_code: str) -> None:
        self.postal_code = postal_code
        self.country = country
        self.cache_key = 'delivery_regions_offsets'
        self.delivery_data = self.get_delivery_region_offset()

    def get_delivery_region_offset(self):
        region_offsets: list[dict] | None = cache.get(self.cache_key)

        if region_offsets:
            return region_offsets

        client = ShippersApiClient()
        delivery_data = client.get_delivery_region_info()

        if delivery_data:
            serializer = DeliveryOffsetSerialized(data=delivery_data, many=True)
            serializer.is_valid(raise_exception=True)
            region_offsets = serializer.data
        else:
            region_offsets = []
        cache.set(self.cache_key, region_offsets, timeout=60 * 60 * 24)

        return region_offsets

    def is_postal_code_excluded(
        self,
        postal_codes_excluded: list[str],
        postal_codes_prefixes_excluded: list[str],
    ) -> bool:
        if not self.postal_code:
            return False
        return self.postal_code in postal_codes_excluded or any(
            self.postal_code.startswith(prefix)
            for prefix in postal_codes_prefixes_excluded
        )

    def is_postal_code_included(self, postal_code_prefixes: list[str]) -> bool:
        if not self.postal_code:
            return False

        return any(
            self.postal_code.startswith(prefix) for prefix in postal_code_prefixes
        )

    def get_offset(self) -> tuple[int, int]:
        for region in self.delivery_data:
            if region['country'] == self.country:
                if self.is_postal_code_excluded(
                    region['postal_codes_excluded'],
                    region['postal_codes_prefixes_excluded'],
                ):
                    continue

                if region['whole_country'] or self.is_postal_code_included(
                    region['postal_codes_prefixes']
                ):
                    return (
                        region['delivery_offset_min'],
                        region['delivery_offset_max'],
                    )

        return DEFAULT_DELIVERY_OFFSET_WEEKS
