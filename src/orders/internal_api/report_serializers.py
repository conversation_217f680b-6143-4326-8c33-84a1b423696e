from django.contrib.auth import get_user_model
from rest_framework import serializers

from orders.models import Order
from user_profile.models import UserProfile

User = get_user_model()


class ForLogisticReportDetailedUserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserProfile
        fields = ['id', 'language', 'first_buy_date', 'get_registration_source_display']


class ForLogisticReportDetailedUserSerializer(serializers.ModelSerializer):
    profile = ForLogisticReportDetailedUserProfileSerializer(read_only=True)

    class Meta:
        model = User
        fields = [
            'id',
            'email',
            'username',
            'profile',
            'date_joined',
        ]


class ForLogisticReportDetailedOrderSerializer(serializers.ModelSerializer):
    owner = ForLogisticReportDetailedUserSerializer(read_only=True)

    class Meta:
        model = Order
        fields = [
            'id',
            'paid_at',
            'owner',
            'email',
            'get_all_address_data',
            'get_customer_as_string',
            'get_total_price',
            'get_sell_channel',
            'get_order_source_display',
            'lead_time',
            'get_base_total_value',
            'promo_text',
            'get_base_promo_amount',
        ]
