from checkout.enums import ViesValidationTriggerAction
from checkout.vat.validation import BaseVatValidator


class MockVatValidator(BaseVatValidator):
    APPROVED_VAT_NUMBERS = {
        'DE262231084',
        'GB548180433',
    }

    def __call__(
        self,
        value: str,
        validation_trigger_action: ViesValidationTriggerAction = ViesValidationTriggerAction.PAYMENT,  # noqa: E501
    ):
        return value in self.APPROVED_VAT_NUMBERS
