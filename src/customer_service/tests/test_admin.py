from decimal import Decimal

from django.contrib.admin import AdminSite
from django.utils import timezone

import pytest

from customer_service.admin import KlarnaAdjustmentAdmin
from customer_service.enums import KlarnaPriceChangeType
from customer_service.models import KlarnaAdjustment
from invoice.choices import InvoiceStatus


@pytest.mark.django_db
class TestKlarnaAdjustmentAdmin:
    @pytest.fixture(autouse=True)
    def setup(self, rf):
        self.klarna_admin = KlarnaAdjustmentAdmin(KlarnaAdjustment, AdminSite())

    @pytest.fixture
    def proforma_invoice(self, invoice_factory, order_factory, invoice_item_factory):
        invoice = invoice_factory(
            pretty_id='normal/1/2',
            status=InvoiceStatus.PROFORMA,
            order=order_factory(items=[]),
        )
        invoice_item_factory(gross_price=100, invoice=invoice, quantity=1)
        return invoice

    @pytest.mark.parametrize(
        ('change_type', 'amount', 'expected_new_price'),
        [
            (KlarnaPriceChangeType.DISCOUNT, '25.00', '75.00'),
            (KlarnaPriceChangeType.INCREASE, '50.00', '150.00'),
        ],
    )
    def test_new_price_calculations(
        self,
        proforma_invoice,
        klarna_adjustment_factory,
        change_type,
        amount,
        expected_new_price,
    ):
        obj = klarna_adjustment_factory(
            invoice=proforma_invoice,
            current_price=proforma_invoice.sum_invoice_items_gross_price(),
            change_type=change_type,
            amount=Decimal(amount),
            finished_at=timezone.now(),
        )
        new_price = self.klarna_admin.new_price(obj)
        assert expected_new_price in new_price
