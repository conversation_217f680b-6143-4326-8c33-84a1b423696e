from decimal import Decimal
from typing import TYPE_CHECKING

from django.db import transaction
from django.utils import timezone

from customer_service.correction_request_strategies import (
    CorrectionRequestQuantitySplitStrategy,
)
from customer_service.enums import (
    CSCorrectionRequestStatus,
    CSCorrectionRequestType,
)
from customer_service.models import CSCorrectionRequest
from invoice.choices import InvoiceStatus
from invoice.enums import InvoiceItemTag
from invoice.models import Invoice
from orders.internal_api.events import OrderRefreshEvent
from orders.models import OrderItem
from producers.choices import ProductStatus

if TYPE_CHECKING:
    from django.contrib.auth import get_user_model

    User = get_user_model()
    from gallery.models.furniture_abstract import FurnitureAbstract


class OrderItemByQuantitySplitter:
    def __init__(self, request_user: 'User', order_item: OrderItem) -> None:
        self.request_user = request_user
        self.source_order_item = order_item
        self.order = order_item.order

    @transaction.atomic
    def split_by_quantity(self, quantity_to_move: int) -> OrderItem:
        self.update_source_quantity(quantity_to_move)
        target_furniture_item = self.create_target_furniture_item()
        target_order_item = self.create_target_order_item(
            target_furniture_item, quantity_to_move
        )
        self.update_target_products(target_order_item, quantity_to_move)

        self.update_order()
        self.create_correction_and_invoice_or_proforma()
        return target_order_item

    def update_source_quantity(self, target_quantity: int) -> None:
        self.source_order_item.quantity -= target_quantity
        self.source_order_item.save(update_fields=['quantity'])

    def create_target_furniture_item(self) -> 'FurnitureAbstract':
        target_furniture_item = self.source_order_item.order_item
        target_furniture_item.pk = None
        target_furniture_item.save()
        return target_furniture_item

    def create_target_order_item(
        self,
        target_furniture_item: 'FurnitureAbstract',
        target_quantity: int,
    ) -> OrderItem:
        target_order_item = OrderItem.objects.get(pk=self.source_order_item.pk)
        target_order_item.pk = None
        target_order_item.order_item = target_furniture_item
        target_order_item.quantity = target_quantity
        target_order_item.save()
        return target_order_item

    def update_target_products(
        self,
        target_order_item: OrderItem,
        target_quantity: int,
    ) -> None:
        target_products = self.source_order_item.product_set.exclude(
            status=ProductStatus.ABORTED
        )
        for product in target_products[:target_quantity]:
            product.order_item = target_order_item
            product.save()

    def update_order(self) -> None:
        self.source_order_item.order.items_changed_at = timezone.now()
        self.source_order_item.order.save(update_fields=['items_changed_at'])
        OrderRefreshEvent(self.source_order_item.order)

    def create_correction_and_invoice_or_proforma(self) -> Invoice | None:
        if self.order.is_free() and not self.order.is_full_barter_deal():
            return None

        if self.order.is_klarna_payment():
            proforma = self.order.create_proforma()
            return proforma

        latest_invoice = (
            self.order.invoice_set.exclude(status=InvoiceStatus.PROFORMA)
            .order_by('-id')
            .first()
        )

        if latest_invoice:
            correction_request = CSCorrectionRequest.objects.create(
                issuer=self.request_user,
                status=CSCorrectionRequestStatus.STATUS_NEW,
                correction_amount_gross=Decimal('0.0'),
                correction_context='Split By Quantity',
                invoice=latest_invoice,
                tag=InvoiceItemTag.PRICE_INCREASE.value,
                type_cs=CSCorrectionRequestType.TYPE_QUANTITY_SPLIT,
            )
            strategy = CorrectionRequestQuantitySplitStrategy(correction_request)
            correction_request = strategy.prepare_correction_request()
            return correction_request.correction_invoice
