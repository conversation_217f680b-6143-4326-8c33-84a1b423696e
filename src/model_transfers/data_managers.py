import json
import os

from datetime import datetime
from itertools import chain

from django.apps import apps
from django.contrib.auth.models import (
    Group,
    User,
)
from django.contrib.contenttypes.models import ContentType
from django.core.files.storage import default_storage
from django.core.management import call_command
from django.core.management.sql import (
    emit_post_migrate_signal,
    emit_pre_migrate_signal,
)
from django.db import (
    DEFAULT_DB_ALIAS,
    connections,
    models,
)
from django.db.migrations.executor import MigrationExecutor
from django.db.migrations.state import ModelState
from django.db.models import (
    Q,
    QuerySet,
)
from django.utils import timezone

from fixture_magic.utils import (
    add_to_serialize_list,
    serialize_me,
)
from taggit.models import TaggedItem

from abtests.models import ABTest
from automatic_batching.models import ComplaintBatchingSetting
from catalogue.models import (
    BoardManualOrder,
    CatalogueEntry,
)
from complaints.models import (
    Area,
    Complaint,
    ReplaceableShelfFittingsStock,
    Responsibility,
    TypicalIssues,
)
from custom.enums import Furniture
from custom.logistic_enums import ServiceStatus
from custom.utils.signals import suppress_signals
from dynamic_delivery.models import (
    ManufactorCapacity,
    ShelfCapacity,
)
from gallery.enums import FurnitureCategory
from gallery.models import (
    Jetty,
    Sotty,
    Watty,
)
from invoice.models import (
    InvoiceItem,
    InvoiceSequence,
    SymfoniaFConfiguration,
)
from model_transfers.bases import BaseExportManager
from orders.enums import OrderType
from orders.models import OrderItem
from pricing_v3.models import (
    PricingHistoryEntry,
    PricingVersion,
    SamplePriceSettings,
)
from producers.models import (
    Manufactor,
    ManufactorAdditionalAccounts,
    Product,
)
from producers.models_split.product_batch_details import (
    ProductBatchDetailsJetty,
    ProductBatchDetailsWatty,
)
from producers.models_split.product_details import (
    ProductDetailsJetty,
    ProductDetailsWatty,
)
from production_margins.models import (
    CustomPricingFactor,
    ElementManagedInfo,
    MaterialManagementCost,
    PricingFactorItem,
    PricingFactors,
)
from regions.models import (
    Country,
    CountryRegionVat,
    CurrencyRate,
    RegionRate,
)
from reviews.choices import TemplateChoices
from reviews.models import (
    Review,
    ReviewPhotos,
    ReviewScore,
    ReviewTranslation,
)
from showrooms.models import ShowRoom
from user_consents.models import Cookie
from user_profile.choices import UserType
from user_profile.models import UserProfile
from warehouse.models import (
    SampleBoxVariant,
    StockSampleBox,
)

SHARED_APP_PATH = '/app/shared/logistic_orders.json'


class CstmExportManager(BaseExportManager):
    """
    products:
        last x products,
        watty products
        with assembly service
        with dedicated transport
    jetties:
        jetties for all products
        jetties for pricing
        jetties for boards
        jetties for catalogue
    sotties:
        sotties for all products
        sotties for boards
        sotties for catalogue
    watties:
        watties for all products
        watties for boards
        watties for catalogue
    product detail:
        product detail for jetty
        product detail for watty
    batch detail:
        batch detail for jetty
        batch detail for watty
    product planning data:
        product planning data
    users:
        admin users
        product users
    user profiles:
        manufactors
        order owners
        admins
    invoices items
        invoice items for all products
    catalogue
        catalogue entries
        board manual order
        catalogue tags
    Not related to products:
        review score
        countries
        active pricing factors
        all pricing factor items
        review photos
        review translations
        cookies
        active abtests
        replaceable shelf fittings stock
        typical production capacity
        symfonia f configurations
        custom pricing factors
        element managed info
        material management costs
        invoice sequences
        cmr contractors
        currency rate
        region rate
        delivery region
        shipper region conf
        Shelf Capacity
        Manufactor Capacity
        Complaint Typical Issues
        Complaint Responsibility
        Complaint Area
        pricing versions
        pricing history
        samples price settings
    """

    files_config = {
        'gallery.FurnitureGridImage': {
            'image': {'handler': 'model_transfers.image_handlers.CompressImageHandler'}
        },
        'gallery.FurnitureImage': {
            'image': {'handler': 'model_transfers.image_handlers.CompressImageHandler'},
            'image_webp': {
                'handler': 'model_transfers.image_handlers.CompressImageHandler'
            },
        },
        'gallery.Jetty': {
            'preview': {
                'handler': 'model_transfers.image_handlers.CompressImageHandler'
            },
        },
        'gallery.Sotty': {
            'preview': {
                'handler': 'model_transfers.image_handlers.CompressImageHandler'
            },
        },
        'gallery.Watty': {
            'preview': {
                'handler': 'model_transfers.image_handlers.CompressImageHandler'
            },
        },
        'reviews.ReviewPhotos': {
            'image': {'handler': 'model_transfers.image_handlers.CompressImageHandler'},
            'image_small_webp': {
                'handler': 'model_transfers.image_handlers.CompressImageHandler',
            },
            'image_medium_webp': {
                'handler': 'model_transfers.image_handlers.CompressImageHandler'
            },
        },
        'catalogue.CatalogueEntry': {
            'product_unreal_image_webp': {
                'handler': 'model_transfers.image_handlers.CompressImageHandler'
            },
            'product_unreal_thumbnail_image_webp': {
                'handler': 'model_transfers.image_handlers.CompressImageHandler'
            },
            'lifestyle_unreal_image_webp': {
                'handler': 'model_transfers.image_handlers.CompressImageHandler'
            },
            'real_lifestyle_image': {
                'handler': 'model_transfers.image_handlers.CompressImageHandler'
            },
            'real_lifestyle_image_webp': {
                'handler': 'model_transfers.image_handlers.CompressImageHandler'
            },
        },
    }

    def __init__(self, directory='cstm_data', file_name='data.json', size=1):
        print(
            f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] '
            f'Start init CstmExportManager'
        )
        self.size = size
        super().__init__(directory, file_name)

        self.reviews = self.get_reviews()
        self.products_count = self.get_products_count()
        self.products = self.get_products()
        self.complaints = self.get_complaints_for_products()
        self.products = self.get_products_for_logistic_orders()

        self.logistic_order_ids = (
            self.products.annotate(
                rel_lo=models.Func(
                    models.F('order__serialized_logistic_info'),
                    function='jsonb_array_elements',
                )
            )
            .annotate(related_logistic_order_id=models.F('rel_lo__id'))
            .values_list('related_logistic_order_id', flat=True)
        )

        with open(SHARED_APP_PATH, 'w') as outfile:
            outfile.write(json.dumps(list(self.logistic_order_ids)))

        self.catalogue_entries = self.get_catalogue_entries()
        self.board_manual_order = self.get_board_manual_order()
        self.jetties = self.get_jetties()
        self.sotties = self.get_sotties()
        self.watties = self.get_watties()
        self.admins = self.get_all_admins()
        self.manufactors = self.get_manufactors()
        self.manufactor_additional_accounts = self.get_manufactor_additional_accounts()
        print(
            f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] '
            f'End init CstmExportManager'
        )

    def serialize_data(self):
        add_to_serialize_list(self.products)
        add_to_serialize_list(self.complaints)
        add_to_serialize_list(self.catalogue_entries)
        add_to_serialize_list(self.board_manual_order)
        add_to_serialize_list(self.get_catalogue_entries_attributes())
        add_to_serialize_list(self.jetties)
        add_to_serialize_list(self.watties)
        add_to_serialize_list(self.sotties)
        add_to_serialize_list(self.get_product_details_jetty())
        add_to_serialize_list(self.get_product_details_watty())
        add_to_serialize_list(self.get_batch_details_jetty())
        add_to_serialize_list(self.get_batch_details_watty())
        add_to_serialize_list(self.get_product_invoice_items())
        add_to_serialize_list(self.manufactors)
        add_to_serialize_list(self.manufactor_additional_accounts)
        add_to_serialize_list(self.get_user_profiles())
        # Not related with product
        add_to_serialize_list(self.get_review_score())
        add_to_serialize_list(self.get_pricing_factors())
        add_to_serialize_list(self.get_pricing_factor_items())
        add_to_serialize_list(self.get_review_photos())
        add_to_serialize_list(self.get_review_translations())
        add_to_serialize_list(self.get_cookies())
        add_to_serialize_list(self.get_active_abtests())
        add_to_serialize_list(self.get_showrooms())

        add_to_serialize_list(self.get_replaceable_shelf_fittings_stocks())
        add_to_serialize_list(self.get_symfonia_f_configurations())
        add_to_serialize_list(self.get_custom_pricing_factors())
        add_to_serialize_list(self.get_element_managed_info())
        add_to_serialize_list(self.get_material_management_costs())
        add_to_serialize_list(self.get_invoice_sequences())
        add_to_serialize_list(self.get_countries())
        add_to_serialize_list(self.get_country_region_vats())
        add_to_serialize_list(self.get_pricing_versions())
        add_to_serialize_list(self.get_pricing_history())
        add_to_serialize_list(self.get_samples_settings())
        add_to_serialize_list(self.get_complaint_batching_settings())

        add_to_serialize_list(self.get_currency_rate())
        add_to_serialize_list(self.get_region_rate())

        add_to_serialize_list(self.get_sample_box_stocks())
        add_to_serialize_list(self.get_sample_box_variants())

        # LOGISTIC
        add_to_serialize_list(self.get_shelves_capacity())
        add_to_serialize_list(self.get_manufactors_capacity())
        add_to_serialize_list(self.get_typical_issues())
        add_to_serialize_list(self.get_responsibility())
        add_to_serialize_list(self.get_area())
        super().serialize_data()

    def get_products_count(self):
        return Product.objects.count()

    def get_last_products(self, amount=1000):
        amount *= self.size
        number_of_elements = min(self.products_count, amount)
        return (
            Product.objects.all()
            .order_by('-created_at')
            .order_by('-id')[:number_of_elements]
        )

    def get_watty_products(self, amount=100):
        number_of_elements = min(self.products_count, amount)
        return Product.objects.filter(
            cached_product_type=Furniture.watty.value
        ).order_by('-id')[:number_of_elements]

    def get_products_with_assembly_service(self, amount=40):
        return Product.objects.filter(
            order__serialized_logistic_info__0__assembly_service__status__in=[
                ServiceStatus.IN_PROGRESS.value,
                ServiceStatus.READY_TO_PROPOSE.value,
                ServiceStatus.PROPOSED_TO_CLIENT.value,
                ServiceStatus.ACCEPTED_BY_CLIENT.value,
            ]
        ).order_by('-id')[:amount]

    def get_products_with_dedicated_transport(self, amount=40):
        return Product.objects.filter(
            order__serialized_logistic_info__0__dedicated_transport__is_active=True
        ).order_by('-id')[:amount]

    def get_products_to_be_shipped(self, amount=150):
        return (
            Product.objects.exclude(
                order__order_type=OrderType.COMPLAINT,
            )
            .filter(order__serialized_logistic_info__0__to_be_shipped=True)
            .order_by('-id')[:amount]
        )

    def get_products_to_be_shipped_complaints(self, amount=40):
        return Product.objects.filter(
            order__serialized_logistic_info__0__to_be_shipped=True,
            order__order_type=OrderType.COMPLAINT,
            order__complaints__isnull=False,
        ).order_by('-id')[:amount]

    def get_products_for_complaints(self):
        order_ids = list(
            self.complaints.values_list('product__order_id', flat=True)
        ) + list(
            self.complaints.values_list('reproduction_product__order_id', flat=True)
        )
        return Product.objects.filter(order_id__in=order_ids).order_by('-id')

    def get_products_for_reviews(self):
        return Product.objects.filter(
            id__in=self.reviews.values_list('order__product'),
        )

    def get_products(self):
        last_products = self.get_last_products()
        watty_products = self.get_watty_products()
        assembly_service_products = self.get_products_with_assembly_service()
        dedicated_transport_products = self.get_products_with_dedicated_transport()
        to_be_shipped_products = self.get_products_to_be_shipped()
        to_be_shipped_complaints_products = self.get_products_to_be_shipped_complaints()
        reviews_products = self.get_products_for_reviews()

        return (
            last_products
            | watty_products
            | assembly_service_products
            | dedicated_transport_products
            | to_be_shipped_products
            | to_be_shipped_complaints_products
            | reviews_products
        )

    def get_products_for_logistic_orders(self):
        products = self.products | self.get_products_for_complaints()
        return Product.objects.filter(
            logistic_order__in=products.values_list('logistic_order', flat=True),
        )

    def get_complaints_for_products(self):
        product_complaint = Complaint.objects.filter(
            product__in=self.products.values_list('id', flat=True)
        )
        reproduction_product_complaint = Complaint.objects.filter(
            reproduction_product__in=self.products.values_list('id', flat=True)
        )
        return product_complaint | reproduction_product_complaint

    def get_jetties(self):
        products_jetties = self.get_jetties_for_products()
        pricing_jetties = self.get_jetties_for_pricing()
        catalogue_jetties = self.get_jetties_for_catalogue_entries()
        board_manual_order_jetties = self.get_jetties_for_board_manual_order()
        return (
            products_jetties
            | pricing_jetties
            | catalogue_jetties
            | board_manual_order_jetties
        )

    def get_jetties_for_products(self):
        jetties_ids = self.products.filter(
            cached_product_type=Furniture.jetty.value
        ).values_list('order_item__object_id')

        return Jetty.objects.filter(id__in=jetties_ids)

    def get_jetties_for_pricing(self):
        jetties = {
            'black_t01': chain(
                range(2776063, 2776079),
                range(2776085, 2776098),
                range(2776103, 2776111),
                range(2776116, 2776119),
                range(2776134, 2776146),
            ),
            'white_t01': range(2776152, 2776186),
            'gray_t01': range(2776232, 2776266),
            'red_t01': range(2776268, 2776302),
            'yellow_t01': range(2776303, 2776337),
            'pink_t01': range(2776343, 2776377),
            'white_t02': chain(
                range(2776731, 2776743),
                range(2776746, 2776747),
                range(2776749, 2776752),
                range(2776755, 2776765),
            ),
            'terracota_t02': range(2776780, 2776798),
            'blue_t02': range(2776801, 2776819),
            'sand_t02': range(2776822, 2776840),
            'mat_black_t02': range(2776863, 2776880),
        }
        jetties_ids = []
        for ids_range in jetties.values():
            jetties_ids.extend(list(ids_range))
        return Jetty.objects.filter(id__in=jetties_ids)

    def get_jetties_for_catalogue_entries(self):
        content_type = ContentType.objects.get_for_model(Jetty)
        furniture = self.catalogue_entries.values_list('object_id', 'content_type_id')
        jetties_ids = [
            jetty_id for jetty_id, ct_id in furniture if ct_id == content_type.id
        ]

        # It's not part of any board, but it's default Jetty which is displayed if
        # user has no permission for shelf he's trying to access
        jetties_ids.append(7645)

        return Jetty.objects.filter(id__in=jetties_ids)

    def get_jetties_for_board_manual_order(self):
        jetties_ids = self.board_manual_order.filter(
            entry__content_type=ContentType.objects.get_for_model(Jetty),
        ).values_list('entry__object_id', flat=True)
        return Jetty.objects.filter(id__in=jetties_ids)

    def get_sotties_for_catalogue_entries(self):
        content_type = ContentType.objects.get_for_model(Sotty)
        furniture = self.catalogue_entries.values_list('object_id', 'content_type_id')
        sotties_ids = [
            sotty_id for sotty_id, ct_id in furniture if ct_id == content_type.id
        ]
        return Sotty.objects.filter(id__in=sotties_ids)

    def get_sotties_for_board_manual_order(self):
        sotties_ids = self.board_manual_order.filter(
            entry__content_type=ContentType.objects.get_for_model(Sotty),
        ).values_list('entry__object_id', flat=True)
        return Sotty.objects.filter(id__in=sotties_ids)

    def get_sotties(self):
        catalogue_sotties = self.get_sotties_for_catalogue_entries()
        board_manual_order_sotties = self.get_sotties_for_board_manual_order()

        return catalogue_sotties | board_manual_order_sotties

    def get_watties_for_products(self):
        watties_ids = self.products.filter(
            cached_product_type=Furniture.watty.value
        ).values_list('order_item__object_id')
        return Watty.objects.filter(id__in=watties_ids)

    def get_watties_for_catalogue_entries(self):
        content_type = ContentType.objects.get_for_model(Watty)
        furniture = self.catalogue_entries.values_list('object_id', 'content_type_id')
        watties_ids = [
            watty_id for watty_id, ct_id in furniture if ct_id == content_type.id
        ]
        return Watty.objects.filter(id__in=watties_ids)

    def get_watties_for_board_manual_order(self):
        watties_ids = self.board_manual_order.filter(
            entry__content_type=ContentType.objects.get_for_model(Watty),
        ).values_list('entry__object_id', flat=True)
        return Watty.objects.filter(id__in=watties_ids)

    def get_watties(self):
        products_watties = self.get_watties_for_products()
        catalogue_watties = self.get_watties_for_catalogue_entries()
        board_manual_order_watties = self.get_watties_for_board_manual_order()

        return products_watties | catalogue_watties | board_manual_order_watties

    def get_product_details_jetty(self):
        return ProductDetailsJetty.objects.filter(product__in=self.products)

    def get_product_details_watty(self):
        return ProductDetailsWatty.objects.filter(product__in=self.products)

    def get_batch_details_jetty(self):
        batches_ids = self.products.values_list('batch_id', flat=True)
        return ProductBatchDetailsJetty.objects.filter(product_batch__in=batches_ids)

    def get_batch_details_watty(self):
        batches_ids = self.products.values_list('batch_id', flat=True)
        return ProductBatchDetailsWatty.objects.filter(product_batch__in=batches_ids)

    def get_catalogue_entries(self, amount_per_category=50):
        qs = CatalogueEntry.objects.select_related('content_type').filter(
            Q(profit_netto_category_order_de__lte=amount_per_category)
            | Q(profit_netto_order_de__lte=amount_per_category)
        )
        jetty_ids = set()
        sotty_ids = set()
        watty_ids = set()
        for entry in qs:
            if entry.content_type.model == Furniture.jetty.value:
                jetty_ids.update(obj['id'] for obj in entry.available_colors[:3])
            elif entry.content_type.model == Furniture.sotty.value:
                sotty_ids.update(obj['id'] for obj in entry.available_colors[:3])
            elif entry.content_type.model == Furniture.watty.value:
                watty_ids.update(obj['id'] for obj in entry.available_colors[:3])
        related_entries = CatalogueEntry.objects.filter(
            Q(content_type__model=Furniture.jetty.value, object_id__in=jetty_ids)
            | Q(content_type__model=Furniture.sotty.value, object_id__in=sotty_ids)
            | Q(content_type__model=Furniture.watty.value, object_id__in=watty_ids)
        )
        armrest_cover_preset_ids = (
            Sotty.objects.exclude(armrests=[])
            .filter(preset=True, covers_only=True, category=FurnitureCategory.COVER)
            .values_list('id', flat=True)
        )
        essentials_entries = CatalogueEntry.objects.filter(
            category=FurnitureCategory.COVER,
            content_type__model=Furniture.sotty.value,
            object_id__in=armrest_cover_preset_ids,
        )
        return qs | related_entries | essentials_entries

    def get_catalogue_entries_attributes(self):
        return TaggedItem.objects.filter(
            object_id__in=self.catalogue_entries.values_list('id', flat=True),
        )

    def get_board_manual_order(self):
        return BoardManualOrder.objects.all()

    def get_product_invoice_items(self):
        invoice_items_ids = self.products.values_list(
            'order__invoice__invoice_items', flat=True
        )
        return InvoiceItem.objects.filter(id__in=invoice_items_ids)

    # Objects without product relation
    def get_review_score(self):
        return ReviewScore.objects.all()

    def get_countries(self):
        return Country.objects.all()

    def get_country_region_vats(self):
        return CountryRegionVat.objects.all()

    def get_pricing_factors(self):
        return PricingFactors.objects.filter(active=True)

    def get_pricing_factor_items(self):
        return PricingFactorItem.objects.all()

    @staticmethod
    def get_reviews(amount=200):
        number_of_elements = min(Review.objects.count(), amount)
        return Review.latest_objects.filter(
            recommended_for_pdp=True,
            template_type__in=[TemplateChoices.SQUARE, TemplateChoices.VERTICAL],
        )[:number_of_elements]

    def get_review_photos(self) -> QuerySet[ReviewPhotos]:
        return ReviewPhotos.objects.filter(review__in=self.reviews)

    def get_review_translations(self) -> QuerySet[ReviewTranslation]:
        return ReviewTranslation.objects.filter(review__in=self.reviews)

    def get_cookies(self):
        return Cookie.objects.all()

    def get_active_abtests(self):
        return ABTest.objects.filter(active=True)

    def get_showrooms(self):
        return ShowRoom.objects.all()

    def get_replaceable_shelf_fittings_stocks(self):
        return ReplaceableShelfFittingsStock.objects.all()

    def get_symfonia_f_configurations(self):
        return SymfoniaFConfiguration.objects.all()

    def get_custom_pricing_factors(self):
        return CustomPricingFactor.objects.filter(
            date_to__gte=timezone.now().date(),
            date_from__lte=timezone.now().date(),
        )

    def get_element_managed_info(self):
        return ElementManagedInfo.objects.valid_for_today()

    def get_material_management_costs(self):
        return MaterialManagementCost.objects.filter(
            date_to__gte=timezone.now().date(),
            date_from__lte=timezone.now().date(),
        )

    def get_invoice_sequences(self):
        return InvoiceSequence.objects.all()

    def get_currency_rate(self):
        return CurrencyRate.objects.all()

    def get_region_rate(self):
        return RegionRate.objects.all()

    def get_sample_box_stocks(self):
        return StockSampleBox.objects.all()

    def get_sample_box_variants(self):
        return SampleBoxVariant.objects.all()

    def get_typical_issues(self):
        return TypicalIssues.objects.all()

    def get_responsibility(self):
        return Responsibility.objects.all()

    def get_area(self):
        return Area.objects.all()

    # Accounts
    def get_manufactors(self):
        return Manufactor.objects.all()

    def get_manufactor_additional_accounts(self):
        return ManufactorAdditionalAccounts.objects.all()

    def get_all_admins(self):
        return User.objects.filter(is_superuser=True)

    def get_user_profiles(self):
        admin_users_ids = list(self.admins.values_list('id', flat=True))
        order_users_ids = list(self.products.values_list('order__owner', flat=True))
        manufactors_user_ids = list(self.manufactors.values_list('owner', flat=True))
        manufactor_additional_accounts_ids = list(
            self.manufactor_additional_accounts.values_list('user', flat=True)
        )
        reviews_users_ids = list(self.reviews.values_list('owner', flat=True))
        reviews_orders_user_id = list(
            self.reviews.values_list('order__owner', flat=True)
        )
        watty_owners_ids = list(self.watties.values_list('owner', flat=True))
        jetty_owners_ids = list(self.jetties.values_list('owner', flat=True))

        return UserProfile.objects.filter(
            Q(
                user_id__in=(
                    admin_users_ids
                    + order_users_ids
                    + manufactors_user_ids
                    + reviews_users_ids
                    + reviews_orders_user_id
                    + watty_owners_ids
                    + jetty_owners_ids
                    + manufactor_additional_accounts_ids
                )
            )
            | Q(user_type__in=[UserType.RECOVERY_PRODUCER, UserType.WAREHOUSE_OPERATOR])
        ).distinct()

    def get_shelves_capacity(self):
        return ShelfCapacity.objects.all()

    def get_manufactors_capacity(self):
        return ManufactorCapacity.objects.all()

    def get_pricing_versions(self):
        return PricingVersion.objects.all()

    def get_pricing_history(self):
        return PricingHistoryEntry.objects.all()

    def get_samples_settings(self):
        return SamplePriceSettings.objects.all()

    def get_complaint_batching_settings(self):
        return ComplaintBatchingSetting.objects.all()


class MarginTestProductsExportManager(BaseExportManager):
    def __init__(
        self,
        date_from: datetime,
        date_to: datetime,
        directory: str = 'cstm_data',
        file_name: str = 'margins_products.json',
    ) -> None:
        super().__init__(directory, file_name)
        serialize_me.clear()
        self.products = self.get_margin_tests_products(date_from, date_to)
        self.order_items = self.get_order_items(self.products)

    def serialize_data(self) -> None:
        add_to_serialize_list(self.products)
        add_to_serialize_list(self.get_product_details_jetty(self.products))
        add_to_serialize_list(self.get_product_details_watty(self.products))
        add_to_serialize_list(self.get_batch_details_jetty(self.products))
        add_to_serialize_list(self.get_batch_details_watty(self.products))
        add_to_serialize_list(self.get_products_jetties())
        add_to_serialize_list(self.get_products_watties())
        super().serialize_data()

    def get_margin_tests_products(self, date_from, date_to) -> QuerySet[Product]:
        return Product.objects.filter(created_at__range=(date_from, date_to))

    def get_products_jetties(self) -> QuerySet[Jetty]:
        jetties = [
            product.order_item.order_item.id
            for product in self.products
            if product.cached_product_type == Furniture.jetty.value
        ]
        return Jetty.objects.filter(id__in=jetties)

    def get_products_watties(self) -> QuerySet[Watty]:
        watties = [
            product.order_item.order_item.id
            for product in self.products
            if product.cached_product_type == Furniture.watty.value
        ]
        return Watty.objects.filter(id__in=watties)

    def get_product_details_jetty(
        self,
        products: QuerySet[Product],
    ) -> QuerySet[ProductDetailsJetty]:
        return ProductDetailsJetty.objects.filter(product__in=products)

    def get_product_details_watty(
        self,
        products: QuerySet[Product],
    ) -> QuerySet[ProductDetailsWatty]:
        return ProductDetailsWatty.objects.filter(product__in=products)

    def get_order_items(
        self,
        products: QuerySet[Product],
    ) -> QuerySet[OrderItem]:
        return OrderItem.objects.filter(
            order__product__batch__in=[product.batch for product in products],
        )

    def get_batch_details_jetty(
        self,
        products: QuerySet[Product],
    ) -> QuerySet[ProductBatchDetailsJetty]:
        return ProductBatchDetailsJetty.objects.filter(
            product_batch__in=[product.batch for product in products]
        )

    def get_batch_details_watty(
        self,
        products: QuerySet[Product],
    ) -> QuerySet[ProductBatchDetailsWatty]:
        return ProductBatchDetailsWatty.objects.filter(
            product_batch__in=[product.batch for product in products]
        )


class CstmLoadManager:
    static_dir = 'static_files'
    migration_plan_file = 'migration_plan.json'

    def __init__(self, directory='fixtures', storage=None):
        super().__init__()
        self.directory = directory
        self.storage = storage or default_storage
        self.using = DEFAULT_DB_ALIAS
        self.verbosity = 1
        self.interactive = True
        self.app_label = None
        self.ignore = False
        self.excluded_apps = set()
        self.excluded_models = set()
        self.static_dir_path = os.path.join(self.directory, self.static_dir)

    def process_load(self, fixture_label, exclude=None):
        connection = connections[DEFAULT_DB_ALIAS]
        executor = MigrationExecutor(connection)
        targets = executor.loader.graph.leaf_nodes()
        plan = self.build_plan_from_file(executor, exclude)
        pre_migrate_state = executor._create_project_state(with_applied_migrations=True)
        pre_migrate_apps = pre_migrate_state.apps
        emit_pre_migrate_signal(
            self.verbosity,
            self.interactive,
            connection.alias,
            apps=pre_migrate_apps,
            plan=plan,
        )
        pre_migrate_state = executor._create_project_state(with_applied_migrations=True)
        post_migrate_state = executor.migrate(
            targets,
            plan=plan,
            state=pre_migrate_state.clone(),
            fake=False,
            fake_initial=False,
        )
        post_migrate_state.clear_delayed_apps_cache()
        post_migrate_apps = post_migrate_state.apps

        # Re-render models of real apps to include relationships now that
        # we've got a final state. This wouldn't be necessary if real apps
        # models were rendered with relationships in the first place.
        with post_migrate_apps.bulk_update():
            model_keys = []
            for model_state in post_migrate_apps.real_models:
                model_key = model_state.app_label, model_state.name_lower
                model_keys.append(model_key)
                post_migrate_apps.unregister_model(*model_key)
        post_migrate_apps.render_multiple(
            [ModelState.from_model(apps.get_model(*model)) for model in model_keys]
        )
        # TODO: Quick fix, find better solution or confirm it doesn't harm
        #       data structure
        ContentType.objects.all().delete()
        Group.objects.all().delete()
        # we need to load data before populating any records
        # i.e before post_migrate_signal)

        with suppress_signals():
            call_command('loaddata', fixture_label)
        emit_post_migrate_signal(
            self.verbosity,
            True,
            connection.alias,
            apps=post_migrate_apps,
            plan=plan,
        )

        self.populate_storage_files(self.static_dir_path)

    def populate_storage_files(self, path):
        for entry in os.listdir(path):
            current_path = os.path.join(path, entry)
            if os.path.isdir(current_path):
                self.populate_storage_files(current_path)
            else:
                with open(current_path, 'rb') as file:
                    storage_path = self.get_storage_path(current_path)
                    if not self.storage.exists(storage_path):
                        self.storage.save(storage_path, file)

    def get_storage_path(self, path):
        return path[len(self.static_dir_path) + 1 :]

    def build_plan_from_file(self, executor, exclude):
        with open(os.path.join(self.directory, self.migration_plan_file)) as json_file:
            migrations = json.load(json_file)
        return [
            (executor.loader.get_migration(*migration), False)
            for migration in migrations
            if migration[0] not in exclude
        ]
