import datetime

import pytest


@pytest.mark.django_db
class TestFreeReturn:
    def test_get_delivery_dates_should_return_empty_when_no_order_items(
        self,
        free_return,
    ):
        delivery_dates = free_return.get_delivery_dates()

        assert delivery_dates == '-'

    @pytest.mark.parametrize(
        ('delivered_dates', 'expected_get_delivery_dates'),
        [
            (
                [datetime.date(2021, 1, 1), datetime.date(2021, 1, 2)],
                {'2021-01-01', '2021-01-02'},
            ),
            ([datetime.date(2021, 1, 1), datetime.date(2021, 1, 1)], {'2021-01-01'}),
        ],
    )
    def test_get_delivery_dates_should_return_delivered_dates_from_logistic_order_when_exists(  # noqa: E501
        self,
        free_return,
        order_factory,
        order_item_factory,
        logistic_order_dto_factory,
        delivered_dates,
        expected_get_delivery_dates,
    ):
        first_order_with_delivered_logistic_order = order_factory(
            logistic_info=[
                logistic_order_dto_factory(delivered_date=delivered_dates[0])
            ]
        )
        order_item_factory(
            order=first_order_with_delivered_logistic_order,
            free_return=free_return,
        )

        second_order_with_delivered_logistic_order = order_factory(
            logistic_info=[
                logistic_order_dto_factory(
                    delivered_date=delivered_dates[1],
                )
            ]
        )

        order_item_factory(
            free_return=free_return,
            order=second_order_with_delivered_logistic_order,
        )

        delivery_dates = free_return.get_delivery_dates()

        assert delivery_dates == expected_get_delivery_dates

    def test_get_delivery_dates_should_return_squashed_nones_when_multiple_orders_without_delivered_logistic_order(  # noqa: E501
        self,
        free_return,
        order_factory,
        order_item_factory,
        logistic_order_dto_factory,
    ):
        order_without_logistic_order = order_factory()
        order_item_factory(order=order_without_logistic_order, free_return=free_return)

        order_with_not_delivered_logistic_order = order_factory(
            logistic_info=[logistic_order_dto_factory(delivered_date=None)]
        )
        order_item_factory(
            order=order_with_not_delivered_logistic_order,
            free_return=free_return,
        )

        order_with_delivered_logistic_order = order_factory(
            logistic_info=[
                logistic_order_dto_factory(delivered_date=datetime.date(2021, 1, 1))
            ]
        )

        order_item_factory(
            free_return=free_return,
            order=order_with_delivered_logistic_order,
        )

        delivery_dates = free_return.get_delivery_dates()

        assert delivery_dates == {'-', '2021-01-01'}
