from unittest.mock import patch

import pytest

from custom.enums import LanguageEnum
from orders.enums import OrderStatus
from reviews.tasks import (
    generate_review_translations,
    match_review_with_order_task,
)


@pytest.mark.django_db
class TestGlobalReviewScoreApiView:
    def test_match_review_with_order_task(
        self,
        user,
        review_factory,
        order_factory,
        order_item_factory,
    ):
        order = order_factory.create(owner=user, status=OrderStatus.DELIVERED)
        order_item = order_item_factory.create(order=order, content_type__model='jetty')

        review = review_factory(
            order=None,
            email=user.email,
            category=order_item.order_item.furniture_category,
        )

        match_review_with_order_task(review.id)
        review.refresh_from_db()
        assert review.order is not None

    def test_match_review_without_order(
        self,
        user,
        review_factory,
    ):
        review = review_factory.create(
            order=None,
            email=user.email,
        )

        match_review_with_order_task(review.id)
        review.refresh_from_db()
        assert review.order is None


class TestGenerateReviewTranslations:
    @patch(
        'reviews.tasks.translate_by_deepl',
        return_value={
            LanguageEnum.DE: 'Hallo Welt',
            LanguageEnum.FR: 'Bonjour le monde',
            LanguageEnum.ES: 'Hola Mundo',
            LanguageEnum.NL: 'Hallo Wereld',
            LanguageEnum.PL: 'Cześć Świat',
            LanguageEnum.IT: 'Ciao mondo',
            LanguageEnum.SV: 'Hej världen',
            LanguageEnum.DA: 'Hej verden',
            LanguageEnum.NO: 'Hallo verden',
        },
    )
    @patch('reviews.tasks.generate_review_translations')
    def test_generate_review_translations(self, _, mock, review_translation_factory):  # noqa: PT019
        review_translation = review_translation_factory(
            language=LanguageEnum.EN,
            is_original_language=True,
            title='Hello World',
            description='Hello World',
        )

        generate_review_translations(review_translation.id)

        translations = review_translation.review.translations.filter(
            is_original_language=False
        )
        assert translations.count() == 9
        for language in LanguageEnum:
            if language == LanguageEnum.EN:
                continue
            translation = translations.get(language=language)
            assert translation.title == mock.return_value[language]
            assert translation.description == mock.return_value[language]
