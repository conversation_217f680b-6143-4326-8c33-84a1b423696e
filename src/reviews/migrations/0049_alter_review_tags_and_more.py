# Generated by Django 4.2.23 on 2025-08-06 13:41

from django.db import migrations, models
import taggit.managers


class Migration(migrations.Migration):

    dependencies = [
        ('taggit', '0006_rename_taggeditem_content_type_object_id_taggit_tagg_content_8fc721_idx'),
        ('reviews', '0048_review_tags'),
    ]

    operations = [
        migrations.AlterField(
            model_name='review',
            name='tags',
            field=taggit.managers.TaggableManager(blank=True, help_text='A comma-separated list of tags.', through='taggit.TaggedItem', to='taggit.Tag', verbose_name='Tags'),
        ),
        migrations.AddConstraint(
            model_name='reviewtranslation',
            constraint=models.UniqueConstraint(condition=models.Q(('is_original_language', True)), fields=('review', 'is_original_language'), name='only_one_single_original_translation'),
        ),
        migrations.AddConstraint(
            model_name='reviewtranslation',
            constraint=models.UniqueConstraint(fields=('review', 'language'), name='only_one_translation_per_language'),
        ),
    ]
