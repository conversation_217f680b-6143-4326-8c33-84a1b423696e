from django.db import models
from django.utils.translation import gettext_lazy as _


class SymfoniaExportStatus(models.IntegerChoices):
    REVERTED = 10, 'Cofnięte'
    OK = 1, 'Ok'


class SymfoniaImportStatus(models.IntegerChoices):
    INIT = 1, 'Init'
    OK = 2, 'Ok'
    PARTIAL = 3, 'Partial'
    ERROR = 100, 'Error'


class InvoiceStatus(models.IntegerChoices):
    ENABLED = 0, _('Normal')
    ENABLED_VAT_REGION_CORRECTION = 7, _('Normal - region after correction')

    DISABLED = 1, _('Disabled')
    PROFORMA = 2, _('Pro forma')
    CANCELLED = 3, 'Cancelled'
    CORRECTING = 4, 'Correcting'

    ADDITIONAL_ES = 6, 'External ES(sumup,cash)'
    CORRECTING_DRAFT = 8, 'Correcting draft'
    CORRECTING_PREVIEW = 9, 'Correcting preview'
    CORRECTING_PROFORMA = 10, 'Correcting proforma'

    @classmethod
    def correcting_statuses(cls):
        return {
            cls.CORRECTING,
            cls.CORRECTING_DRAFT,
            cls.CORRECTING_PREVIEW,
            cls.CORRECTING_PROFORMA,
        }

    @classmethod
    def preview_statuses(cls):
        return {
            cls.CORRECTING_DRAFT,
            cls.CORRECTING_PREVIEW,
        }


class InvoiceSource(models.IntegerChoices):
    CSTM = 0, 'CSTM'
    LOGISTIC = 1, 'Logistic'
    RETOOL = 2, 'Retool'
    CUSTOMER_SERVICE = 3, 'Customer Service'
    LOGISTIC_FREE_MATERIAL_SAMPLE_OUTSIDE_EU = (
        4,
        'Logistic Free Material Sample Outside EU',
    )


class InvoiceItemType(models.IntegerChoices):
    ITEM = 0, _('Item')
    DELIVERY = 1, _('Delivery')
    ASSEMBLY = 2, _('Additional service')
    FAST_TRACK = 3, _('Fast track')
    SERVICE = 4, _('Service')


class VatType(models.IntegerChoices):
    NORMAL = 0, 'Normal VAT'
    EU = 1, 'VAT 0% - WDT'
    SWITZERLAND = 2, 'Switzerland VAT'


class InvoiceChangeScope(models.IntegerChoices):
    VAT_ID = 3, 'Customer VAT ID'
    EXCHANGE = 1, 'Exchange'
    EXCHANGE_DATE = 2, 'Exchange date'
    OTHER = 0, 'Other'
    ISSUE_DATE = 4, 'Change issue date'
    INVOICE_NUMBER = 5, 'Change invoice number'


class NumerationType(models.IntegerChoices):
    NORMAL = 0, 'Normal'  # Normal invoices - issued with Polish entity
    RV = 1, 'RV'  # Regional vat -- issued with regional entity
    VC = 2, 'VC'  # Vat corrections -- used when switching from normal to rv
    INV = 3, 'INV'  # UK
