{% load invoice_tags %}
<tr>
    <td>
        {{ invoice_item.item_name }}
        {% if invoice_item.item_type == 0 %}
            <br/>{{ invoice_item.item_material|default_if_none:"" }}
            <br/>{{ invoice_item.item_dimensions|default_if_none:"" }}
        {% endif %}
    </td>
    {% if invoice.delivery_address.country == 'united_kingdom' %}
        <td class="nowrap">
            <em>-</em>
        </td>
    {% endif %}
    <td class="nowrap">
        <em>{{ invoice_item.quantity }}</em>
    </td>
    <td class="nowrap">
        <em>{{ invoice_item.net_price }}{{ invoice.currency_symbol }}</em>
    </td>
    {% if invoice_object.get_promo_amount_number > 0 or invoice_object.is_correction %}
        <td class="nowrap">
            <em>{{ invoice_item.discount_value }}{{ invoice.currency_symbol }}</em>
        </td>
    {% endif %}
    <td class="nowrap">
        <em>{{ invoice_item.net_value }}{{ invoice.currency_symbol }}</em>
    </td>
    <td class="nowrap">
        <em>{% as_percentage invoice_item.vat_rate %}%</em>
    </td>
    <td class="nowrap">
        <em>{{ invoice_item.vat_amount }}{{ invoice.currency_symbol }}</em>
    </td>
    {% if invoice.delivery_address.outside_eu %}
        <td>{{ invoice_item.net_weight }} kg</td>
        <td>{{ invoice_item.gross_weight }} kg</td>
    {% endif %}
    <td class="nowrap">
        <em>{{ invoice_item.gross_price }}{{ invoice.currency_symbol }}</em>
    </td>
</tr>
