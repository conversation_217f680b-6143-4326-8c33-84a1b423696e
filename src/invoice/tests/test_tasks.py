import datetime

import pytest

from custom.enums import Furniture
from invoice.tasks import (
    LogisticOrderSentDeliveryDate,
    format_sent_to_customer_dates,
    group_logistic_order_dates_by_order_type,
)
from orders.enums import OrderStatus


@pytest.mark.django_db
class TestGenerateSalesReportAndSend:
    def test_group_logistic_order_dates_by_order_type_should_return_empty_when_no_logistic_orders(  # noqa: E501
        self, order
    ):
        logistic_order_dates_by_type = group_logistic_order_dates_by_order_type(
            [], order
        )

        assert logistic_order_dates_by_type == {}

    def test_group_logistic_order_dates_by_order_type_should_set_empty_order_type_when_default(  # noqa: E501
        self, logistic_order_dto_factory, order_factory
    ):
        order = order_factory(status=OrderStatus.DELIVERED.value)
        no_order_type_sent_to_customer = datetime.date(2021, 10, 1)
        no_order_type_delivered_date = datetime.date(2021, 10, 10)
        logistic_order_no_order_type = logistic_order_dto_factory(
            order_type='',
            sent_to_customer=no_order_type_sent_to_customer,
            delivered_date=no_order_type_delivered_date,
        )
        jetty_sent_to_customer = datetime.date(2021, 10, 2)
        jetty_delivered_date = datetime.date(2021, 10, 11)
        logistic_order_for_jetty = logistic_order_dto_factory(
            order_type=Furniture.jetty.value,
            sent_to_customer=jetty_sent_to_customer,
            delivered_date=jetty_delivered_date,
        )
        logistic_order_dates_by_type = group_logistic_order_dates_by_order_type(
            [logistic_order_no_order_type, logistic_order_for_jetty], order
        )

        assert logistic_order_dates_by_type == {
            '': LogisticOrderSentDeliveryDate(
                no_order_type_sent_to_customer, no_order_type_delivered_date
            ),
            Furniture.jetty.value: LogisticOrderSentDeliveryDate(
                jetty_sent_to_customer, jetty_delivered_date
            ),
        }

    def test_group_logistic_order_dates_by_order_type_should_return_dates_grouped_by_type_when_only_watty(  # noqa: E501
        self, logistic_order_dto_factory, order
    ):
        watty_sent_to_customer = datetime.date(2021, 10, 2)
        watty_delivered_date = datetime.date(2021, 10, 11)
        logistic_order_for_watty = logistic_order_dto_factory(
            order_type=Furniture.watty.value,
            sent_to_customer=watty_sent_to_customer,
            delivered_date=watty_delivered_date,
        )
        logistic_order_dates_by_type = group_logistic_order_dates_by_order_type(
            [logistic_order_for_watty], order
        )

        assert logistic_order_dates_by_type == {
            Furniture.watty.value: LogisticOrderSentDeliveryDate(
                watty_sent_to_customer, watty_delivered_date
            )
        }

    def test_group_logistic_order_dates_by_order_type_should_return_dates_grouped_by_type_when_all_order_types(  # noqa: E501
        self, logistic_order_dto_factory, order
    ):
        watty_sent_to_customer = datetime.date(2021, 10, 2)
        watty_delivered_date = datetime.date(2021, 10, 11)
        logistic_order_for_watty = logistic_order_dto_factory(
            order_type=Furniture.watty.value,
            sent_to_customer=watty_sent_to_customer,
            delivered_date=watty_delivered_date,
        )
        jetty_sent_to_customer = datetime.date(2021, 10, 3)
        jetty_delivered_date = datetime.date(2021, 10, 12)
        logistic_order_for_jetty = logistic_order_dto_factory(
            order_type=Furniture.jetty.value,
            sent_to_customer=jetty_sent_to_customer,
            delivered_date=jetty_delivered_date,
        )

        sample_box_sent_to_customer = datetime.date(2021, 10, 4)
        sample_box_delivered_date = datetime.date(2021, 10, 13)
        logistic_order_for_sample_box = logistic_order_dto_factory(
            order_type=Furniture.sample_box.value,
            sent_to_customer=sample_box_sent_to_customer,
            delivered_date=sample_box_delivered_date,
        )

        logistic_order_dates_by_type = group_logistic_order_dates_by_order_type(
            [
                logistic_order_for_watty,
                logistic_order_for_jetty,
                logistic_order_for_sample_box,
            ],
            order,
        )

        assert logistic_order_dates_by_type == {
            Furniture.watty.value: LogisticOrderSentDeliveryDate(
                watty_sent_to_customer, watty_delivered_date
            ),
            Furniture.jetty.value: LogisticOrderSentDeliveryDate(
                jetty_sent_to_customer, jetty_delivered_date
            ),
            Furniture.sample_box.value: LogisticOrderSentDeliveryDate(
                sample_box_sent_to_customer, sample_box_delivered_date
            ),
        }

    def test_format_sent_to_customer_dates_should_exclude_logistic_orders_when_no_sent_to_customer(  # noqa: E501
        self,
    ):
        jetty_sent_to_customer = datetime.date(2021, 10, 3)
        logistic_order_dates_by_type = {
            Furniture.watty.value: LogisticOrderSentDeliveryDate('', ''),
            Furniture.jetty.value: LogisticOrderSentDeliveryDate(
                jetty_sent_to_customer, ''
            ),
        }
        formatted_sent_to_customer = format_sent_to_customer_dates(
            logistic_order_dates_by_type
        )

        assert formatted_sent_to_customer == f'j:{jetty_sent_to_customer}'

    def test_format_sent_to_customer_dates_should_return_formatted_when_multiple_order_types(  # noqa: E501
        self,
    ):
        jetty_sent_to_customer = datetime.date(2021, 10, 3)
        watty_sent_to_customer = datetime.date(2021, 10, 4)
        logistic_order_dates_by_type = {
            Furniture.watty.value: LogisticOrderSentDeliveryDate(
                watty_sent_to_customer, ''
            ),
            Furniture.jetty.value: LogisticOrderSentDeliveryDate(
                jetty_sent_to_customer, ''
            ),
        }

        formatted_sent_to_customer = format_sent_to_customer_dates(
            logistic_order_dates_by_type
        )

        assert (
            formatted_sent_to_customer
            == f'w:{watty_sent_to_customer} j:{jetty_sent_to_customer}'
        )
