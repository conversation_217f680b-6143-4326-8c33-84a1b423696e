from dataclasses import asdict
from itertools import groupby

from django.utils import timezone

from celery import (
    chord,
    shared_task,
)

from automatic_batching.models import ComplaintBatchingSetting
from automatic_batching.services.complaint_batching import (
    ComplaintBatching<PERSON>ailer,
    ComplaintBatchingPlanner,
    ComplaintBatchPlan,
)
from custom.utils.slack import notify_about_automatic_batching_status
from producers.choices import (
    BatchStatus,
    BatchType,
)
from producers.models import (
    Manufactor,
    Product,
)
from producers.services import ProductBatchService


@shared_task
def auto_create_complaint_batches():
    """Automatically create complaint batches based on the configured settings."""
    planner = ComplaintBatchingPlanner()
    settings: ComplaintBatchingSetting = planner.settings
    now = timezone.now()

    if settings.last_run_started == settings.last_run:
        # batching is already in progress
        return
    if settings.last_run.date() == now.date():
        return
    if now.weekday() not in settings.batching_days:
        return
    if now.time() < settings.batching_time:
        return

    batch_plans = planner()
    if not batch_plans:
        notify_about_automatic_batching_status('<PERSON><PERSON> ma rekla<PERSON> do batchowania')
        return

    notify_about_automatic_batching_status('Batchuje automatycznie reklamacje')
    create_batches_tasks = [
        complaint_batches__create_batch.s(asdict(batch_plan))
        for batch_plan in batch_plans
    ]
    chord(create_batches_tasks, complaint_batches__mailer_finish.s()).apply_async()

    settings.last_run = now
    settings.last_run_started = now
    settings.save(update_fields=['last_run', 'last_run_started'])


@shared_task
def create_complaint_batches(product_ids):
    """Create complaint batches for the given product IDs from admin."""
    planner = ComplaintBatchingPlanner(product_ids=product_ids)
    batching_groups = planner()
    if not batching_groups:
        return

    notify_about_automatic_batching_status('Batchuje wybrane reklamacje')
    create_batches_tasks = [
        complaint_batches__create_batch.s(asdict(batching_group))
        for batching_group in batching_groups
    ]
    chord(create_batches_tasks, complaint_batches__no_mailer_finish.s()).apply_async()


@shared_task
def send_auto_email_with_files_to_manufactor(manufactor_id, batch_ids):
    """Send email with files to the manufactor after batch creation from admin."""
    mailer = ComplaintBatchingMailer()
    mailer.send(manufactor_id, batch_ids)


@shared_task
def complaint_batches__create_batch(batching_group_dict):
    """Subtask to create a complaint batch."""
    batching_group = ComplaintBatchPlan(**batching_group_dict)
    products = list(Product.objects.filter(id__in=batching_group.product_ids))
    manufactor = Manufactor.objects.get(id=batching_group.manufacturer)
    product_batch_service = ProductBatchService()
    batch = product_batch_service.create(
        products,
        manufactor,
        batch_type=BatchType.COMPLAINTS,
        batch_status=BatchStatus.IN_PRODUCTION,
    )
    return (batching_group.manufacturer, batch.id)


@shared_task
def complaint_batches__mailer_finish(created_batches: list[tuple[int, int]]):
    """Subtask to send email with files to the manufactor after batches creation."""
    mailer = ComplaintBatchingMailer()

    for manufacturer, batches in groupby(created_batches, key=lambda x: x[0]):
        batch_ids = [batch_id for _manufacturer, batch_id in batches]
        mailer.send(manufacturer, batch_ids)

    settings = mailer.settings
    settings.last_run = timezone.now()
    settings.save(update_fields=['last_run'])
    notify_about_automatic_batching_status(
        'Zakonczylem automatyczne batchowanie reklamacji'
    )


@shared_task
def complaint_batches__no_mailer_finish(*_args, **_kwargs):
    """Subtask to finish the complaint batches task from admin."""
    notify_about_automatic_batching_status(
        'Zakonczylem batchowanie wybranych reklamacji',
    )
