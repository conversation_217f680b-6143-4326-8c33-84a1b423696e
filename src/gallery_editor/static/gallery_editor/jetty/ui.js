import { thickness } from "../constants.js";
import {
  canRemoveVertical,
  getAllInsertsDepths,
  getDoorsCount,
  getDrawersCount,
  getGrommets,
  getHorizontalsInserts,
  getInsertsDepth,
  getRowHeight,
  hasBacks,
  hasDoors,
  hasDrawers,
  hasSupports,
} from './compartments.js';
import {
  insertsDepthOffset,
  minDoorWidth,
  rowHeights,
} from "./constants.js";
import { launchWebglRender } from "../render/launch_webgl_render.js";
import { launchFrontView } from "../render/launch_front_view.js";

export const rowHeightSelect = document.getElementById('rowHeight');
export const fillSelect = document.getElementById('fill');
export const orientationSelect = document.getElementById('orientation');
export const grommetSelect = document.getElementById('grommet');
export const horizontalInsertsSelect = document.getElementById('horizontalInserts');
export const insertsDepthSelect = document.getElementById('insertsDepth');
export const insertsDepthButton = document.getElementById('unifyInsertsDepth');
export const mirrorShelfButton = document.getElementById('mirrorShelf');
export const swapLegsButton = document.getElementById('swapLegs');
export const jettyInput = document.getElementById('jettyInput');
export const addHorizontalButton = document.getElementById('addHorizontal');
export const moveVerticalButton = document.getElementById('moveVertical');
export const addVerticalButton = document.getElementById('addVertical');
export const removeLeftVerticalButton = document.getElementById('removeLeftVertical');
export const removeRightVerticalButton = document.getElementById('removeRightVertical');
export const depthRange = document.getElementById('depthRange');
const addHorizontalSelect = document.getElementById('addHorizontalHeight');
const jettyForm = document.getElementById('jettyForm');
const depthRangeLabel = document.getElementById('depthRangeLabel');
const compartmentInfo = document.getElementById('compartment_info')
const saveButton = document.getElementById('saveOriginalObject')
const draftButton = document.getElementById('saveDraftObject')
const validateButton = document.getElementById('recalculatePrice')
const frontViewButton = document.getElementById('getFrontView')
const renderButton = document.getElementById('getImage')
const moveVerticalSelect = document.getElementById('moveVerticalChoice');
const moveVerticalRange = document.getElementById('moveVerticalRange');
const moveVerticalAppend = document.getElementById('moveVerticalAppend');

export const updateUi = (selectedCompartment, validationNeeded) => {
  if (validationNeeded) {
    saveButton.className = 'btn btn-secondary';
    draftButton.className = 'btn btn-secondary';
    validateButton.className = 'btn btn-warning';
    draftButton.title = 'Please validate before saving.'
  }
  updateLegsSwapButton(swapLegsButton, jetty);
  if (selectedCompartment) {
    updateRowHeightSelect(rowHeightSelect, selectedCompartment);
    updateAddHorizontalSelect(addHorizontalSelect, selectedCompartment);
    updateAddHorizontalButton(addHorizontalButton, selectedCompartment);
    updateVerticalButtons(addVerticalButton, removeLeftVerticalButton, removeRightVerticalButton, selectedCompartment);
    updateMoveVerticalGroup(selectedCompartment);
    updateFillTypeSelect(fillSelect, selectedCompartment);
    updateOrientationSelect(orientationSelect, selectedCompartment);
    updateGrommetTypeSelect(grommetSelect, selectedCompartment);
    updateHorizontalInsertsSelect(horizontalInsertsSelect, selectedCompartment);
    updateInsertsDepthSelect(insertsDepthSelect, insertsDepthButton, selectedCompartment, jetty);
    let width = selectedCompartment.x2 - selectedCompartment.x1
    let height = selectedCompartment.y2 - selectedCompartment.y1
    compartmentInfo.innerHTML =  `Dimensions: ${width}x${height} [mm]`;
  } else {
    fillSelect.style.display = 'none';
    addHorizontalSelect.style.display = 'none';
    addHorizontalButton.style.display = 'none';
    moveVerticalSelect.style.display = 'none';
    moveVerticalAppend.style.display = 'none';
    moveVerticalRange.style.display = 'none';
    moveVerticalButton.style.display = 'none';
    orientationSelect.style.display = 'none';
    rowHeightSelect.style.display = 'none';
    grommetSelect.style.display = 'none';
    horizontalInsertsSelect.style.display = 'none';
    insertsDepthSelect.style.display = 'none';
    insertsDepthButton.style.display = 'none';
    addVerticalButton.style.display = 'none';
    removeLeftVerticalButton.style.display = 'none';
    removeRightVerticalButton.style.display = 'none';
    compartmentInfo.innerHTML = '';
  }
}
export const updateDepthRange = (depthRange, value) => {
  depthRangeLabel.innerHTML = 'Depth: ' + value + ' [cm]';
  depthRange.value = value;
}

const updateRowHeightSelect = (rowHeightSelect, selectedCompartment) => {
  rowHeightSelect.style.display = 'inline-block';
  let rowHeight = selectedCompartment.double ? 0 : getRowHeight(selectedCompartment);
  rowHeightSelect.value = rowHeight;
  [...rowHeightSelect.options].forEach(option => {
    option.disabled = !!rowHeight !== !!parseInt(option.value);
  });
}

const updateAddHorizontalSelect = (addHorizontalSelect, selectedCompartment) => {
  let rowHeight = getRowHeight(selectedCompartment);
  if (rowHeight < rowHeights.C) {
    addHorizontalSelect.style.display = 'none';
    return;
  } else
    addHorizontalSelect.style.display = 'inline-block';
  addHorizontalSelect.value = '';
  [...addHorizontalSelect.options].forEach(option => {
    if (!option.value) return;
    let selectedHeight = rowHeights[option.value];
    let remainingHeight = rowHeight - selectedHeight - thickness;
    option.hidden = remainingHeight < rowHeights.A;
    option.innerText = (
      `${option.value}: ${selectedHeight} mm + ${remainingHeight} mm`
    );
  });
}

depthRange.oninput = (e) => {
  depthRangeLabel.innerHTML = 'Depth: ' + depthRange.value + ' [cm]';
}

addHorizontalSelect.onchange = (e) => {
  addHorizontalButton.disabled = false;
  // store the chosen value in the action button for convenience
  addHorizontalButton.value = rowHeights[addHorizontalSelect.value];
}

moveVerticalRange.onchange = () => {
  // store the chosen value in the action button for convenience
  moveVerticalButton.value = moveVerticalRange.value;
}

const updateMoveVerticalGroup = () => {
  moveVerticalSelect.style.display = 'inline-block';
  moveVerticalAppend.style.display = 'inline-block';
  moveVerticalRange.style.display = 'inline-block';
  moveVerticalButton.style.display = 'inline-block';
  moveVerticalSelect.value = '';
  moveVerticalRange.disabled = true;
  moveVerticalRange.value = 0;
  moveVerticalButton.innerText = 'Move vertical';
  moveVerticalButton.disabled = true;
}

moveVerticalSelect.onchange = (e) => {
  moveVerticalRange.disabled = false;
  moveVerticalButton.disabled = false;
  // store the chosen value in the action button for convenience
  moveVerticalButton.side = moveVerticalSelect.value;
}

frontViewButton.onclick = (e) => {
  e.preventDefault();
  launchFrontView(jettyForm, jetty, frontViewButton, 'jetty');
}

renderButton.onclick = (e) => {
  e.preventDefault();
  launchWebglRender(jettyForm, jetty, 'jetty', []);
}

const updateAddHorizontalButton = (addHorizontalButton, selectedCompartment) => {
  let rowHeight = getRowHeight(selectedCompartment);
  addHorizontalButton.disabled = true
  addHorizontalButton.style.display = rowHeight >= 382 ? 'inline-block': 'none';
}

const updateVerticalButtons = (
  addVerticalButton,
  removeLeftVerticalButton,
  removeRightVerticalButton,
  selectedCompartment,
) => {
  removeLeftVerticalButton.style.display = 'inline-block';
  removeRightVerticalButton.style.display = 'inline-block';
  addVerticalButton.style.display = 'inline-block';
  if (canRemoveVertical(jetty, selectedCompartment, 'left')) {
    removeLeftVerticalButton.className = 'btn btn-secondary';
    removeLeftVerticalButton.disabled = false;
  } else {
    removeLeftVerticalButton.className = 'btn btn-disabled';
    removeLeftVerticalButton.disabled = true;
  }

  if (canRemoveVertical(jetty, selectedCompartment, 'right')) {
    removeRightVerticalButton.className = 'btn btn-secondary';
    removeRightVerticalButton.disabled = false;
  } else {
    removeRightVerticalButton.className = 'btn btn-disabled';
    removeRightVerticalButton.disabled = true;
  }
  let isWideEnough = selectedCompartment.x2 - selectedCompartment.x1 >= minDoorWidth * 2;
  if (isWideEnough) {
    addVerticalButton.className = 'btn btn-secondary';
    addVerticalButton.disabled = false;
  } else {
    addVerticalButton.className = 'btn btn-disabled';
    addVerticalButton.disabled = true;
  }
}

const updateFillTypeSelect = (fillTypeSelect, selectedCompartment) => {
  fillSelect.style.display = 'inline-block'
  let fillType = 'none';
  let doorsCount = getDoorsCount(selectedCompartment);
  let drawersCount = getDrawersCount(selectedCompartment);

  if (doorsCount === 1)
    fillType = 'door';
  else if (doorsCount === 2)
    fillType = 'doubleDoor';
  else if (drawersCount === 1)
    fillType = 'drawer'
  else if (drawersCount === 2)
    fillType = 'doubleDrawer';
  else if (hasBacks(selectedCompartment))
    fillType = 'back';
  else if (hasSupports(selectedCompartment))
    fillType = 'support';
  fillTypeSelect.value = fillType;

  [...fillTypeSelect.options].find(o => o.value === 'doubleDoor')
    .disabled = ((selectedCompartment.x2 - selectedCompartment.x1) < 2 * minDoorWidth);
  // we only allow double drawers in row height C
  [...fillTypeSelect.options].find(o => o.value === 'doubleDrawer')
    .disabled = getRowHeight(selectedCompartment) !== rowHeights.C;
}

const updateOrientationSelect = (orientationSelect, selectedCompartment) => {
  if (hasDoors(selectedCompartment) || hasDrawers(selectedCompartment)) {
    let doorsAndDrawers = selectedCompartment.doors.concat(selectedCompartment.drawers)
    orientationSelect.style.display = 'inline-block';
    let fill = doorsAndDrawers.find(fill => fill.handle === 0); // select main door
    if (fill === undefined) fill = doorsAndDrawers[0]; // fallback for drawers
    if (fill.direction === 1 || fill.flip === 1)
      orientationSelect.value = 'left';
    else
      orientationSelect.value = 'right';
  } else if (hasSupports(selectedCompartment)) {
    orientationSelect.style.display = 'inline-block';
    let support = selectedCompartment.supports[0]
    if (Math.min(support.x1, support.x2) === selectedCompartment.x1)
      orientationSelect.value = 'left';
    else
      orientationSelect.value = 'right';
  } else
    orientationSelect.style.display = 'none';
}

const updateGrommetTypeSelect = (grommetSelect, selectedCompartment) => {
  if (hasBacks(selectedCompartment))
    grommetSelect.style.display = 'inline-block';
  else
    grommetSelect.style.display = 'none';
  let grommetPositions = getGrommets(selectedCompartment);
  [...grommetSelect.options].forEach(option => {
    let optionHeight = parseInt(option.value)
    option.hidden = !(selectedCompartment.y1 + optionHeight <= selectedCompartment.y2 - 50);
    option.selected = !!grommetPositions.includes(optionHeight);
  })
}

const updateHorizontalInsertsSelect = (horizontalInsertsSelect, selectedCompartment) => {
  if (!hasDrawers(selectedCompartment) || getHorizontalsInserts(selectedCompartment).length)
    horizontalInsertsSelect.style.display = 'inline-block';
  let insertsPositions = getHorizontalsInserts(selectedCompartment);
  [...horizontalInsertsSelect.options].forEach(option => {
    let optionHeight = parseInt(option.value)
    option.hidden = !(selectedCompartment.y1 + optionHeight <= selectedCompartment.y2 - 82);
    option.selected = !!insertsPositions.includes(optionHeight);
  })
}


const updateInsertsDepthSelect = (insertsDepthSelect, insertsDepthButton, selectedCompartment, jetty) => {
  if (!hasDrawers(selectedCompartment)) {
    insertsDepthSelect.style.display = 'inline-block';
    insertsDepthButton.style.display = 'inline-block';
  } else {
    insertsDepthSelect.style.display = 'none';
    insertsDepthButton.style.display = 'none';
  }

  let insertDepth = getInsertsDepth(selectedCompartment);
  if (insertDepth === insertsDepthOffset['full-depth'])
    insertsDepthSelect.value = 'full-depth';
  else if (insertDepth === insertsDepthOffset['standard'])
    insertsDepthSelect.value = 'standard';
  else
    insertsDepthSelect.value = '';

  let allInsertsDepths = getAllInsertsDepths(jetty);
  if (allInsertsDepths.length > 1 && insertsDepthSelect.value !== '') {
    insertsDepthButton.disabled = false;
    insertsDepthButton.className = 'btn btn-warning';
  } else {
    insertsDepthButton.disabled = true;
    insertsDepthButton.className = 'btn btn-secondary';
  }
};

const updateLegsSwapButton = (legsSwapButton, jetty) => {
  if (jetty.plinth.find(p => !p.removed))
    legsSwapButton.style.display = 'none';
  else if (jetty.long_legs.find(p => !p.removed))
    legsSwapButton.innerText = 'Remove long legs'
  else if (jetty.legs.find(p => !p.removed))
    legsSwapButton.innerText = 'Add long legs'
}
