import { shelfTypeValues, thickness } from "../constants.js";

const padding = 25;

const getFillColor = (object, defaultColor) => {
  if (object.wasAdded) return '#00ff00';
  if (object.wasDeleted) return '#ff0000';
  if (object.wasChanged) return '#ffff00';
  if (object.wasMoved) return '#00ffff';
  if (object.hasChangedFittings) return '#ff9900';
  return defaultColor;
}

export const drawHorizontal = (horizontal, drawing, offsetX) => {
  if (horizontal.drawing) horizontal.drawing.remove();
  if (horizontal.removed) return;
  let horizontalAxis = (horizontal.y1 + horizontal.y2) / 2
  horizontal.drawing = drawing.rect(horizontal.x2 - horizontal.x1, thickness)
    .move(offsetX + horizontal.x1, horizontalAxis - thickness / 2)
    .fill({ color: getFillColor(horizontal, '#1e2170') })
    .stroke({ color: '#d8f2ff', opacity: 0.6, width: 5 });
}

export const drawVertical = (vertical, drawing, offsetX) => {
  if (vertical.drawing) vertical.drawing.remove();
  if (vertical.removed) return;
  let verticalAxis = (vertical.x1 + vertical.x2) / 2
  vertical.drawing = drawing.rect(thickness, vertical.y2 - vertical.y1)
    .move(offsetX + verticalAxis - thickness / 2, vertical.y1)
    .fill({ color: getFillColor(vertical, '#1484c5') })
    .stroke({ color: '#b3ddff', opacity: 0.6, width: 5 });
}

export const drawPlinthZ = (plinth, drawing, offsetX) => {
  if (plinth.drawing) plinth.drawing.remove();
  if (plinth.removed) return;
  plinth.drawing = drawing.rect(thickness, plinth.y2 - plinth.y1)
    .move(offsetX + plinth.x1 - thickness / 2, plinth.y1)
    .fill({ color: '#039898', opacity: 0.8 })
    .stroke({ color: '#0c727c', opacity: 0.6, width: 5 });
}

export const drawPlinthX = (plinth, drawing, offsetX) => {
  if (plinth.drawing) plinth.drawing.remove();
  if (plinth.removed) return;
  let x1 = Math.min(plinth.x1, plinth.x2)
  let x2 = Math.max(plinth.x1, plinth.x2)
  plinth.drawing = drawing.rect(x2 - x1, plinth.y2 - plinth.y1)
    .move(offsetX + x1, plinth.y1)
    .fill({ color: getFillColor(plinth, '#0c627c'), opacity: 0.8 })
    .stroke({ color: '#0b7f8f', opacity: 0.6, width: 5 });
}

export const drawLeg = (leg, drawing, offsetX) => {
  if (leg.drawing) leg.drawing.remove();
  if (leg.removed) return;
  let legRadius = 5;
  let x = (leg.x1 + leg.x2) / 2;
  leg.drawing = drawing.rect(legRadius * 2, leg.y2 - leg.y1)
    .move(offsetX + x - legRadius, leg.y1)
    .fill({ color: getFillColor(leg, '#052631'), opacity: 0.8 })
    .stroke({ color: '#424242', opacity: 0.6, width: 5 });
}

export const drawLongLeg = (leg, drawing, offsetX) => {
  if (leg.drawing) leg.drawing.remove();
  if (leg.removed) return;
  let legRadius = 5;
  let baseH = 10
  let x = (leg.x1 + leg.x2) / 2;
  let h = leg.y2 - leg.y1;
  leg.drawing = drawing.group()
    .fill({ color: getFillColor(leg, '#0c627c'), opacity: 0.8 })
    .stroke({ color: '#0b7f8f', opacity: 0.6, width: 5 });
  switch (leg.rotation_z) {
    case 0:
    case 90:
      leg.drawing.rect(30, baseH).move(offsetX + x, leg.y2 - baseH);
      break;
    case 135:
    case 315:
      leg.drawing.rect(40, baseH).move(offsetX + x - 20, leg.y2 - baseH);
      break;
    case 180:
    case 270:
      leg.drawing.rect(30, baseH).move(offsetX + x - 30, leg.y2 - baseH);
      break;
  }
  leg.drawing.rect(legRadius * 2, h).move(offsetX + x - legRadius, leg.y1);
}

export const drawBackOrSupport = (back, drawing, offsetX) => {
  if (back.drawing) back.drawing.remove();
  if (back.removed) return;
  let x1 = Math.min(back.x1, back.x2)
  let x2 = Math.max(back.x1, back.x2)
  back.drawing = drawing.rect(x2 - x1, back.y2 - back.y1)
    .move(offsetX + x1, back.y1)
    .fill({ color: getFillColor(back, '#c0ced0'), opacity: 1 })
    .stroke({ color: '#4c5a64', opacity: 0.6, width: 5 });
}

export const drawInsert = (insert, drawing, offsetX, jetty_depth) => {
  if (insert.drawing) insert.drawing.remove();
  if (insert.removed) return;

  let insertColor = '#9507ab';
  if (insert.subtype === 't') {
    insertColor = '#4800ff';
  } else if (insert.z2 === jetty_depth) {
    insertColor = '#cf0076';
  }

  insert.drawing = drawing.rect(insert.x2 - insert.x1, insert.y2 - insert.y1)
    .move(offsetX + insert.x1, insert.y1)
    .fill({ color: getFillColor(insert, insertColor) })
    .stroke({ color: '#d5b9ff', opacity: 0.6, width: 5 });
}

export const drawGrommet = (grommet, drawing, offsetX) => {
  if (grommet.drawing) grommet.drawing.remove();
  if (grommet.removed) return;
  let grommetR = 60
  let grommetXAxis = (grommet.x1 + grommet.x2) / 2
  let grommetYAxis = (grommet.y1 + grommet.y2) / 2
  if (grommet.subtype === 'h') {
    grommet.drawing = drawing.rect(grommetR, thickness)
      .move(offsetX + grommetXAxis - grommetR / 2, grommetYAxis - thickness / 2)
      .fill({ color: getFillColor(grommet, '#74beb6') })
      .stroke({ color: '#0b6445', opacity: 0.6, width: 5 });
  } else {
    grommet.drawing = drawing.circle(grommetR)
      .move(offsetX + grommetXAxis - grommetR / 2, grommetYAxis - grommetR / 2)
      .fill({ color: getFillColor(grommet, '#74beb6') })
      .stroke({ color: '#0b6445', opacity: 0.6, width: 5 });
  }
}

export const drawDoor = (door, drawing, offsetX, shelfType) => {
  if (door.drawing) door.drawing.remove();
  if (door.removed) return;
  door.drawing = drawing.group();
  door.drawing
    .rect(door.x2 - door.x1 - padding * 2, door.y2 - door.y1 - padding * 2)
    .move(offsetX + door.x1 + padding, door.y1 + padding)
    .fill({ color: getFillColor(door, '#1e6970'), opacity: 0.1 })
    .stroke({ color: '#1e7066', opacity: 0.6, width: 5 });
  let lineX1 = door.x2 - padding;
  let lineX2 = door.x1 + padding;
  if (door.flip === 1 || door.direction === 1) {
    lineX1 = door.x1 + padding;
    lineX2 = door.x2 - padding;
  }
  door.drawing
    .polyline([
      [offsetX + lineX1, door.y1 + padding],
      [offsetX + lineX2, (door.y1 + door.y2) / 2],
      [offsetX + lineX1, door.y2 - padding],
    ])
    .fill('none')
    .stroke({ color: '#1e7066', opacity: 0.6, width: 5 });
  switch (shelfType) {
    case shelfTypeValues.TYPE_01:
      return drawType01DoorHandle(door, offsetX);
    case shelfTypeValues.TYPE_02:
      return drawType02Handle(door, offsetX);
    case shelfTypeValues.TYPE_01v:
      return drawType01vHandle(door, offsetX);
  }
}

export const drawDrawer = (drawer, drawing, offsetX, shelfType) => {
  if (drawer.drawing) drawer.drawing.remove();
  if (drawer.removed) return;
  drawer.drawing = drawing.group();
  drawer.drawing
    .rect(drawer.x2 - drawer.x1 - padding * 2, drawer.y2 - drawer.y1 - padding * 2)
    .move(offsetX + drawer.x1 + padding, drawer.y1 + padding)
    .fill({ color: getFillColor(drawer, '#591e70'), opacity: 0.1 })
    .stroke({ color: '#481e70', opacity: 0.6, width: 5 });
  drawer.drawing
    .polyline([
      [offsetX + padding + drawer.x1, drawer.y1 + padding],
      [offsetX + (drawer.x1 + drawer.x2) / 2, drawer.y2 - padding],
      [offsetX - padding + drawer.x2, drawer.y1 + padding],
    ])
    .fill('none')
    .stroke({ color: '#1e7066', opacity: 0.6, width: 5 });
  switch (shelfType) {
    case shelfTypeValues.TYPE_01: return drawType01vHandle(drawer, offsetX);
    case shelfTypeValues.TYPE_02: return drawType02Handle(drawer, offsetX);
    case shelfTypeValues.TYPE_01v: return drawType01vHandle(drawer, offsetX);
  }
}

const drawHandle = (element, handleXSize, handleYSize, handleX1, handleY1) => {
  element.drawing
    .rect(handleXSize, handleYSize)
    .move(handleX1, handleY1)
    .fill({ color: '#613dc9', opacity: 0.1 })
    .stroke({ color: '#6754af', opacity: 0.6, width: 5 });
};

const drawType02Handle = (element, offsetX) => {
  if (element.handle === 1 && element.parity !== 'single')
    return;
  const handleXSize = 130;
  const handleYSize = 15;
  let handleX1 = element.x1 + offsetX + padding;
  let handleY1 = element.y2 - padding - handleYSize;
  if (element.flip === 1 || element.direction === 1) {
    handleX1 = element.x2 + offsetX - padding - handleXSize;
  }
  if (element.handleYPos === 1) {
    handleY1 = element.y1 + padding;
  }
  drawHandle(element, handleXSize, handleYSize, handleX1, handleY1);
}

const drawType01vHandle = (element, offsetX) => {
  const handleXSize = element.x2 - element.x1 - 2 * padding;
  const handleYSize = 30;
  let handleX1 = element.x1 + offsetX + padding;
  let handleY1 = element.y2 - padding - handleYSize;
  if (element.handleYPos === 1) {
    handleY1 = element.y1 + padding;
  }
  drawHandle(element, handleXSize, handleYSize, handleX1, handleY1);
}

const drawType01DoorHandle = (element, offsetX) => {
  const handleXSize = element.handle === 0 ? 30 : 15;
  const handleYSize = element.y2 - element.y1 - 2 * padding;
  let handleX1 = element.x1 + offsetX + padding;
  let handleY1 = element.y2 - padding - handleYSize;
  if (element.flip === 1 || element.direction === 1) {
    handleX1 = element.x2 + offsetX - padding - handleXSize;
  }
  drawHandle(element, handleXSize, handleYSize, handleX1, handleY1);
}
