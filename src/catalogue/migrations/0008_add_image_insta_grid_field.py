# Generated by Django 3.2.15 on 2022-09-15 12:56

from django.db import (
    migrations,
    models,
)
from django.db.models import (
    F,
    IntegerField,
    OuterRef,
    Prefetch,
    Subquery,
)
from django.db.models.functions import Cast


def migrate_insta_grid_images_to_catalogue_entry(apps, schema_editor):
    CatalogueEntry = apps.get_model('catalogue', 'CatalogueEntry')
    FurnitureImage = apps.get_model('gallery', 'FurnitureImage')
    insta_grid_type = 'insta_grid'

    prefetched_images = Prefetch(
        'furniture__additional_images',
        queryset=FurnitureImage.objects.filter(
            type=insta_grid_type,
            enabled=True,
        ),
    )

    (
        CatalogueEntry.objects.all()
        .prefetch_related('furniture', prefetched_images)
        .annotate(
            original_id=Cast(F('available_colors__0__id'), output_field=IntegerField())
        )
        .update(
            image_insta_grid=Subquery(
                FurnitureImage.objects.filter(
                    furniture_object_id=OuterRef('original_id'),
                    furniture_content_type=OuterRef('content_type'),
                    enabled=True,
                    type=insta_grid_type,
                    color=OuterRef('material'),
                ).values('image')[:1],
            ),
            image_webp_insta_grid=Subquery(
                FurnitureImage.objects.filter(
                    furniture_object_id=OuterRef('original_id'),
                    furniture_content_type=OuterRef('content_type'),
                    enabled=True,
                    type=insta_grid_type,
                    color=OuterRef('material'),
                ).values('image_webp')[:1],
            ),
        )
    )


class Migration(migrations.Migration):

    dependencies = [
        ('catalogue', '0007_alter_catalogueentry_category'),
    ]

    operations = [
        migrations.AddField(
            model_name='catalogueentry',
            name='image_insta_grid',
            field=models.ImageField(
                blank=True,
                max_length=200,
                null=True,
                upload_to='catalogue/catalogue_entry/%Y/%m',
            ),
        ),
        migrations.AddField(
            model_name='catalogueentry',
            name='image_webp_insta_grid',
            field=models.ImageField(
                blank=True,
                max_length=200,
                null=True,
                upload_to='catalogue/catalogue_entry/%Y/%m',
            ),
        ),
        migrations.RunPython(
            migrate_insta_grid_images_to_catalogue_entry,
            migrations.RunPython.noop,
        ),
    ]
