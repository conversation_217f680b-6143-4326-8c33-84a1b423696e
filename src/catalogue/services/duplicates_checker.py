from typing import (
    Generic,
    Iterable,
    TypeVar,
    Union,
)

from catalogue.models import CatalogueEntry
from custom.enums import Furniture
from gallery.types import FurnitureType

TFurniture = TypeVar('TFurniture', bound=FurnitureType)


class DuplicatesChecker(Generic[TFurniture]):
    """Class for checking if two furniture objects are duplicates of each other.

    Duplicates in this context are furniture objects that look the same (small
    geometric differences do not matter).
    """

    BASE_CONDITIONS = {
        'furniture_category',
        'shelf_type',
        'material',
        'width',
        'height',
        'depth',
    }
    JETTY_CONDITIONS = {
        'backs',
        'doors',
        'drawers',
        'horizontals',
        'legs',
        'verticals',
    }
    SOTTY_CONDITIONS = {
        'armrests',
        'chaise_longues',
        'corners',
        'covers_only',
        'footrests',
        'seaters',
    }
    WATTY_CONDITIONS = {
        'backs',
        'bars',
        'doors',
        'drawers',
        'lighting',
    }

    def __init__(self, furniture1: TFurniture, furniture2: TFurniture):
        self.furniture1 = furniture1
        self.furniture2 = furniture2
        if self.furniture1.furniture_type != self.furniture2.furniture_type:
            raise ValueError('Furniture types are different')
        self.type_conditions = {
            Furniture.jetty.value: self.JETTY_CONDITIONS,
            Furniture.sotty.value: self.SOTTY_CONDITIONS,
            Furniture.watty.value: self.WATTY_CONDITIONS,
        }.get(self.furniture1.furniture_type, {})

    def are_duplicates(self) -> bool:
        return self._check_conditions(
            conditions=self.BASE_CONDITIONS
        ) and self._check_conditions(conditions=self.type_conditions)

    def _check_conditions(self, conditions: Iterable[str]) -> bool:
        return all(
            getattr(self.furniture1, attr) == getattr(self.furniture2, attr)
            for attr in conditions
        )


def has_duplicate_in_catalogue(furniture: Union[FurnitureType]) -> bool:
    candidates = CatalogueEntry.objects.filter(
        content_type__model=furniture.furniture_type,
        category=furniture.furniture_category,
        width=furniture.width,
        height=furniture.height,
        depth=furniture.depth,
        shelf_type=furniture.shelf_type,
        material=furniture.material,
    )
    for entry in candidates:
        if DuplicatesChecker(furniture, entry.furniture).are_duplicates():
            return True
    return False
