from typing import Iterable

import factory.fuzzy

from custom.enums import Sofa01Color


class CatalogueEntryFactory(factory.django.DjangoModelFactory):
    order = factory.Sequence(int)
    category = factory.SelfAttribute('furniture.shelf_category')
    category_order = factory.Sequence(int)
    furniture = factory.SubFactory(
        'gallery.tests.factories.JettyFactory',
    )
    shelf_type = factory.SelfAttribute('furniture.shelf_type')
    material = factory.SelfAttribute('furniture.material')
    product_unreal_image_webp = factory.django.ImageField()
    product_unreal_thumbnail_image_webp = factory.django.ImageField()
    width = factory.fuzzy.FuzzyInteger(30, 240)
    enabled = True

    class Meta:
        model = 'catalogue.CatalogueEntry'

    class Params:
        is_jetty = factory.Trait(
            furniture=factory.SubFactory('gallery.tests.factories.JettyFactory')
        )
        is_sotty = factory.Trait(
            furniture=factory.SubFactory('gallery.tests.factories.SottyFactory')
        )
        is_watty = factory.Trait(
            furniture=factory.SubFactory('gallery.tests.factories.WattyFactory')
        )
        is_sofa_corduroy = factory.Trait(
            furniture=factory.SubFactory(
                'gallery.tests.factories.SottyFactory',
                materials=[Sofa01Color.CORDUROY_ECRU],
            )
        )
        is_sofa_wool = factory.Trait(
            furniture=factory.SubFactory(
                'gallery.tests.factories.SottyFactory',
                materials=[Sofa01Color.REWOOL2_BROWN],
            )
        )
        is_tone_wardrobe = factory.Trait(
            furniture=factory.SubFactory(
                'gallery.tests.factories.WattyFactory', is_tone_wardrobe=True
            )
        )
        is_edge_wardrobe = factory.Trait(
            furniture=factory.SubFactory(
                'gallery.tests.factories.WattyFactory', is_edge_wardrobe=True
            )
        )

    @factory.post_generation
    def attributes(self, create, tags: Iterable[str], **kwargs):
        if not create or not tags:
            return

        self.attributes.add(*tags)


class BoardManualOrderFactory(factory.django.DjangoModelFactory):
    entry = factory.SubFactory(CatalogueEntryFactory)
    board_name = ''
    order = factory.Sequence(lambda n: n)
    published = True

    class Meta:
        model = 'catalogue.BoardManualOrder'
