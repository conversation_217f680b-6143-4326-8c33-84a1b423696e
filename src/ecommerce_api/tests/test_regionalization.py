import pytest

from regions.constants import OTHER_REGION_NAME
from regions.geo_guessr import (
    get_country_from_geoip_match,
    get_region_by_ip,
)


@pytest.mark.django_db
@pytest.mark.parametrize(
    'ip, language, region_name',  # noqa: PT006
    [
        ('INVALID', 'de', 'germany'),
        ('INVALID', 'pl', OTHER_REGION_NAME),
        ('************', 'en', 'germany'),
        ('**************', 'en', 'united_kingdom'),
        ('**************', 'en', OTHER_REGION_NAME),
        ('************', 'nl', 'netherlands'),
    ],
)
def test_get_region_by_ip(ip, language, region_name, region_factory, country_factory):
    country_factory(germany=True)
    country_factory(united_kingdom=True)
    country_factory(netherlands=True)
    region_factory(other=True)
    region_factory(netherlands=True)
    region = get_region_by_ip(ip, language=language)

    assert region.name == region_name


@pytest.mark.django_db
def test_get_country_from_geoip_match_starting_with_the_article(country_factory):
    netherlands = country_factory(netherlands=True)

    match = 'The Netherlands'
    country = get_country_from_geoip_match(match)
    assert country == netherlands
