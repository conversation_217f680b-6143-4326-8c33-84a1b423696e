from __future__ import unicode_literals

from django.db import (
    migrations,
    transaction,
)


def migrate_old_complaints(apps, schema_editor):

    Complaint = apps.get_model('complaints', 'Complaint')
    ComplaintType = apps.get_model('complaints', 'ComplaintType')
    TypicalIssues = apps.get_model('complaints', 'TypicalIssues')
    Responsibility = apps.get_model('complaints', 'Responsibility')

    old_complaints = Complaint.objects.all()

    with transaction.atomic():
        for complaint in old_complaints:
            new_ctype, _ = ComplaintType.objects.get_or_create(
                name=complaint.complaint_type, is_deprecated=True
            )
            new_typicalissues, _ = TypicalIssues.objects.get_or_create(
                name=complaint.typical_issues,
                complaint_type=new_ctype,
                is_deprecated=True,
            )
            new_responsibility, _ = Responsibility.objects.get_or_create(
                name=complaint.responsibility, is_deprecated=True
            )

            complaint.complaint_type_new = new_ctype
            complaint.typical_issues_new = new_typicalissues
            complaint.responsibility_new = new_responsibility
            complaint.deleted = True
            complaint.save()


class Migration(migrations.Migration):

    dependencies = [
        ('complaints', '0004_replace_complaint_fields_into_models'),
    ]

    operations = [
        migrations.RunPython(
            migrate_old_complaints, migrations.RunPython.noop, elidable=False
        ),
    ]
