from decimal import Decimal

from django.utils import timezone

import pytest

from complaints import reproduction_days_elements
from complaints.enums import ComplaintPriorityChoices
from complaints.models import ComplaintCosts
from complaints.services.create_complaint import (
    create_complaints_with_costs,
    get_element_details,
)
from complaints.services.create_related_objects import ProcessComplaintToProduction
from producers.enums import Manufacturers


def get_complaint_elements(
    elements_regular=None,
    elements_features=None,
    elements_other=None,
    fittings=None,
):
    return {
        'elements': elements_regular or [],
        'features': elements_features or [],
        'other': elements_other or [],
        'fittings': fittings or {},
    }


@pytest.fixture
def form_cleaned_data(user_admin, typical_issues_factory):
    return {
        'reporter': user_admin,
        'reported_date': timezone.now(),
        'typical_issues': typical_issues_factory(),
        'reproduction': True,
        'is_repeated': False,
        'boxes_damaged': False,
        'refund': False,
        'refund_currency': 'EUR',
        'refund_amount': Decimal('0.00'),
        'fittings_only': False,
        'assembly_team_intervention': False,
        'additional_info': '123',
        'conversation_link': '',
        'packages_info_was_provided': False,
    }


def assert_proper_complaints_created(complaints, create_priority, create_normal):
    complaint_priority = [
        complaint
        for complaint in complaints
        if complaint.priority == ComplaintPriorityChoices.PRIORITY
    ]
    complaint_normal = [
        complaint
        for complaint in complaints
        if complaint.priority == ComplaintPriorityChoices.NORMAL
    ]
    assert len(complaint_priority) == int(create_priority)
    assert len(complaint_normal) == int(create_normal)


@pytest.mark.django_db
class TestGetElementDetails:
    def test_assigns_correct_element_details_for_fitting(
        self,
        product_factory,
        manufactor_factory,
    ):
        manufactor = manufactor_factory(id=1)
        product = product_factory(manufactor=manufactor)
        element_details = get_element_details(
            {'fittings': {'fitting_benjamin_franklin': 1}},
            product,
        )
        assert len(element_details) == 1
        assert element_details[0].element_name == 'fitting_benjamin_franklin'
        assert element_details[0].number_of_fittings == 1
        assert (
            element_details[0].reproduction_days_element
            == reproduction_days_elements.ElementFitting
        )

    @pytest.mark.parametrize(
        'verticals',
        (  # noqa: PT007
            ['v1 - BOX:1'],
            ['v12 - BOX:1'],
            ['Ah - BOX:1'],
            ['Clr - BOX:1', 'Ahn - BOX:1', 'B - BOX:1'],
        ),
    )
    def test_assigns_correct_element_details_for_vertical_drewtur(
        self,
        product_factory,
        manufactor_factory,
        verticals,
    ):
        drewtur = manufactor_factory(id=1, name='Drewtur')
        product = product_factory(manufactor=drewtur)
        element_details = get_element_details(
            {'elements': verticals},
            product,
        )
        assert len(element_details) == len(verticals)
        assert element_details[0].element_name == verticals[0]
        assert element_details[0].number_of_fittings is None
        assert (
            element_details[0].reproduction_days_element
            == reproduction_days_elements.ElementExpressReplacement
        )

    @pytest.mark.parametrize(
        'support',
        (  # noqa: PT007
            ['S - BOX:1'],
            ['s - BOX:1'],
            ['As - BOX:3'],
            ['Bs - BOX:2'],
            ['Cs - BOX:1', 'Bs - BOX:2'],
        ),
    )
    def test_assigns_correct_element_details_for_support_express_replacement(
        self,
        product_factory,
        manufactor_factory,
        support,
    ):
        manufactor = manufactor_factory(id=Manufacturers.DREWTUR)
        product = product_factory(manufactor=manufactor)
        element_details = get_element_details(
            {'elements': support},
            product,
        )
        assert len(element_details) == len(support)
        assert element_details[0].element_name == support[0]
        assert (
            element_details[0].reproduction_days_element
            == reproduction_days_elements.ElementExpressReplacement
        )

    @pytest.mark.parametrize(
        'support',
        (  # noqa: PT007
            ['As - BOX:3'],
            ['Bs - BOX:2'],
            ['Cs - BOX:1', 'Bs - BOX:2'],
        ),
    )
    def test_assigns_correct_element_details_for_support_not_express_replacement(
        self,
        product_factory,
        manufactor_factory,
        support,
    ):
        manufactor = manufactor_factory(id=Manufacturers.AMIR)
        product = product_factory(manufactor=manufactor)
        element_details = get_element_details(
            {'elements': support},
            product,
        )
        assert len(element_details) == len(support)
        assert element_details[0].element_name == support[0]
        assert (
            element_details[0].reproduction_days_element
            == reproduction_days_elements.JettyElementSupport
        )

    @pytest.mark.parametrize(
        'verticals',
        (  # noqa: PT007
            ['v1 - BOX:1'],
            ['v12 - BOX:1'],
            ['Ah - BOX:1'],
            ['Clr - BOX:1', 'Ahn - BOX:1', 'B - BOX:1'],
        ),
    )
    def test_assigns_correct_element_details_for_vertical_meblepl(
        self,
        product_factory,
        manufactor_factory,
        verticals,
    ):
        meblepl = manufactor_factory(id=24, name='MeblePL')
        product = product_factory(manufactor=meblepl)
        element_details = get_element_details(
            {'elements': verticals},
            product,
        )
        assert len(element_details) == len(verticals)
        assert element_details[0].element_name == verticals[0]
        assert element_details[0].number_of_fittings is None
        assert (
            element_details[0].reproduction_days_element
            == reproduction_days_elements.JettyElementVertical
        )

    @pytest.mark.parametrize(
        'horizontals',
        (  # noqa: PT007
            ['1 - BOX:1'],
            ['2 - BOX:1', '5 - BOX:1'],
            ['h1 - BOX:1'],
        ),
    )
    def test_assigns_correct_element_details_for_horizontal(
        self,
        product_factory,
        manufactor_factory,
        horizontals,
    ):
        manufactor = manufactor_factory(id=1)
        product = product_factory(manufactor=manufactor)
        element_details = get_element_details(
            {'elements': horizontals},
            product,
        )
        assert len(element_details) == len(horizontals)
        assert element_details[0].element_name == horizontals[0]
        assert (
            element_details[0].reproduction_days_element
            == reproduction_days_elements.JettyElementHorizontal
        )

    @pytest.mark.parametrize(
        'back',
        (  # noqa: PT007
            ['w1 - BOX:1'],
            ['W1 - BOX:1'],
            ['w1 - BOX:1', 'W2 -  - BOX:1'],
        ),
    )
    def test_assigns_correct_element_details_for_back(
        self,
        product_factory,
        manufactor_factory,
        back,
    ):
        manufactor = manufactor_factory(id=1)
        product = product_factory(manufactor=manufactor)
        element_details = get_element_details(
            {'elements': back},
            product,
        )
        assert len(element_details) == len(back)
        assert element_details[0].element_name == back[0]
        assert (
            element_details[0].reproduction_days_element
            == reproduction_days_elements.JettyElementBack
        )

    @pytest.mark.parametrize(
        'insert',
        (  # noqa: PT007
            ['i4 - BOX:1'],
            ['i1 - BOX:1', 'i10 -  - BOX:1'],
        ),
    )
    def test_assigns_correct_element_details_for_insert(
        self,
        product_factory,
        manufactor_factory,
        insert,
    ):
        manufactor = manufactor_factory(id=1)
        product = product_factory(manufactor=manufactor)
        element_details = get_element_details(
            {'elements': insert},
            product,
        )
        assert len(element_details) == len(insert)
        assert element_details[0].element_name == insert[0]
        assert (
            element_details[0].reproduction_days_element
            == reproduction_days_elements.JettyElementInsert
        )

    @pytest.mark.parametrize(
        'plinth',
        (  # noqa: PT007
            ['p1 - BOX:1'],
            ['p2 - BOX:1', 'p5 - BOX:1'],
        ),
    )
    def test_assigns_correct_element_details_for_plinth(
        self,
        product_factory,
        manufactor_factory,
        plinth,
    ):
        manufactor = manufactor_factory(id=1)
        product = product_factory(manufactor=manufactor)
        element_details = get_element_details(
            {'elements': plinth},
            product,
        )
        assert len(element_details) == len(plinth)
        assert element_details[0].element_name == plinth[0]
        assert (
            element_details[0].reproduction_days_element
            == reproduction_days_elements.JettyElementPlinth
        )

    @pytest.mark.parametrize(
        'drawer',
        (  # noqa: PT007
            ['T1c - BOX:1'],
            ['d1d - BOX:1', 'd2c -  - BOX:1'],
            ['d1-1 - BOX:1'],
            ['d1-1 - BOX:1', 'd2-3 -  - BOX:1'],
        ),
    )
    def test_assigns_correct_element_details_for_drawer(
        self,
        product_factory,
        manufactor_factory,
        drawer,
    ):
        manufactor = manufactor_factory(id=1)
        product = product_factory(manufactor=manufactor)
        element_details = get_element_details(
            {'elements': drawer},
            product,
        )
        assert len(element_details) == len(drawer)
        assert element_details[0].element_name == drawer[0]
        assert (
            element_details[0].reproduction_days_element
            == reproduction_days_elements.ElementDrawer
        )

    @pytest.mark.parametrize(
        'door',
        (  # noqa: PT007
            ['f1 - BOX:1'],
            ['Z - BOX:1'],
            ['Z1 - BOX:1', 'Z2 -  - BOX:1'],
        ),
    )
    def test_assigns_correct_element_details_for_door(
        self,
        product_factory,
        manufactor_factory,
        door,
    ):
        manufactor = manufactor_factory(id=1)
        product = product_factory(manufactor=manufactor)
        element_details = get_element_details(
            {'elements': door},
            product,
        )
        assert len(element_details) == len(door)
        assert element_details[0].element_name == door[0]
        assert (
            element_details[0].reproduction_days_element
            == reproduction_days_elements.JettyElementDoor
        )


@pytest.mark.django_db
class TestCreateComplaintsForElements:
    @pytest.mark.parametrize(
        'element1, element2, create_priority, create_normal',  # noqa: PT006
        [
            ('h', 'v1', True, False),  # horizontal and vertical
            ('w1', 'i1', False, True),  # back and insert
            ('h1', 'T1C', True, True),  # horizontal and drawer
            ('h1', 'f1', True, True),  # horizontal and door
            ('As', 'D', True, False),  # support(express replacement) and vertical
            ('p1', 'i2', True, False),  # plinth and insert
            ('As', 'w', True, True),  # support(express replacement) and back
            ('v1', 'As', True, False),  # express_replacement and support (exp rep)
        ],
    )
    def test_valid_complaints_created(
        self,
        manufactor_factory,
        product_factory,
        user_admin,
        form_cleaned_data,
        element1,
        element2,
        create_priority,
        create_normal,
    ):
        manufactor = manufactor_factory(id=1)
        product = product_factory(
            manufactor=manufactor, cached_physical_product_version=10
        )

        complaints = create_complaints_with_costs(
            elements=get_complaint_elements(
                elements_regular=[element1, element2],
            ),
            product=product,
            request_user=user_admin,
            split_complaint=True,
            **form_cleaned_data,
        )
        assert_proper_complaints_created(complaints, create_priority, create_normal)


class TestCreateComplaintFittingAndElement:
    @pytest.mark.parametrize(
        'element, create_priority, create_normal',  # noqa: PT006
        [
            ('v1', True, False),  # fitting and vertical
            ('As', True, False),  # fitting and support (express replacement)
            ('h2', True, False),  # fitting and horizontal
            ('w1', True, True),  # fitting and back
            ('i1', True, True),  # fitting and insert
            ('p1', True, False),  # fitting and plinth
            ('T1D', True, True),  # fitting and drawer
            ('D2', True, False),  # fitting and vertical
        ],
    )
    def test_fitting_and_element(
        self,
        manufactor_factory,
        product_factory,
        admin_user,
        form_cleaned_data,
        element,
        create_priority,
        create_normal,
    ):
        manufactor = manufactor_factory(id=1)
        product = product_factory(
            manufactor=manufactor, cached_physical_product_version=10
        )
        elements = get_complaint_elements(
            elements_regular=[element], fittings={'fitting_benjamin_franklin': 1}
        )
        complaints = create_complaints_with_costs(
            elements=elements,
            product=product,
            request_user=admin_user,
            split_complaint=True,
            **form_cleaned_data,
        )
        assert_proper_complaints_created(complaints, create_priority, create_normal)

    @pytest.mark.parametrize(
        'element, create_priority, create_normal, expected_categories',  # noqa: PT006
        [
            ('v1', False, True, ['vertical', 'fitting']),  # fitting and vertical
            (
                'As',
                False,
                True,
                ['support', 'fitting'],
            ),  # fitting and support (express replacement)
            ('h2', False, True, ['horizontal', 'fitting']),  # fitting and horizontal
            ('w1', False, True, ['back', 'fitting']),  # fitting and back
            ('i1', False, True, ['insert', 'fitting']),  # fitting and insert
            ('p1', False, True, ['plinth', 'fitting']),  # fitting and plinth
            ('T1D', False, True, ['drawer', 'fitting']),  # fitting and drawer
            ('f2', False, True, ['door', 'fitting']),  # fitting and door
        ],
    )
    def test_fitting_and_element_not_split_complaint(
        self,
        manufactor_factory,
        product_factory,
        admin_user,
        form_cleaned_data,
        element,
        create_priority,
        create_normal,
        expected_categories,
    ):
        manufactor = manufactor_factory(id=1)
        product = product_factory(
            manufactor=manufactor, cached_physical_product_version=10
        )
        elements = get_complaint_elements(
            elements_regular=[element], fittings={'fitting_benjamin_franklin': 1}
        )
        complaints = create_complaints_with_costs(
            elements=elements,
            product=product,
            request_user=admin_user,
            split_complaint=False,
            **form_cleaned_data,
        )
        assert set(complaints[0].reproduction_element_categories) == set(
            expected_categories
        )
        assert_proper_complaints_created(complaints, create_priority, create_normal)

    def test_fitting_and_element_not_express_replacement(
        self,
        manufactor_factory,
        product_factory,
        admin_user,
        form_cleaned_data,
    ):
        manufactor = manufactor_factory(id=Manufacturers.AMIR)
        product = product_factory(manufactor=manufactor)
        elements = get_complaint_elements(
            elements_regular=['As'], fittings={'fitting_benjamin_franklin': 1}
        )
        complaints = create_complaints_with_costs(
            elements=elements,
            product=product,
            request_user=admin_user,
            split_complaint=True,
            **form_cleaned_data,
        )
        assert_proper_complaints_created(complaints, True, True)


@pytest.mark.django_db
class TestComplaintCosts:
    def test_complaint_costs_created(
        self,
        manufactor_factory,
        product_factory,
        user_admin,
        form_cleaned_data,
    ):
        manufactor = manufactor_factory(id=1)
        product = product_factory(manufactor=manufactor)
        refund_amount = Decimal('199.05')
        form_cleaned_data['refund_amount'] = refund_amount
        form_cleaned_data['refund'] = True
        complaints = create_complaints_with_costs(
            elements={},
            product=product,
            request_user=user_admin,
            **form_cleaned_data,
        )
        assert len(complaints) == 1
        assert complaints[0].complaint_costs.refund_amount == refund_amount

    @pytest.mark.nbp
    def test_get_refund_in_euro_should_return_none_when_no_refund_amount(self):
        complaint_cost = ComplaintCosts(
            currency='CHF',
            refund_amount=None,
        )
        complaint_cost.save()
        complaint_cost.refresh_from_db()
        assert complaint_cost.refund_in_euro is None


@pytest.mark.django_db
class TestProcessComplaintToProduction:
    def test_create_order_item_related_should_clear_order_item_prices(
        self,
        order_factory,
        order,
        order_item_factory,
        product_factory,
        complaint_factory,
    ):
        order_item = order_item_factory(
            order=order,
            is_jetty=True,
            price=Decimal('100.0'),
            region_price=Decimal('120.0'),
            price_net=Decimal('80.0'),
            region_price_net=Decimal('100.0'),
            assembly_price=Decimal('20.0'),
            region_assembly_price=Decimal('30.0'),
            delivery_price=Decimal('10.0'),
            region_delivery_price=Decimal('20.0'),
            vat_amount=Decimal('20.0'),
            region_vat_amount=Decimal('40.0'),
            region_promo_value=Decimal('5.0'),
        )
        product = product_factory(order_item=order_item)
        complaint = complaint_factory(reproduction_order=order_factory())
        processor = ProcessComplaintToProduction()
        complaint_order_item = processor.create_order_item_related(complaint, product)

        assert complaint_order_item.price == Decimal('0.0')
        assert complaint_order_item.region_price == Decimal('0.0')
        assert complaint_order_item.price_net == Decimal('0.0')
        assert complaint_order_item.region_price_net == Decimal('0.0')
        assert complaint_order_item.assembly_price == Decimal('0.0')
        assert complaint_order_item.region_assembly_price == Decimal('0.0')
        assert complaint_order_item.delivery_price == Decimal('0.0')
        assert complaint_order_item.region_delivery_price == Decimal('0.0')
        assert complaint_order_item.vat_amount == Decimal('0.0')
        assert complaint_order_item.region_vat_amount == Decimal('0.0')
        assert complaint_order_item.region_promo_value == Decimal('0.0')
