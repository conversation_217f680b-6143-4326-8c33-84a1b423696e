from datetime import datetime

from django.db.models import QuerySet
from django.http import HttpResponse

import openpyxl

from complaints.models import Complaint
from custom.openpyxl_styles import (
    center_alignment,
    gray70_background,
    horizontal_left_alignment,
    thin_border,
    white_font,
    yellow_background,
)
from custom.utils.report_file import ReportFile
from custom.utils.xlsx import (
    apply_styles_to_cell,
    set_columns_widths,
)


def generate_complaint_production_order_as_html(
    queryset: QuerySet[Complaint],
) -> HttpResponse:
    report_file = generate_complaint_production_order_xls(queryset)
    return report_file.get_as_http_response()


def generate_complaint_production_order_xls(
    complaint_queryset: QuerySet[Complaint],
) -> ReportFile:
    small_column = 4
    medium_column = 9
    large_column = 26

    queryset = complaint_queryset.order_by('id')
    current_row = 1

    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = 'Zamów<PERSON>ie'

    set_columns_widths(
        worksheet,
        {
            'A': small_column,
            'B': large_column,
            'C': medium_column,
            'D': large_column,
            'E': large_column,
            'F': large_column,
            'G': large_column,
            'H': large_column,
            'I': large_column,
            'J': large_column,
            'K': medium_column,
            'L': medium_column,
            'M': large_column,
            'N': large_column,
            'O': large_column,
            'P': large_column,
            'R': large_column,
            'S': large_column,
            'T': large_column,
        },
    )
    top_header_style = {
        'fill': gray70_background,
        'font': white_font,
        'alignment': horizontal_left_alignment,
    }
    border_style = {
        'border': thin_border,
        'alignment': center_alignment,
    }
    complaint_column_style = {
        'fill': yellow_background,
    }
    headers = (
        'lp.',
        'Complaint',
        'Product',
        'Manufacturer',
        'Manufacturer Complaint',
        'Priority',
        'Batching date',
        'Reproduction days',
        'Reproduction date',
        'Order',
        'Batch',
        'Type',
        'Elementy',
        'Materiał',
        'Rodzaj reklamacji',
        'Powód',
        'Obszar',
        'Odp.',
        'Data',
    )

    for i, header in enumerate(headers, 1):
        apply_styles_to_cell(
            worksheet.cell(
                row=current_row,
                column=i,
                value=header,
            ),
            top_header_style,
        )
    current_row += 1
    for complaint in queryset.iterator():
        product = complaint.product
        reproduction_product = complaint.reproduction_product
        row = (
            current_row - 1,
            complaint.reproduction_product_id,
            product.id,
            product.manufactor.name if product.manufactor else '-',
            reproduction_product.manufactor.name
            if reproduction_product and reproduction_product.manufactor
            else '-',
            product.get_priority_display(),
            reproduction_product.batch.created_at
            if reproduction_product and reproduction_product.batch
            else '-',
            reproduction_product.get_fixed_reproduction_days_for_production_order()
            if reproduction_product
            else '-',
            reproduction_product.get_reproduction_date_for_production_order()
            if reproduction_product
            else '-',
            product.order_id,
            product.batch_id,
            product.get_shelf_type(with_html=False),
            complaint.get_elements_description(),
            complaint.material,
            complaint.complaint_type.name if complaint.complaint_type else '-',
            complaint.typical_issues.name,
            getattr(complaint.area, 'name', ''),
            getattr(complaint.responsibility, 'name', ''),
            datetime.now().strftime('%Y/%m/%d'),
        )
        apply_styles_to_cell(
            worksheet.cell(row=current_row, column=2, value=row[2]),
            complaint_column_style,
        )
        for i, value in enumerate(row, 1):
            apply_styles_to_cell(
                worksheet.cell(row=current_row, column=i, value=value),
                border_style,
            )
        current_row += 1
    return ReportFile.load_workbook(
        workbook, f'zamowienie_reklamacyjne_{datetime.now().date()}.xlsx'
    )
