# Generated by Django 1.11.24 on 2020-02-17 21:54
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

from custom.models import ChoiceArrayField


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('regions', '0001_initial'),
        ('abtests', '0001_initial'),
        ('vouchers', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Promotion',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=150)),
                ('active', models.BooleanField(default=False)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('validated', models.BooleanField(default=False)),
                (
                    'promocode',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to='vouchers.Voucher',
                    ),
                ),
                (
                    'promogroup',
                    models.ManyToManyField(
                        related_name='promogroup', to='vouchers.VoucherGroup'
                    ),
                ),
            ],
            options={
                'verbose_name': 'Promotion',
                'verbose_name_plural': 'Promotions',
                'ordering': ('created_at',),
            },
        ),
        migrations.CreateModel(
            name='PromotionConfig',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'short_name',
                    models.CharField(
                        max_length=150,
                        verbose_name='for cookies, analytics and so on, only letters',
                    ),
                ),
                ('name', models.CharField(max_length=150)),
                ('active', models.BooleanField(default=False)),
                ('start_date', models.DateTimeField(blank=True, null=True)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('countdown_date', models.DateTimeField(blank=True, null=True)),
                (
                    'languages',
                    models.IntegerField(
                        choices=[
                            (0, 'all'),
                            (1, 'English'),
                            (3, 'French'),
                            (2, 'German'),
                        ],
                        default=0,
                    ),
                ),
                (
                    'devices',
                    models.IntegerField(
                        choices=[(0, 'all'), (1, 'mobile'), (2, 'tablet and desktop')],
                        default=0,
                    ),
                ),
                ('utm_part', models.CharField(blank=True, max_length=250, null=True)),
                (
                    'hp_promo',
                    models.BooleanField(
                        default=False,
                        help_text=(
                            'select this if you want to have '
                            'promo section enabled on hp'
                        ),
                    ),
                ),
                (
                    'hp_promo_video',
                    models.BooleanField(
                        default=False,
                        help_text=(
                            'select this if you want to have '
                            'video/vistia version of video on hp'
                        ),
                    ),
                ),
                ('hp_promo_copy_en_header', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_en_text', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_en_cta', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_en_link', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_de_header', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_de_text', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_de_cta', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_de_link', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_fr_header', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_fr_text', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_fr_cta', models.TextField(blank=True, null=True)),
                ('hp_promo_copy_fr_link', models.TextField(blank=True, null=True)),
                ('popup_enabled', models.BooleanField(default=False)),
                (
                    'popup_image',
                    models.ImageField(
                        blank=True, null=True, upload_to='promotion_popups/'
                    ),
                ),
                (
                    'popup_video',
                    models.FileField(
                        blank=True, null=True, upload_to='promotion_popups/'
                    ),
                ),
                (
                    'popup_video_poster',
                    models.ImageField(
                        blank=True, null=True, upload_to='promotion_popups/'
                    ),
                ),
                ('popup_copy_en_header_1', models.TextField(blank=True, null=True)),
                ('popup_copy_en_header_2', models.TextField(blank=True, null=True)),
                ('popup_copy_en_button_1', models.TextField(blank=True, null=True)),
                ('popup_copy_en_header_3', models.TextField(blank=True, null=True)),
                ('popup_copy_en_link', models.TextField(blank=True, null=True)),
                ('popup_copy_de_header_1', models.TextField(blank=True, null=True)),
                ('popup_copy_de_header_2', models.TextField(blank=True, null=True)),
                ('popup_copy_de_button_1', models.TextField(blank=True, null=True)),
                ('popup_copy_de_header_3', models.TextField(blank=True, null=True)),
                ('popup_copy_de_link', models.TextField(blank=True, null=True)),
                ('popup_copy_fr_header_1', models.TextField(blank=True, null=True)),
                ('popup_copy_fr_header_2', models.TextField(blank=True, null=True)),
                ('popup_copy_fr_button_1', models.TextField(blank=True, null=True)),
                ('popup_copy_fr_header_3', models.TextField(blank=True, null=True)),
                ('popup_copy_fr_link', models.TextField(blank=True, null=True)),
                ('ribbon_enabled', models.BooleanField(default=False)),
                ('ribbon_copy_en_header_1', models.TextField(blank=True, null=True)),
                ('ribbon_copy_en_header_2', models.TextField(blank=True, null=True)),
                (
                    'ribbon_copy_en_header_mobile_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'ribbon_copy_en_header_mobile_2',
                    models.TextField(blank=True, null=True),
                ),
                ('ribbon_copy_en_link', models.TextField(blank=True, null=True)),
                ('ribbon_copy_de_header_1', models.TextField(blank=True, null=True)),
                ('ribbon_copy_de_header_2', models.TextField(blank=True, null=True)),
                (
                    'ribbon_copy_de_header_mobile_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'ribbon_copy_de_header_mobile_2',
                    models.TextField(blank=True, null=True),
                ),
                ('ribbon_copy_de_link', models.TextField(blank=True, null=True)),
                ('ribbon_copy_fr_header_1', models.TextField(blank=True, null=True)),
                ('ribbon_copy_fr_header_2', models.TextField(blank=True, null=True)),
                (
                    'ribbon_copy_fr_header_mobile_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'ribbon_copy_fr_header_mobile_2',
                    models.TextField(blank=True, null=True),
                ),
                ('ribbon_copy_fr_link', models.TextField(blank=True, null=True)),
                ('cart_ribbon_enabled', models.BooleanField(default=False)),
                (
                    'cart_ribbon_copy_en_header_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'cart_ribbon_copy_en_header_mobile_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'cart_ribbon_copy_de_header_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'cart_ribbon_copy_de_header_mobile_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'cart_ribbon_copy_fr_header_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'cart_ribbon_copy_fr_header_mobile_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_new_alert_enabled',
                    models.IntegerField(
                        choices=[
                            (0, 'Disabled'),
                            (1, 'Enabled version 33/66'),
                            (2, 'Enabled version 50/50'),
                        ],
                        default=0,
                    ),
                ),
                ('configurator_alert_enabled', models.BooleanField(default=False)),
                (
                    'configurator_copy_en_promo_alert_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_en_promo_alert_3',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_en_promo_alert_button_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_en_promo_alert_mobile_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_en_promo_alert_mobile_1_popup_paragraph_1',
                    models.TextField(blank=True, null=True, verbose_name='not used'),
                ),
                (
                    'configurator_copy_en_promo_alert_mobile_1_popup_paragraph_2',
                    models.TextField(blank=True, null=True, verbose_name='not used'),
                ),
                (
                    'configurator_copy_en_promo_alert_mobile_1_popup_paragraph_3',
                    models.TextField(blank=True, null=True, verbose_name='not used'),
                ),
                (
                    'configurator_copy_de_promo_alert_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_de_promo_alert_3',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_de_promo_alert_button_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_de_promo_alert_mobile_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_de_promo_alert_mobile_1_popup_paragraph_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_de_promo_alert_mobile_1_popup_paragraph_2',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_de_promo_alert_mobile_1_popup_paragraph_3',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_fr_promo_alert_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_fr_promo_alert_3',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_fr_promo_alert_button_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_fr_promo_alert_mobile_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_fr_promo_alert_mobile_1_popup_paragraph_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_fr_promo_alert_mobile_1_popup_paragraph_2',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'configurator_copy_fr_promo_alert_mobile_1_popup_paragraph_3',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'grid_copy_en_slot_1_header_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'grid_copy_en_slot_1_paragraph_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'grid_copy_de_slot_1_header_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'grid_copy_de_slot_1_paragraph_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'grid_copy_fr_slot_1_header_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'grid_copy_fr_slot_1_paragraph_1',
                    models.TextField(blank=True, null=True),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                (
                    'ab_test',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to='abtests.ABTest',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Promotion variant',
                'verbose_name_plural': 'Promotion variants',
                'ordering': ('created_at',),
            },
        ),
        migrations.CreateModel(
            name='PromotionPicture',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('title', models.CharField(max_length=512, unique=True)),
                (
                    'image',
                    models.ImageField(
                        upload_to='instapages_gallery',
                        verbose_name='Original image, check proper resolution',
                    ),
                ),
                (
                    'image_webp',
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to='instapages_gallery',
                        verbose_name='Webp version, generate by action',
                    ),
                ),
                (
                    'image_base64_thumbnail',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default={}, null=True
                    ),
                ),
            ],
            options={
                'verbose_name': 'Promotion picture',
            },
        ),
        migrations.CreateModel(
            name='VoucherConditionOrder',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=150)),
                (
                    'number_of_items',
                    models.IntegerField(
                        blank=True,
                        null=True,
                        verbose_name='Minimum number of items in order',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Voucher conditionals for orders',
            },
        ),
        migrations.CreateModel(
            name='VoucherConditionShelf',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=150)),
                ('height_min', models.IntegerField(blank=True, null=True)),
                ('height_max', models.IntegerField(blank=True, null=True)),
                ('width_min', models.IntegerField(blank=True, null=True)),
                ('width_max', models.IntegerField(blank=True, null=True)),
                (
                    'features',
                    ChoiceArrayField(
                        base_field=models.IntegerField(
                            choices=[
                                (1, 'doors'),
                                (2, 'drawers'),
                                (3, 'extended width'),
                            ]
                        ),
                        blank=True,
                        null=True,
                        size=None,
                    ),
                ),
                (
                    'colors',
                    ChoiceArrayField(
                        base_field=models.IntegerField(
                            choices=[
                                (1, 'white'),
                                (2, 'black'),
                                (4, 'gray'),
                                (5, 'obergine'),
                                (6, 'natural/fornir'),
                            ]
                        ),
                        blank=True,
                        null=True,
                        size=None,
                    ),
                ),
            ],
            options={
                'verbose_name': 'Voucher conditionals for shelves',
            },
        ),
        migrations.CreateModel(
            name='VoucherCopyConfig',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=150)),
                (
                    'copy_en_promo_alert_popup_paragraph_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_en_promo_alert_popup_paragraph_2',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_en_promo_alert_popup_paragraph_3',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_de_promo_alert_popup_paragraph_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_de_promo_alert_popup_paragraph_2',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_de_promo_alert_popup_paragraph_3',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_fr_promo_alert_popup_paragraph_1',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_fr_promo_alert_popup_paragraph_2',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_fr_promo_alert_popup_paragraph_3',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_en_promo_ribbon_left_text',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_en_promo_ribbon_right_text',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_en_promo_ribbon_left_red',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_en_promo_ribbon_right_red',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_de_promo_ribbon_left_text',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_de_promo_ribbon_right_text',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_de_promo_ribbon_left_red',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_de_promo_ribbon_right_red',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_fr_promo_ribbon_left_text',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_fr_promo_ribbon_right_text',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_fr_promo_ribbon_left_red',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'copy_fr_promo_ribbon_right_red',
                    models.TextField(blank=True, null=True),
                ),
                (
                    'promotion',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='voucher_configs',
                        to='promotions.PromotionConfig',
                    ),
                ),
                (
                    'voucher',
                    models.ForeignKey(
                        blank=True,
                        help_text='empty for below lowest limit',
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to='vouchers.Voucher',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Copy for voucher',
                'verbose_name_plural': 'Copy for vouchers',
            },
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='background_picture',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='background_picture',
                to='promotions.PromotionPicture',
            ),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='desktop_picture',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='desktop_picture',
                to='promotions.PromotionPicture',
            ),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='enabled_regions',
            field=models.ManyToManyField(to='regions.Region'),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='grid_picture',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='grid_picture',
                to='promotions.PromotionPicture',
            ),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='mobile_picture',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='mobile_picture',
                to='promotions.PromotionPicture',
            ),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='order_conditions',
            field=models.ManyToManyField(
                blank=True, to='promotions.VoucherConditionOrder'
            ),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='promotion',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='configs',
                to='promotions.Promotion',
            ),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='shelf_conditions',
            field=models.ManyToManyField(
                blank=True, to='promotions.VoucherConditionShelf'
            ),
        ),
    ]
