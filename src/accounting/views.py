import csv
import datetime
import io

from django.shortcuts import (
    get_object_or_404,
    render,
)
from django.views.generic import View
from django.views.generic.edit import FormView

from accounting.enums import (
    MoneySourceChoices,
    PaymentTypeChoices,
)
from accounting.forms import (
    BankExportForm,
    BankExportsForm,
)
from accounting.models import MoneyTransfer
from accounting.tasks import match_with_orders_task
from orders.models import (
    Order,
    PaidOrders,
)
from regions.models import Currency


class OrderOverviewView(View):
    def get(self, request, pk=None):
        context = {}
        orders = list(
            PaidOrders.objects.order_by('created_at').values_list('id', flat=True)
        )
        context['list_type'] = 'Paid orders by created at'

        order = get_object_or_404(Order, pk=pk)

        try:
            order_index = orders.index(int(pk))
        except ValueError:
            order_index = -1
        context['original'] = order
        if order_index > -1:
            context['prev'] = orders[order_index - 1 if order_index > 1 else 0]
            context['next'] = orders[
                order_index + 1 if order_index < len(orders) - 1 else len(orders) - 1
            ]
            context['this'] = order_index
            context['list_length'] = len(orders)
        else:
            context['prev'] = int(pk) - 1
            context['next'] = int(pk) + 2
            context['this'] = pk
            context['list_length'] = 'all'
            context['list_type'] = 'All orders'

        return render(request, 'accounting/order_overview.html', context=context)


class PaypalExportView(FormView):
    template_name = 'accounting/paypal_data_import.html'
    form_class = BankExportForm
    success_url = '/thanks/'

    def list_from_dict(self, itemlist, itemkeys):
        resp_list = []
        for item in itemlist:
            item_list = []
            for key in itemkeys:
                item_list.append(item[key])  # noqa: PERF401
            resp_list.append(item_list)
        return resp_list

    def add_logic_to_items(self, paypal_items):
        for item in paypal_items:
            if 'order-' in item['item_name']:
                item['order_link'] = (
                    '<a href="/admin/accounting/order_info/{}/'
                    '">Link to added order</a>'.format(
                        item['item_name'].replace('order-', '')
                    )
                )
            else:
                if item['waluta'] == 'EUR':
                    item['order_link'] = (
                        '<a href="/admin/orders/order/?total_price={}">'
                        'Filter with order value</a>'.format(item['brutto'])
                    )
                else:
                    item['order_link'] = ''
                item['order_link'] += (
                    '<br/><br/><a href="/admin/search?q={}">'
                    'Search with user email</a>'.format(item['email'])
                )

    def prepare_data(self, csv_file):
        paypal_items = []
        keys = [
            'data',
            'email',
            'name',
            'item_name',
            'topic',
            'type',
            'status',
            'waluta',
            'brutto',
            'transaction_id',
            'delivery',
            'note',
            'order_link',
        ]
        for line in list(csv.reader(csv_file))[1:]:
            paypal_items.append(  # noqa: PERF401
                {
                    'data': datetime.datetime.strptime(
                        f'{line[0]} {line[1]}', '%d.%m.%Y %H:%M:%S'
                    ),
                    'hour': line[1],
                    'name': line[3],
                    'type': line[4],
                    'status': line[5],
                    'waluta': line[6],
                    'brutto': float(
                        ''.join(x for x in line[7] if x in '-**********,').replace(
                            ',', '.'
                        )
                    ),
                    'email': line[10],
                    'transaction_id': line[12],
                    'delivery': line[13],
                    'item_name': line[15],
                    'topic': line[38],
                    'note': line[37],
                    'order_link': '',
                    'source': MoneySourceChoices.MONEY_PAYPAL,
                }
            )
        return keys, paypal_items

    def prepare_paypal(self, context):
        csv_file = io.StringIO(
            self.request.FILES['csv_file'].read().decode('utf-8'),
        )
        keys, paypal_items = self.prepare_data(csv_file)
        context['response_keys'] = keys
        context['response_all'] = self.list_from_dict(paypal_items, keys)
        outgoing_payments = [x for x in paypal_items if x['brutto'] <= 0]
        context['response_outgoing'] = self.list_from_dict(outgoing_payments, keys)
        paypal_items = [x for x in paypal_items if x not in outgoing_payments]
        ads_payments = [
            x for x in paypal_items if x['email'] == '<EMAIL>'
        ]
        context['response_ads'] = self.list_from_dict(ads_payments, keys)
        paypal_items = [x for x in paypal_items if x not in ads_payments]
        system_payments = [
            x
            for x in paypal_items
            if 'przeliczenie waluty' in x['type']
            or ' realizacj' in x['type']
            or 'danie wys' in x['type']
        ]
        context['response_system'] = self.list_from_dict(system_payments, keys)
        paypal_items = [x for x in paypal_items if x not in system_payments]
        self.add_logic_to_items(paypal_items)
        context['response_rest'] = self.list_from_dict(paypal_items, keys)

    def form_valid(self, form):
        # This method is called when valid form data has been POSTed.
        # It should return an HttpResponse.
        context = self.get_context_data(form=form)
        self.prepare_paypal(context)
        return self.render_to_response(context)


class MoneyTransfersView(PaypalExportView):
    template_name = 'accounting/money_transfer.html'
    form_class = BankExportsForm
    success_url = '/thanks/'

    def list_from_dict(self, itemlist, itemkeys):
        resp_list = []
        for item in itemlist:
            item_list = []
            for key in itemkeys:
                item_list.append(item[key])  # noqa: PERF401
            resp_list.append(item_list)
        return resp_list

    def add_logic_to_items(self, paypal_items):
        mt_ids = []
        for item in paypal_items:
            if (
                MoneyTransfer.objects.filter(external_id=item['transaction_id']).count()
                == 0
            ):
                mt = MoneyTransfer(
                    order=None,
                    invoice=None,
                    payment_source=item['source'],
                    payment_type=PaymentTypeChoices.PAYMENT_SALE,
                    external_id=item['transaction_id'],
                    external_type=item['type'].strip(),
                    description=item['topic'].strip(),
                    sender=item['name'].strip(),
                    sender_account=item['email'],
                    amount=item['brutto'],
                    currency=Currency.objects.filter(
                        code__iexact=str(item['waluta']).strip()
                    ).first(),
                    notes=item['note'].strip(),
                    issue_date=item['data'],
                    additional_date=None,
                    export_date=datetime.date.today(),
                )
                mt.save()
                mt_ids.append(mt.pk)
            else:
                item['order_link'] = 'Transfer already in system'
        match_with_orders_task.delay(mt_ids)

    def check_adyen(self, title):
        if 'TX' in title and 'batch' in title and 'CSTMCO' in title:
            return True
        return False

    def check_own(self, sender):
        if 'CUSTOM SP Z O O MIŃSKA'.replace(' ', '') in sender.replace(' ', ''):
            return True
        return False

    def prepare_bank_data(self, csv_file):
        bank_items = []
        keys = [
            'data',
            'email',
            'name',
            'item_name',
            'topic',
            'type',
            'status',
            'waluta',
            'brutto',
            'transaction_id',
            'delivery',
            'note',
            'order_link',
        ]
        first_line = True
        for line in list(csv.reader(csv_file, delimiter=';')):
            if first_line:
                first_line = False
                continue
            if self.check_adyen(line[22]) or self.check_own(line[9].strip()):
                continue
            bank_items.append(
                {
                    'data': datetime.datetime.strptime(f'{line[5]}', '%Y-%m-%d'),
                    'hour': '',  # line[1],
                    'name': line[9],
                    'type': line[21],
                    'status': line[12],
                    'waluta': line[23],
                    'brutto': float(
                        ''.join(x for x in line[8] if x in '-**********,').replace(
                            ',', '.'
                        )
                    ),
                    'email': '',  # line[10],
                    'transaction_id': line[17],
                    'delivery': '',  # line[13],
                    'item_name': '',  # line[15],
                    'topic': line[22],
                    'note': '',  # line[37],
                    'order_link': '',
                    'source': MoneySourceChoices.MONEY_BANK,
                }
            )
        return keys, bank_items

    def prepare_bank(self, context):
        csv_file = io.StringIO(
            self.request.FILES['bank_csv_file'].read().decode('utf-8'),
        )
        keys, bank_items = self.prepare_bank_data(csv_file)
        context['response_keys_bank'] = keys
        self.add_logic_to_items(bank_items)
        context['response_rest_bank'] = self.list_from_dict(bank_items, keys)

    def form_valid(self, form):
        context = self.get_context_data(form=form)
        if 'csv_file' in self.request.FILES:
            self.prepare_paypal(context)
        if 'bank_csv_file' in self.request.FILES:
            self.prepare_bank(context)
        return self.render_to_response(context)
