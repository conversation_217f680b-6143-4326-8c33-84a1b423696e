from django.contrib.contenttypes.models import ContentType
from django.db.models import QuerySet

from custom.enums import Furniture
from feeds.image_configs import ImageConfigOption
from feeds.models import FeedItem
from render_tasks.choices import WebglRenderTaskType
from render_tasks.models import WebglRenderTask


class WebglImageChooserService:
    """
    This is used to get all the feed items with lacking webgl images
    """

    def __init__(self, furniture_model_class: type) -> None:
        self.furniture_model_class = furniture_model_class

    def get_queryset(self) -> QuerySet[FeedItem]:
        return (
            FeedItem.objects.filter(
                category__image_config__in=ImageConfigOption.webgl_configs(),
                content_type=ContentType.objects.get_for_model(
                    self.furniture_model_class
                ),
            )
            .exclude(images__config__in=ImageConfigOption.webgl_configs())
            .distinct()
            .order_by('-id')
        )

    def create_render_tasks(self) -> None:
        tasks = []
        for feed_item in self.get_queryset():
            if feed_item.category.image_config in ImageConfigOption.webgl_configs():
                task = WebglRenderTask(
                    furniture=feed_item.furniture,
                    task_type=WebglRenderTaskType.FEEDS,
                    image_configuration=feed_item.category.image_config,
                    feed_item=feed_item,
                )
                tasks.append(task)
        WebglRenderTask.objects.bulk_create(tasks, batch_size=300)


def create_tasks_for_lacking_feed_images():
    for value, _name in Furniture.furniture_types_choices():
        WebglImageChooserService(Furniture(value).model).create_render_tasks()
