import logging

from datetime import datetime
from functools import lru_cache
from typing import (
    Any,
    Iterable,
    Optional,
    Union,
)

from django.contrib.contenttypes.models import ContentType
from django.core.paginator import Paginator
from django.db.models import Count

import pandas as pd

from custom.enums import Furniture
from gallery.enums import ConfiguratorTypeEnum
from orders.enums import OrderType
from orders.internal_api.clients import LogisticOrderAPIClient
from orders.models import OrderItem
from producers.models import Product
from production_margins.choices import ReportDataType
from production_margins.data_management.exporters import AveragePricingFactorsExporter

logger = logging.getLogger('cstm')


def generate_codenames_report(
    products: Iterable[Product],
    is_original_serialization: bool = True,
    is_aggregated: Optional[bool] = False,
    override_ppv: Optional[dict] = None,
) -> tuple[pd.DataFrame, list[int]]:
    products = products.order_by('id')
    usages, products_with_errors = _extract_report_info_from_products(
        products,
        is_original_serialization,
        override_ppv,
    )
    report_data = _report_data_with_calculated_columns(usages)
    if is_aggregated:
        report_data = _report_data_with_aggregated_codenames(report_data)

    return report_data, products_with_errors


def _extract_report_info_from_products(
    products: Iterable[Product],
    is_original_serialization: bool,
    override_ppv: Optional[dict] = None,
) -> tuple[pd.DataFrame, list[int]]:
    usage_info = pd.DataFrame()
    products_with_errors = []
    if override_ppv is None:
        override_ppv = {}

    created_at = {
        created_at.date().replace(day=15)
        for created_at in products.values_list('created_at', flat=True).distinct()
    }

    if is_original_serialization:
        pf_by_date = {
            created_date: _get_pricing_factors_for_date(created_date)
            for created_date in created_at
        }
    else:
        pf_by_date = _get_pricing_factors_for_date()

    for page in Paginator(products, 100):
        for product in page.object_list:
            ppv = override_ppv.get(product.cached_shelf_type)
            try:
                usages_dataframe = _extract_report_info_from_single_product(
                    product,
                    is_original_serialization,
                    ppv,
                )
                if is_original_serialization:
                    data_from_pf = pf_by_date[product.created_at.date().replace(day=15)]
                else:
                    data_from_pf = pf_by_date
                usage_with_loss_factor = usages_dataframe.join(
                    data_from_pf,
                    how='left',
                    on='codename',
                )
                usage_info = pd.concat([usage_info, usage_with_loss_factor])

            except Exception:
                # lots of things can go wrong - we should not interrupt the process
                products_with_errors.append(product.id)
    return usage_info, products_with_errors


def _get_data_from_pricing_factors(
    product: Product,
    is_original_serialization: bool,
) -> pd.DataFrame:
    for_date = None
    if is_original_serialization:
        for_date = product.created_at.replace(day=15).date()
    return _get_pricing_factors_for_date(for_date)


@lru_cache()
def _get_pricing_factors_for_date(for_date: datetime = None) -> pd.DataFrame:  # noqa: RUF013
    pricing_factors = pd.DataFrame(
        AveragePricingFactorsExporter(for_date=for_date).to_dict()
    )
    pricing_factors = pricing_factors.astype(
        {
            'weight_per_unit': 'float64',
            'price_per_unit': 'float64',
            'loss_factor': 'float64',
            'management_cost': 'float64',
        },
    )
    pricing_factors['management_cost'] /= 100
    return pricing_factors[
        ['loss_factor', 'price_per_unit', 'weight_per_unit', 'management_cost']
    ]


def _extract_report_info_from_single_product(
    product: Product,
    is_original_serialization: bool,
    override_ppv: Optional[int] = None,
) -> pd.DataFrame:
    usage_info = []
    serialization = _get_serialization(product, is_original_serialization, override_ppv)
    product_base_info = _get_product_base_info(product)
    serialization_info = _get_serialization_base_info(serialization)
    furniture_parameters = _get_furniture_parameters(product)
    for category, materials in serialization['materials'].items():  # noqa: B007, PERF102
        for codename, usage_data in materials.items():
            codename_usage_info = {
                **product_base_info,
                'codename': codename,
                'codename_category': codename.split('_')[0],
                'summary_quantity': usage_data['usage'],
                **serialization_info,
                **furniture_parameters,
            }
            usage_info.append(codename_usage_info)
    return pd.DataFrame(usage_info)


def _get_serialization(
    product: Product,
    is_original_serialization: bool,
    override_ppv: Optional[int] = None,
) -> dict:
    if override_ppv:
        return product.serialization_updater._test_product_serialization(
            ppv=override_ppv
        )
    if is_original_serialization:
        return product.details.cached_serialization
    return product.serialization_updater._test_product_serialization()


def _get_product_base_info(product: Product) -> dict[str, Any]:
    return {
        'product_id': product.id,
        'order_id': product.order_id,
        'item_id': product.order_item.order_item.id,
        'paid_at': product.order.paid_at,
        'placed_at': product.order.placed_at,
        'production_date': product.get_finished_production_date(),
        'delivery_date': product.order.get_delivery_date(),
        'sold_price': product.order_item.order_item.get_shelf_price_as_number(
            region=product.order_item.region,
        ),
    }


def _get_serialization_base_info(serialization: dict[str, Any]) -> dict[str, str]:
    return {
        'serialized_at': serialization.get('serialized_at', ''),
        'pricing_factors_id': serialization.get('id_pricing_factors', -1),
        'weight_net': serialization['item']['weight'],
        'weight_gross': _get_weight_gross(serialization),
        'number_of_packages': _get_number_of_packages(serialization),
    }


def _get_weight_gross(serialization: dict[str, Any]) -> float:
    return sum(pack['weight'] for pack in serialization['item']['packs'])


def _get_number_of_packages(serialization: dict[str, Any]) -> int:
    return len(serialization['item']['packs'])


def _get_furniture_parameters(product: Product) -> dict[str, Any]:
    item = product.order_item.order_item
    return {
        'width': item.width,
        'height': item.height,
        'depth': item.depth,
        'item_type': item.shelf_type,
        'color': item.material,
        'category': item.furniture_category,
        'dna': item.dna_name,
        'country': product.order.country.lower(),
        'configurator_type': ConfiguratorTypeEnum(item.configurator_type).name,
    }


def _report_data_with_calculated_columns(
    usages_dataframe: pd.DataFrame,
) -> pd.DataFrame:
    # loss factor is kept as (1+x) where `x` is the actual loss.
    # We need `x` in percentage
    usages_dataframe['summary_price'] = usages_dataframe.price_per_unit
    usages_dataframe['codename_weight'] = (
        usages_dataframe.weight_per_unit * usages_dataframe.summary_quantity
    )
    usages_dataframe['summary_losses'] = (usages_dataframe.loss_factor - 1) * 100
    usages_dataframe['summary_material_consumption_with_losses'] = (
        usages_dataframe.loss_factor * usages_dataframe.summary_quantity
    )
    usages_dataframe['summary_cost_with_losses'] = (
        usages_dataframe.summary_material_consumption_with_losses
        * usages_dataframe.summary_price
    )
    usages_dataframe['summary_cost_of_management'] = (
        usages_dataframe.summary_cost_with_losses * usages_dataframe.management_cost
    )
    usages_dataframe['summary_total_cost'] = (
        usages_dataframe.summary_cost_with_losses
        + usages_dataframe.summary_cost_of_management
    )
    usages_dataframe.drop(
        columns=['loss_factor', 'management_cost', 'price_per_unit'],
        inplace=True,
    )
    return usages_dataframe


def _report_data_with_aggregated_codenames(
    usages_dataframe: pd.DataFrame,
) -> pd.DataFrame:
    usages_dataframe_grouped = usages_dataframe.groupby(
        by=[
            'codename',
            'item_type',
            'color',
            'category',
            'country',
            'configurator_type',
        ],
        as_index=False,
    ).agg(
        {
            'weight_net': 'mean',
            'weight_gross': 'mean',
            'width': 'mean',
            'height': 'mean',
            'depth': 'mean',
            'summary_price': 'sum',
            'summary_losses': 'sum',
            'summary_material_consumption_with_losses': 'sum',
            'summary_cost_with_losses': 'sum',
            'summary_cost_of_management': 'sum',
            'summary_total_cost': 'sum',
        }
    )

    return usages_dataframe_grouped


def get_product_ids_for_report(
    report_type: int,
    product_ids: Optional[Iterable[int]] = None,
    reporting_date: Union[str, datetime.date, None] = None,
    exclude_complaints: bool = False,
    count: Optional[int] = None,
) -> Iterable[int]:
    if report_type == ReportDataType.PRODUCT_IDS:
        products = Product.objects.filter(id__in=product_ids)
    else:
        if isinstance(reporting_date, str):
            reporting_date_converted = datetime.strptime(
                reporting_date,
                '%Y-%m-%dT%H:%M:%S',
            ).date()
        else:
            reporting_date_converted = reporting_date
        products = Product.objects.filter(
            created_at__month=reporting_date_converted.month,
            created_at__year=reporting_date_converted.year,
        )
    if exclude_complaints:
        products = products.exclude(order__order_type=OrderType.COMPLAINT)
    if count:
        products = products[:count]
    return list(products.values_list('id', flat=True))


def get_product_ids_for_ecotax_report(
    start_date: datetime.date,
    end_date: datetime.date,
) -> list[int]:
    logistic_order_api_client = LogisticOrderAPIClient()
    order_ids = logistic_order_api_client.filter_orders_for_eco_tax(
        start_date, end_date
    )
    products = Product.objects.filter(order__id__in=order_ids).exclude(
        order__order_type=OrderType.COMPLAINT
    )
    return list(products.values_list('id', flat=True))


def get_sample_box_ecotax_info(
    start_date: datetime.date,
    end_date: datetime.date,
):
    SampleBox = ContentType.objects.get(app_label='gallery', model='samplebox')  # noqa: N806
    logistic_order_api_client = LogisticOrderAPIClient()
    order_ids = logistic_order_api_client.filter_orders_for_eco_tax(
        start_date,
        end_date,
        order_type=Furniture.sample_box.value,
    )

    return list(
        OrderItem.objects.filter(
            content_type=SampleBox,
            order__id__in=order_ids,
        )
        .values('order__country')
        .annotate(items_count=Count('order__country'))
    )
