import datetime
import os

from decimal import Decimal

import factory

from factory import fuzzy

from production_margins.choices import (
    MeasurementUnit,
    PricingFactorItemCategory,
)
from production_margins.models import ManufacturerCode
from user_profile.tests.factories import UserFactory


def load_test_pricing_factors_data():
    filepath = os.path.join(
        os.path.dirname(__file__),
        'test_files',
        'pricing_factors_data.json',
    )
    with open(filepath) as fp:
        return fp.read()


class PricingFactorsFactory(factory.django.DjangoModelFactory):
    active = True
    name = factory.Faker('name')
    created_by = factory.SubFactory(UserFactory)
    data = factory.LazyFunction(load_test_pricing_factors_data)

    class Meta:
        model = 'production_margins.PricingFactors'


class ElementsOrderFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = 'production_margins.ElementsOrder'


class MaterialManagementCostFactory(factory.django.DjangoModelFactory):
    date_from = datetime.date(2021, 1, 1)
    date_to = datetime.date.today() + datetime.timedelta(days=2)

    class Meta:
        model = 'production_margins.MaterialManagementCost'


class PricingFactorItemFactory(factory.django.DjangoModelFactory):
    codename = fuzzy.FuzzyText()
    category = fuzzy.FuzzyChoice(
        choices=PricingFactorItemCategory.values,
    )
    measurement_unit = fuzzy.FuzzyChoice(
        choices=MeasurementUnit.values,
    )
    weight = Decimal('0.1')
    thickness = 0
    length = 0
    price = Decimal('1.0')
    loss_factor = Decimal('1.5')
    description = 'd'
    conversion = 'c'

    class Meta:
        model = 'production_margins.PricingFactorItem'


class ElementManagedInfoFactory(factory.django.DjangoModelFactory):
    date_from = datetime.date(2021, 1, 1)
    date_to = datetime.date.today() + datetime.timedelta(days=2)
    pricing_factor_item = factory.SubFactory(PricingFactorItemFactory)

    class Meta:
        model = 'production_margins.ElementManagedInfo'


class ManufacturerCodeFactory(factory.django.DjangoModelFactory):
    code = fuzzy.FuzzyText()

    class Meta:
        model = ManufacturerCode

    @factory.post_generation
    def pricing_factor_items(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for pricing_factor_item in extracted:
                self.pricing_factor_item.add(pricing_factor_item)


class CustomPricingFactorFactory(factory.django.DjangoModelFactory):
    pricing_factor_item = factory.SubFactory(PricingFactorItemFactory)
    manufactor = factory.SubFactory('producers.tests.factories.ManufactorFactory')
    date_from = datetime.date(2021, 1, 1)
    date_to = datetime.date.today() + datetime.timedelta(days=2)
    price = fuzzy.FuzzyDecimal(low=10, high=100)
    loss_factor = fuzzy.FuzzyDecimal(low=0, high=100)

    class Meta:
        model = 'production_margins.CustomPricingFactor'


class CustomPricingFactorChangeRequestFactory(factory.django.DjangoModelFactory):
    date_from = datetime.date(2021, 1, 1)
    manufacturer_code = fuzzy.FuzzyText()
    custom_pricing_factor = factory.SubFactory(CustomPricingFactorFactory)
    price = fuzzy.FuzzyDecimal(low=10, high=100)

    class Meta:
        model = 'production_margins.CustomPricingFactorChangeRequest'

    @factory.post_generation
    def setup_element_managed_info(self, create, extracted, **kwargs):
        if not create:
            return

        # Create active ElementManagedInfo instance
        ElementManagedInfoFactory(
            pricing_factor_item=self.custom_pricing_factor.pricing_factor_item,
            manufactor=self.custom_pricing_factor.manufactor,
        )
