import typing

from decimal import Decimal

from custom.enums import Axis

if typing.TYPE_CHECKING:
    from gallery.models import Watty

Element = dict[str, int]
ElementsList = list[Element]

MAX_DOOR_HEIGHT_FOR_TREX = Decimal('0.382')


def compute_length(element: Element, axis: Axis) -> Decimal:
    """Compute element length along an axis and convert it to meters."""
    return abs(Decimal(element[f'{axis}2']) - Decimal(element[f'{axis}1'])) / 1000


def calculate_area(elements: ElementsList, axis1: Axis, axis2: Axis) -> Decimal:
    return sum(
        compute_length(element, axis1) * compute_length(element, axis2)
        for element in elements
    )


def sum_lengths(elements: ElementsList, axis: Axis) -> Decimal:
    return sum(compute_length(element, axis) for element in elements)


class Type13VeneerElementsPriceCalculator:
    def __init__(self, watty: 'Watty', price_coefficients: dict) -> None:
        self.watty = watty
        self._pc = price_coefficients

    def calculate_walls_price(self) -> Decimal:
        walls = self.watty.walls
        if not walls:
            return Decimal()

        return (
            len(walls) * self._pc['type13_veneer_wall_unit']
            + calculate_area(walls, Axis.Y, Axis.Z)
            * self._pc['type13_veneer_t02_wall_area']
        )

    def calculate_slabs_price(self) -> Decimal:
        slabs = self.watty.slabs
        if not slabs:
            return Decimal()

        return (
            len(slabs) * self._pc['type13_veneer_slab_unit']
            + calculate_area(slabs, Axis.X, Axis.Z)
            * self._pc['type13_veneer_slab_area']
        )

    def calculate_doors_price(self) -> Decimal:
        doors = self.watty.doors
        if not doors:
            return Decimal()

        doors_perimeter = sum(
            (compute_length(door, Axis.X) + compute_length(door, Axis.Y)) * 2
            for door in doors
        )
        return (
            len(doors) * self._pc['type13_veneer_door_unit']
            + doors_perimeter * self._pc['type13_veneer_door_perimeter']
        )

    def calculate_backs_price(self) -> Decimal:
        backs = self.watty.backs
        if not backs:
            return Decimal()

        return (
            len(backs) * self._pc['type13_veneer_backs_unit']
            + calculate_area(backs, Axis.X, Axis.Y)
            * self._pc['type13_veneer_backs_area']
        )

    def calculate_bars_price(self) -> Decimal:
        regular_bars = [bar for bar in self.watty.bars if bar['subtype'] != 'z']
        cross_bars = [bar for bar in self.watty.bars if bar['subtype'] == 'z']
        if regular_bars or cross_bars:
            return (
                len(regular_bars) * self._pc['type13_veneer_bar_unit']
                + len(cross_bars) * self._pc['type13_veneer_crossbar_unit']
                + sum_lengths(regular_bars, Axis.X)
                * self._pc['type13_veneer_bar_length']
            )
        return Decimal()

    def calculate_drawers_price(self) -> Decimal:
        # note: internal in context of t13 means behind doors
        drawers = self.watty.drawers
        internal_drawers = [drawer for drawer in drawers if drawer['subtype'] == 'd']
        external_drawers = [
            drawer for drawer in drawers if drawer['subtype'] in {'i', 'b', 'e'}
        ]
        internal_drawers_length = sum_lengths(internal_drawers, Axis.X)
        external_drawers_length = sum_lengths(external_drawers, Axis.X)

        internal_drawers_price = (
            len(internal_drawers) * self._pc['type13_veneer_internal_drawer_unit']
            + internal_drawers_length * self._pc['type13_veneer_drawer_width']
        )

        external_drawers_price = (
            len(external_drawers) * self._pc['type13_veneer_external_drawer_unit']
            + external_drawers_length * self._pc['type13_veneer_drawer_width']
        )

        return internal_drawers_price + external_drawers_price

    def calculate_cable_managements_price(self) -> Decimal:
        """Cable management price is constant."""
        cable_management = self.watty.cable_management
        return len(cable_management) * self._pc.get(
            'type13_veneer_cable_management_unit', Decimal('0')
        )
