import logging

from custom.enums import ShelfType
from regions.types import RegionLikeObject

logger = logging.getLogger('cstm')


class UnifiedPricingInterface:
    def __init__(self, region: RegionLikeObject, coefficients):
        self.region_name = region.name
        self.coefficients = coefficients

    def get_price(self, geometry):
        # circular imports
        from gallery.models import Jetty

        if isinstance(geometry, Jetty):
            return self.jetty_calculator(geometry)
        if geometry.shelf_type == ShelfType.TYPE03:
            return self.t03_calculator(geometry)
        if geometry.shelf_type == ShelfType.TYPE13:
            return self.t13_calculator(geometry)
        if geometry.shelf_type == ShelfType.VENEER_TYPE13:
            return self.f13_calculator(geometry)
        if geometry.shelf_type == ShelfType.TYPE23:
            return self.t23_calculator(geometry)
        if geometry.shelf_type == ShelfType.TYPE24:
            return self.t24_calculator(geometry)
        if geometry.shelf_type == ShelfType.TYPE25:
            return self.t25_calculator(geometry)
        if geometry.shelf_type == ShelfType.SOFA_TYPE01:
            return self.s01_calculator(geometry)

        msg = 'Pricing calculator not found for shelf type %s'
        logger.exception(msg, geometry.shelf_type)
        raise NotImplementedError(msg, geometry.shelf_type)

    def jetty_calculator(self, geometry):
        raise NotImplementedError

    def t03_calculator(self, geometry):
        raise NotImplementedError

    def t13_calculator(self, geometry):
        raise NotImplementedError

    def f13_calculator(self, geometry):
        raise NotImplementedError

    def t23_calculator(self, geometry):
        raise NotImplementedError

    def t24_calculator(self, geometry):
        raise NotImplementedError

    def t25_calculator(self, geometry):
        raise NotImplementedError

    def s01_calculator(self, geometry):
        raise NotImplementedError
