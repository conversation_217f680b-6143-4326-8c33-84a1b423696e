import typing

from decimal import Decimal

from django.conf import settings

from custom.enums import ShelfType

from ..constants import FACTOR_EURO
from .base_prices import (
    calculate_base_price,
    calculate_logistics_price,
)
from .coefficients import get_coefficients
from .elements import calculate_elements_price
from .margins import calculate_margins
from .traits import calculate_traits_price
from .value_based import calculate_value_based_increase

if typing.TYPE_CHECKING:
    from gallery.models import Jetty
    from pricing_v3.models import PricingVersion


def calculate_material_price(
    jetty: 'Jetty',
    jetty_price: Decimal,
    price_coefficients: dict[str, Decimal],
):
    shelf_type = {0: 't01', 1: 't02'}.get(jetty.shelf_type)
    if not shelf_type:
        # veneer or other mistakes :>
        return Decimal(0)
    color_group = ShelfType(jetty.shelf_type).colors(jetty.material).price_group()
    coef_name = f'{shelf_type}_{color_group.value}'
    return price_coefficients.get(coef_name, Decimal(0)) * jetty_price


def calculate_additional_increase(
    jetty: 'Jetty',
    jetty_price: Decimal,
    price_coefficients: dict[str, Decimal],
):
    if jetty.shelf_type == ShelfType.TYPE01:
        return jetty_price * price_coefficients['type_01_additional_increase']
    if jetty.shelf_type == ShelfType.TYPE02:
        return jetty_price * price_coefficients['type_02_additional_increase']
    if jetty.shelf_type == ShelfType.VENEER_TYPE01:
        return jetty_price * price_coefficients['type_01v_additional_increase']
    return 0


def prepare_price(
    jetty: 'Jetty', region_name: str, pricing_version: 'PricingVersion' = None
) -> dict:
    """
    Prepares the pricing dict.

    Pricing dict includes all the components of the price, for easier analysis.

    Args:
        jetty: Jetty for which the price is calculated
        region_name: Region name to calculate coefficients
        pricing_version: Optional pricing version. Could be used to recalculate price
                based on past coefficients

    Returns: Dictionary with the price split.

    """
    if pricing_version is None:
        price_coefficients = get_coefficients(region_name)
    else:
        price_coefficients = pricing_version.coefficients

    base_jetty_price = calculate_base_price(jetty, price_coefficients)
    jetty_price = sum(base_jetty_price.values())

    elements_price = calculate_elements_price(jetty, price_coefficients)
    jetty_price += sum(elements_price.values())

    traits_price = calculate_traits_price(jetty, jetty_price, price_coefficients)
    jetty_price += sum(traits_price.values())

    margins_price = calculate_margins(jetty, jetty_price, price_coefficients)
    jetty_price += sum(margins_price.values())

    logistic_price = calculate_logistics_price(jetty, price_coefficients)
    jetty_price += sum(logistic_price.values())

    value_based_increase = calculate_value_based_increase(
        jetty, jetty_price, price_coefficients
    )
    jetty_price += sum(value_based_increase.values())

    material_price = calculate_material_price(jetty, jetty_price, price_coefficients)
    jetty_price += material_price

    additional_increase = calculate_additional_increase(
        jetty,
        jetty_price,
        price_coefficients,
    )

    jetty_price += additional_increase

    return {
        'jetty_price_gross': jetty_price * Decimal(settings.POLISH_VAT_FACTOR),
        **base_jetty_price,
        **elements_price,
        **traits_price,
        **margins_price,
        **value_based_increase,
        **logistic_price,
    }


def get_pricing_dict(jetty: 'Jetty', region_name: str, in_pln: bool) -> dict:
    pricing_dict = prepare_price(jetty, region_name=region_name)

    price_in_pln = pricing_dict['jetty_price_gross']
    if in_pln:
        final_price = price_in_pln
    else:
        final_price = price_in_pln / FACTOR_EURO
    pricing_dict['total_rounded_gross_regional'] = int(final_price)
    return pricing_dict


def calculate_price(
    jetty: 'Jetty',
    region_name: str | None,
    pricing_version: 'PricingVersion' = None,
) -> int:
    pricing_dict = prepare_price(jetty, region_name, pricing_version)
    euro_price_gross = pricing_dict['jetty_price_gross'] / FACTOR_EURO
    return int(euro_price_gross)
