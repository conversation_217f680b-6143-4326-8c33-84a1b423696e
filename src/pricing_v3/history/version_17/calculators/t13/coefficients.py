from decimal import Decimal
from typing import Optional

base_coefs = {
    'type13_base_unit': Decimal('54.9059'),
    'type13_base_area': Decimal('16.1588'),
}


type13_coefs = {
    'type13_wall_unit': Decimal('22.6979'),
    'type13_t01_wall_area': Decimal('253.87'),
    'type13_t02_wall_area': Decimal('73.93'),
    'type13_slab_unit': Decimal('19.3694'),
    'type13_slab_area': Decimal('78.9872'),
    'type13_door_unit': Decimal('48.0713'),
    'type13_door_perimeter': Decimal('20.1187'),
    'type13_backs_unit': Decimal('20.1504'),
    'type13_backs_area': Decimal('73.0940'),
    'type13_bar_unit': Decimal('13.1048'),
    'type13_bar_length': Decimal('30.1818'),
    'type13_crossbar_unit': Decimal('55.3800'),
    'type13_drawer_width': Decimal('69.7642'),
    'type13_internal_drawer_unit': Decimal('170.8268'),
    'type13_external_drawer_unit': Decimal('145.7164'),
    'type13_logs_vol': Decimal('85.0000'),
    'type13_logs_drawers': Decimal('0'),
    'type13_logs_base': Decimal('0'),
    'type13_cable_management_unit': Decimal('16.61902'),
}


margin_coefs = {
    'type13_margin_wardrobe': Decimal('2.472'),
    'type13_margin_bookcase': Decimal('2.5961'),
    'type13_margin_wallstorage': Decimal('2.5961'),
    'type13_margin_chest': Decimal('2.3586'),
}

additional_factors = {
    'type_13_additional_increase': Decimal('0.0934'),
}


def get_coefficients(
    region_name: Optional[str],
    overrides: dict = None,  # noqa: RUF013
) -> dict[str, Decimal]:
    defaults = get_default_coefficients(region_name)
    coefficients = defaults['coefficients']

    if overrides:
        coefficients.update(overrides)

    return coefficients


def get_default_coefficients(region_name: Optional[str] = None) -> dict:
    coefficients = {
        **base_coefs,
        **margin_coefs,
        **type13_coefs,
        **additional_factors,
    }

    return {
        'coefficients': coefficients,
    }
