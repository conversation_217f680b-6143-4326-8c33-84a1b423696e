import enum
import json
import uuid

from datetime import date

from django.db import models

from cstm_be.media_storage import (
    private_media_storage,
    whatsapp_files_private_media_storage,
)
from custom.enums import ChoicesMixin
from custom.models import Timestampable
from producers.utils import RandomizedUploadTo


class DixaWebHookEvent(Timestampable):
    event_id = models.CharField(unique=True, max_length=256)
    event_type = models.CharField(max_length=256)
    conversation_id = models.CharField(max_length=256)
    requester_email = models.EmailField()
    processed = models.DateTimeField(null=True)


class MessageSource(ChoicesMixin, enum.Enum):
    SLACK = 'slack'
    DIXA = 'dixa'


class ChannelMapping(models.Model):
    slack_channel_id = models.Char<PERSON>ield(max_length=100)
    slack_channel_name = models.CharField(max_length=100)
    dixa_queue_name = models.CharField(max_length=100)
    dixa_queue_id = models.Char<PERSON>ield(max_length=100)


class Conversation(models.Model):
    dixa_id = models.CharField(max_length=100, null=True)  # noqa: DJ001
    slack_timestamp = models.CharField(max_length=100, null=True)  # noqa: DJ001
    original_source = models.CharField(max_length=100, choices=MessageSource.choices())
    channel_mapping = models.ForeignKey(
        ChannelMapping, on_delete=models.SET_NULL, null=True
    )


class Message(models.Model):
    dixa_id = models.CharField(max_length=100, null=True)  # noqa: DJ001
    slack_timestamp = models.CharField(max_length=100, null=True)  # noqa: DJ001
    original_source = models.CharField(max_length=100, choices=MessageSource.choices())
    conversation = models.ForeignKey(
        Conversation,
        related_name='messages',
        on_delete=models.SET_NULL,
        null=True,
    )
    text = models.TextField()


class AttachmentFile(models.Model):
    message = models.ForeignKey(
        Message,
        related_name='attachments',
        on_delete=models.CASCADE,
    )
    file = models.FileField(
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_zip/%Y/%m'),
    )
    dixa_url_path = models.CharField(max_length=1000, null=True)  # noqa: DJ001
    slack_id = models.CharField(max_length=1000, null=True)  # noqa: DJ001
    file_name = models.CharField(max_length=100)
    source = models.CharField(max_length=100, choices=MessageSource.choices())
    slack_permalink = models.CharField(max_length=1000, null=True)  # noqa: DJ001


class WhatsappFile(Timestampable):
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    file = models.FileField(
        storage=whatsapp_files_private_media_storage,
    )


class ConversationForAI(models.Model):
    dixa_id = models.IntegerField(null=False)
    channel = models.CharField(max_length=100, default='', blank=True)
    email = models.EmailField(blank=True, default='')
    phone = models.CharField(max_length=100, blank=True, default='')

    def as_text(self, without_stop_words=False):
        if without_stop_words:
            return self.all_messages_as_text_without_stop_words()
        return self.all_messages_as_text()

    def all_messages_as_text_without_stop_words(self):
        messages = self.messages.all().order_by('created_at')
        return '\n'.join(
            message.message_without_stopwords_with_author for message in messages
        )

    def all_messages_as_text(self):
        messages = self.messages.all().order_by('created_at')
        return '\n'.join(message.clean_message_with_author for message in messages)


class MessageForAI(models.Model):
    conversation = models.ForeignKey(
        ConversationForAI, on_delete=models.CASCADE, related_name='messages'
    )
    uuid = models.CharField(max_length=100)
    raw_data = models.JSONField()

    text_without_stop_words = models.TextField(default='', blank=True)
    cleaned_text = models.TextField(default='', blank=True)
    language = models.CharField(max_length=10, default='en', blank=True)

    is_customer_message = models.BooleanField(default=False)
    phone = models.CharField(max_length=100, blank=True)
    email = models.EmailField(blank=True)
    name = models.CharField(max_length=100, blank=True)
    created_at = models.DateTimeField()

    @property
    def message_without_stopwords_with_author(self):
        if self.is_customer_message:
            return f'Customer: {self.text_without_stop_words}'
        return f'Agent: {self.text_without_stop_words}'

    @property
    def clean_message_with_author(self):
        if self.is_customer_message:
            return f'Customer: {self.cleaned_text}'
        return f'Agent: {self.cleaned_text}'


class SystemInstruction(models.Model):
    prompt_template = (
        'You are an assistant analyzing a customer conversation for an eCommerce shop '
        'that sells custom furniture. Your goal is to identify key conversation '
        'features that can help improve customer experience, service quality, '
        'and product feedback. \n'
        'You should analyze and extract the following features from upcoming '
        'conversations:\n'
        '{features_description}\n'
        'Return your output strictly in the following JSON format:\n'
        '{output_format}'
    )

    created_at = models.DateTimeField(auto_now_add=True)
    name = models.CharField(max_length=100)
    created_by = models.CharField(max_length=100)

    def __str__(self):
        return f'{self.name}_{self.created_by}_{self.id}'

    @property
    def prompt_text(self) -> str:
        if hasattr(self, '_prompt_text'):
            return self._prompt_text
        self._prompt_text = self.prompt_template.format(
            features_description=self._features_description,
            output_format=self._output_format,
        )
        return self._prompt_text

    @property
    def _features_description(self) -> str:
        results = ''
        for field in self.output_fields.all():
            results += f'{field.field_name} - {field.description}.'
            if field.possible_values:
                results += f'Choose one value from: {field.possible_values}'
            results += '\n'
        return results

    @property
    def _output_format(self) -> str:
        return json.dumps(
            {
                field.field_name: field.possible_values or 'short text'
                for field in self.output_fields.all()
            },
            indent=2,
        )


class AnalysisOutputField(models.Model):
    system_instruction = models.ForeignKey(
        SystemInstruction, on_delete=models.CASCADE, related_name='output_fields'
    )
    field_name = models.CharField(max_length=100)
    possible_values = models.TextField(blank=True)
    description = models.TextField(blank=True)

    class Meta:
        unique_together = ['system_instruction', 'field_name']

    def __str__(self):
        return f'{self.field_name} - {self.system_instruction.name}'


class ConversationAnalysisResult(models.Model):
    conversation = models.ForeignKey(
        ConversationForAI, on_delete=models.CASCADE, related_name='analysis'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    system_instruction = models.ForeignKey(
        SystemInstruction, on_delete=models.SET_NULL, null=True
    )
    model_name = models.CharField(max_length=100)
    input = models.TextField()
    output = models.TextField()
    data = models.JSONField(help_text='JSON formatted output')


class DailyUsage(models.Model):
    usage_date = models.DateField(default=date.today, unique=True)
    limit = models.PositiveIntegerField(
        default=50, help_text='Maximum number of analysis actions allowed per day'
    )
    usage = models.PositiveIntegerField(default=0)

    @property
    def left_usage(self) -> int:
        return self.limit - self.usage

    def update_usage(self, count: int):
        if count > self.left_usage:
            raise ValueError(
                f'Cannot update usage by {count}. Only {self.left_usage} '
                f'left for today.'
            )
        self.usage += count
        self.save(update_fields=['usage'])
