import factory

from waiting_list.choices import WaitingListStatus


class WaitingListSetupFactory(factory.django.DjangoModelFactory):
    enabled = True
    creator = factory.SubFactory(
        'user_profile.tests.factories.UserFactory',
        is_admin=True,
    )

    class Meta:
        model = 'waiting_list.WaitingListSetup'


class WaitingListEntryFactory(factory.django.DjangoModelFactory):
    watty = factory.SubFactory('gallery.tests.factories.WattyFactory')
    status = WaitingListStatus.NEW

    related_status = factory.RelatedFactory(
        'waiting_list.tests.factories.WaitingListEntryStatusFactory',
        status=WaitingListStatus.NEW,
        factory_related_name='entry',
    )

    class Meta:
        model = 'waiting_list.WaitingListEntry'


class WaitingListEntryStatusFactory(factory.django.DjangoModelFactory):
    creator = factory.SubFactory('user_profile.tests.factories.UserFactory')

    class Meta:
        model = 'waiting_list.WaitingListEntryStatus'
