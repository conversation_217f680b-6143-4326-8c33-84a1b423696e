import {
    checkIfPricesAreEqual,
    extractPriceValue,
    verifyFooterDesktop,
    generateRandomEmail
} from "../../test_modules/common_functions"
import { verifyReviews } from "../../test_modules/sections_PDP_tests"
import sofa_pdp from "../../pages/sofa_product_page"
import sofa_configurator from "../../pages/sofa_configurator"
import change_category from "../../pages/sofa_change_category_page"
import cart from "../../pages/cart_page"
import login_page from "../../pages/login_page"
import wishlist from "../../pages/library_page"
import checkout_page from "../../pages/checkout_page"

describe('Sofa E2E Tests', () => {
    const testParams = {
        sofaPdpUrl: '/de-de/sofa/drei_sitzer/739,s,3-sitzer-sofa-olivgrun-173x83x100cm',
        sofaConfigUrl: '/de-de/configure/739,s',
        sofaPdpRomania: '/en-ro/sofa/drei_sitzer/739,s,3-sitzer-sofa-olivgrun-173x83x100cm',
        sofaConfigRomania: '/en-ro/configure/739,s',
        wishlistRomania: '/en-ro/library',
        desktopDevice: 'macbook-15',
        mobileDevice: 'iphone-se2',
    }

    context('Sofa desktop tests', () => {
        beforeEach(() => {
            cy.viewport(testParams.desktopDevice)
        })

        it('should complete sofa purchase flow', () => {
            cy.visit(testParams.sofaPdpUrl)
            cy.agreeCookies()
            cy.get('[data-testid="item-price"]:visible').invoke('text').then((pdpPrice) => {
                expect(extractPriceValue(pdpPrice)).to.be.greaterThan(0)
                sofa_pdp.configureYours()
                cy.url().should('contain', '/configure')
                sofa_pdp.getPricingv3AsString().then((configPrice) => {
                    checkIfPricesAreEqual(pdpPrice, configPrice)
                    sofa_configurator.addToCart()
                    // TODO: check if price on modal is proper
                    sofa_configurator.acceptModalAndGoToCart()
                    cy.url().should('contain', '/cart')

                    cy.get('[data-testid="cart-item"]').should('have.length', 1).as('cartItem')
                    cy.get('@cartItem').find('[data-testid="item-price"]').invoke('text')
                        .then((cartPrice) => {
                            checkIfPricesAreEqual(pdpPrice, cartPrice)
                        })
                })
            })
        })

        it('should properly handle sofa configurator functionality', () => {
            const mail = generateRandomEmail()
            cy.visit(testParams.sofaConfigUrl)
            cy.agreeCookies()

            // Verify basic elements
            cy.get(sofa_configurator.selectors.config_price).should('be.visible')
            cy.get(sofa_configurator.selectors.tylko_logo).should('be.visible')
            cy.wait(3000)

            // Test add and delete module functionality
            cy.get(sofa_configurator.selectors.add_module).should('be.visible').click({ timeout: 10000 })
            cy.get(sofa_configurator.selectors.add_module_icon).first().click()
            cy.get(sofa_configurator.selectors.delete_module).filter(':visible').click({ timeout: 15000 })
            cy.get(sofa_configurator.selectors.close_drawer_button).click()
            cy.get(sofa_configurator.selectors.delete_module).should('not.exist')

            // Test save for later
            sofa_configurator.saveForLaterGuest(mail)
            sofa_configurator.closeModal()
            sofa_configurator.closeModal()

            // Test category change
            sofa_configurator.changeSofaCategory('armchair')
            sofa_configurator.goBack()
            cy.get(sofa_configurator.selectors.delivery_modal).should('be.visible')
            cy.get(sofa_configurator.selectors.delivery_modal_accept).click()
            cy.url().should('contain', '/change-category')
            change_category.selectCategory('couch')
            cy.url().should('contain', '/configure')
        })

        it('should display all PDP sections correctly', () => {
            cy.visit(testParams.sofaPdpUrl)
            cy.agreeCookies()

            testSofaGallery()
            testSofaProductCard()
            testSofaHighlights()
            testSofaProductDetails()
            testSofaHero()
            verifyReviews()
            testSofaStickyBar()

            // Verify additional PDP sections
            cy.get(sofa_pdp.selectors.comfort_levels).scrollIntoView().should('be.visible')
            cy.get(sofa_pdp.selectors.color_selection).scrollIntoView().should('be.visible')
            cy.get(sofa_pdp.selectors.pdp_samples_open_button).should('be.visible')
            cy.get(sofa_pdp.selectors.value_proposition).scrollIntoView().as('valueProposition')
                .should('be.visible')
            cy.get('@valueProposition').find('picture').should('have.length.gte', 4)
            cy.get(sofa_pdp.selectors.sofa_creators).scrollIntoView().should('be.visible')
            cy.get(sofa_pdp.selectors.sofa_minigrid).scrollIntoView().should('be.visible')
            cy.get(sofa_pdp.selectors.showrooms_map).scrollIntoView().should('be.visible')
            verifyFooterDesktop()
        })

        it('should handle checkout with sofa properly', () => {
            cy.visit(testParams.sofaPdpUrl)
            cy.agreeCookies()

            sofa_pdp.addToCart()
            sofa_pdp.acceptModalAndGoToCart()
            cy.url().should('contain', '/cart')

            // Verify delivery price
            cy.get(cart.selectors.cart_summary_delivery).find('span').eq(1).invoke('text')
                .then((price) => {
                    const value = extractPriceValue(price)
                    expect(value).to.be.gt(0)
                })

            cart.goToCheckoutFromCart()
            login_page.continueAsGuest()

            // Verify checkout elements
            cy.get(checkout_page.selectors.checkout_price_delivery)
            cy.get(checkout_page.selectors.white_gloves_delivery).should('be.visible')
            cy.get(checkout_page.selectors.white_gloves_delivery_price).should('be.visible')
            cy.get(checkout_page.selectors.home_delivery).should('be.visible')
            cy.get(checkout_page.selectors.premium_services_section).should('be.visible')
            cy.get(checkout_page.selectors.old_sofa_collection_price).should('be.visible')
        })
    })

    context('Region restrictions', () => {
        it('should handle not allowed region correctly', () => {
            const mail = generateRandomEmail('cytest.pl')

            // Check PDP restrictions
            cy.visit(testParams.sofaPdpRomania)
            cy.agreeCookies()
            cy.get(sofa_pdp.selectors.cta_add_to_cart).should('not.exist')
            cy.get(sofa_pdp.selectors.configure_yours_button).should('not.exist')
            cy.get(sofa_pdp.selectors.navigation_a2c_sticky_bar).should('be.disabled')

            // Check configurator restrictions
            cy.visit(testParams.sofaConfigRomania)
            cy.wait(1000)
            cy.get(sofa_configurator.selectors.cta_add_to_cart).should('have.class', 'disabled')

            // Check wishlist restrictions
            sofa_configurator.saveForLaterGuest(mail)
            cy.visit(testParams.wishlistRomania)
            cy.get(wishlist.selectors.wishlist_item_a2c).should('have.length', 1).and('be.disabled')
        })
    })
})

// Helper functions for testing PDP sections
function testSofaGallery() {
    cy.get(sofa_pdp.selectors.sofa_pdp_gallery).should('be.visible')
    cy.get(sofa_pdp.selectors.sofa_pdp_gallery_asset).should('have.length', 6).as('galleryAssets')
    cy.get('@galleryAssets').first().should('be.visible')
    cy.get(sofa_pdp.selectors.sofa_pdp_gallery_pagination).should('be.visible')
    cy.get(sofa_pdp.selectors.s4l_heart_cta).should('be.visible').click()
    sofa_pdp.closeModal()
}

function testSofaProductCard() {
    cy.get(sofa_pdp.selectors.product_card).as('productCard').should('be.visible')

    // Verify all product card elements
    cy.get('@productCard').find(sofa_pdp.selectors.review_score).should('be.visible')
    cy.get('@productCard').find(sofa_pdp.selectors.sofa_info_title).should('be.visible')
    cy.get('@productCard').find(sofa_pdp.selectors.item_price).should('be.visible')
    cy.get('@productCard').find(sofa_pdp.selectors.sofa_info_description).should('be.visible')
    cy.get('@productCard').find(sofa_pdp.selectors.sofa_info_dimensions).should('be.visible')
    cy.get('@productCard').find(sofa_pdp.selectors.sofa_info_fabric).should('be.visible')
    cy.get('@productCard').find(sofa_pdp.selectors.sofa_info_color).should('be.visible')
    cy.get('@productCard').find(sofa_pdp.selectors.configure_yours_button).should('be.visible')
    cy.get('@productCard').find(sofa_pdp.selectors.cta_add_to_cart).should('be.visible')
    cy.get('@productCard').find(sofa_pdp.selectors.sofa_info_eu_made).should('be.visible')
    cy.get('@productCard').find(sofa_pdp.selectors.sofa_info_delivery).should('be.visible')
    cy.get('@productCard').find(sofa_pdp.selectors.sofa_info_payment_info_button).should('be.visible')
}

function testSofaHero() {
    cy.get(sofa_pdp.selectors.hero_video).scrollIntoView().should('be.visible').find('a')
        .should('have.attr', 'href').and('include', '/change-category')
}

function testSofaStickyBar() {
    cy.get(sofa_pdp.selectors.navigation_a2c_sticky_bar).should('be.visible')
    cy.get(sofa_pdp.selectors.navigation_config_yours_sticky_bar).should('be.visible')
}

function testSofaProductDetails() {
    cy.get(sofa_pdp.selectors.sofa_product_details).should('be.visible').as('productDetails')
    cy.get('@productDetails').find('[data-testid="toggle-button"]').should('be.visible').and('have.length', 4)
}

function testSofaHighlights() {
    cy.get('[data-testid="sofa-pdp-highlights-heading"]').should('be.visible')
    cy.get('[data-testid="sofa-pdp-highlights-tiles"]').find('picture')
        .should('have.length.gte', 4)
}
