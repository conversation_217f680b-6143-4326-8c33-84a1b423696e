import {
    testNotLoggedOnUserBuyingFurnitureOnMobile,
    testNotLoggedOnUserBuyingSampleOnMobile,
    testNotLoggedOnUserBuyingFurnitureAndSampleOnMobile,
} from "../../test_modules/goldenpath_tests"

const mobileDevices = ['iphone-se2']

describe('Goldenpath on mobile', () => {
    mobileDevices.forEach(device =>
        context(`${device} resolution`, () => {
            beforeEach(() => {
                cy.viewport(device)
            })

            const test1Params = [
                {region: 'pl', language: 'pl', category: 'sofa'},
                {region: 'fr', language: 'en', category: 'bookcases'},
                {region: 'fr', language: 'en', category: 'bookcases'},
                {region: 'de', language: 'de', category: 'wardrobes'},
                {region: 'nl', language: 'nl', category: 'sideboards'},
                {region: 'no', language: 'en', category: 'desks'},
                {region: 'ch', language: 'de', category: 'bookcases'},
                {region: 'it', language: 'en', category: 'chestofdrawers'},
                {region: 'es', language: 'es', category: 'tvstands'},
            ]
            test1Params.forEach(param => testNotLoggedOnUserBuyingFurnitureOnMobile(param.region, param.category, param.language, param.payment_method))

            const test2Params = [
                {region: 'be', language: 'de'}, //all types of samples //change payment on bancontact when new test card available
                {region: 'ro', language: 'en'}, //not all types of samples
            ]
            test2Params.forEach(param => testNotLoggedOnUserBuyingSampleOnMobile(param.region, param.language, param.payment_method))

            const test3Params = [
                {region: 'ch', language: 'de', category: 'vinylstorage'},
                {region: 'uk', language: 'en', category: 'bedsidetables'},
            ]
            test3Params.forEach(param => testNotLoggedOnUserBuyingFurnitureAndSampleOnMobile(param.region, param.category, param.language, param.payment_method))
        })
    )
})
