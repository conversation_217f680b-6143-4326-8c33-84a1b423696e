import {
    testNotLoggedOnUserBuyingFurniture,
    testNotLoggedOnUserBuyingSample,
    testNotLoggedOnUserBuyingFurnitureAndSample,
} from "../../test_modules/goldenpath_tests"

const desktopDevices = ['macbook-15']

describe('Goldenpath on desktop', () => {
    desktopDevices.forEach(device =>
        context(`${device} resolution`, () => {
            beforeEach(() => {
                cy.viewport(device)
                cy.setCookie('popup-popup-product_save_nouser1-done', 'ok')
            })

            const test1Params = [
                {region: 'nl', language: 'nl', category: 'sofa'},
                {region: 'it', language: 'it', category: 'sideboards'},
                {region: 'de', language: 'de', category: 'wardrobes'},
                {region: 'es', language: 'es', category: 'desks'},
                {region: 'fr', language: 'fr', category: 'bookcases'},
                {region: 'gr', language: 'en', category: 'wallstorage'},
                {region: 'at', language: 'de', category: 'bookcases'},
                {region: 'bg', language: 'en', category: 'allshelves'},
            ]
            test1Params.forEach(param => testNotLoggedOnUserBuyingFurniture(param.region, param.category, param.language))

            const test2Params = [
                {region: 'fr', language: 'fr'}, //all types of samples
                {region: 'se', language: 'sv'}, //not all types of samples
            ]
            test2Params.forEach(param => testNotLoggedOnUserBuyingSample(param.region, param.language))

            const test3Params = [
                {region: 'uk', language: 'en', category: 'wardrobes'},
                {region: 'pt', language: 'en', category: 'desks'},
                {region: 'cz', language: 'en', category: 'chestofdrawers'},
            ]
            test3Params.forEach(param => testNotLoggedOnUserBuyingFurnitureAndSample(param.region, param.category, param.language))
        }
    ))
})
