import CommonPage from './common_page'

class SofaChangeCategoryPage extends CommonPage {
    selectors = {
        sofa_type_link: '[data-testid="sofa-type-link"]'
    }

    sofaTypes = {
        'armchair': 0,
        'chaise_lounge': 1,
        'l_shaped': 2,
        'couch': 3,
        'u_shaped': 4,
    }

    selectCategory(sofaType) {
        if (sofaType in this.sofaTypes) {
            cy.get(this.selectors.sofa_type_link).eq(this.sofaTypes[sofaType]).click()
        } else {
            throw new Error(`Invalid sofa type: "${sofaType}". Available types are: ${Object.keys(this.sofaTypes).join(', ')}`)
        }
    }
}

export default new SofaChangeCategoryPage()