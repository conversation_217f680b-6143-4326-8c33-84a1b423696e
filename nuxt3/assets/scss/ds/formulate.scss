// Formulate inputs
// -----------------------------------------------------------------------------
@mixin base-input-wrapper {
  @apply
    text-grey-800
    border
    border-grey-800
    rounded-6
    normal-16
    basic-transition
    transition-border-color
    hover:border-grey-900;
}

@mixin input-oneline-left {
  @apply w-full rounded-r-none hover:z-2 hover:relative ;

  @screen lg-max {
    @apply rounded-r-6;
  }

  .formulate-input-wrapper {
    @apply rounded-r-none hover:z-2 hover:relative;

    @screen lg-max {
      @apply rounded-r-6;
    }
  }

  input {
    @apply rounded-r-none;
  }
}

@mixin input-oneline-right {
  width: calc(100% - 1px);

  .formulate-input-wrapper {
    @apply rounded-l-none;

    @screen lg-max {
      @apply rounded-l-6;
    }
  }

  @apply ml-[-1px] rounded-l-none;

  @screen lg-max {
    @apply rounded-r-6;
  }

  input {
    @apply rounded-l-none;
  }
}

@mixin label {
  @apply block w-full normal-14 text-offblack-800 pt-8 pl-16 mb-4 pr-24;
}

@mixin select-arrow {
  &::after {
    background-image: url('~@/assets/icons/chevronDown.svg');
    content: '';

    @apply w-20 h-20 absolute bottom-8 right-16 bg-no-repeat pointer-events-none;
  }
}

.formulate-input {
  /* stylelint-disable */
  &:has(select[disabled='disabled']) {
    // global disabled styles

    //select
    &[data-classification='select'] {
      .formulate-input-wrapper {
        &::after {
          @apply opacity-20;
        }

        .flag-sprite {
          @apply opacity-20;
        }
      }

      &[data-has-value='true'] {
        select {
          @apply border-grey-600 hover:border-grey-600 text-grey-600;
        }

        label {
          @apply text-grey-800;
        }
      }

      select {
        @apply border-grey-600 hover:border-grey-600 text-grey-600 cursor-default;
      }

      label {
        @apply text-grey-800;
      }
    }
  }

  .formulate-input-wrapper {
    // global wrapper styles
  }

  .formulate-input-label {
    // global label styles
  }

  .formulate-input-element {
    // Global field-level wrapper styles
  }

  .formulate-input-help {
    // Global help text styles
    @apply normal-12 mt-4 text-grey-900;
  }

  .formulate-input-errors {
    // Global error message list wrapper
    @apply normal-12 text-orange pl-16 mt-4;
  }

  .formulate-input-error,
  .formulate-file-upload-error {
    // Error message styles
  }
    .formulate-file-upload-error {
    // Error message styles
    @extend .formulate-input-errors;
  }

  .formulate-input-group-item {
    // Group of items (like list of checkboxes) wrapper
  }

  // Text inputs
  // -----------------------------------------------------------------------------

  &[data-classification='text'] , &[data-classification='textarea'], &[data-family='text'], &[data-type='textarea']{
    &.input-oneline-left {
      @include input-oneline-left;
    }
    &.input-oneline-right {
      @include input-oneline-right;
    }
    .formulate-input-wrapper {
      @include base-input-wrapper;

      &:has(> .formulate-input-element > .focus-visible) {
        @apply border-offblack-600;
      }

      input:-webkit-autofill,
      input:-webkit-autofill:hover,
      input:-webkit-autofill:focus,
      input:-webkit-autofill:active {
        box-shadow: 0 0 0 30px #fff inset !important;
      }
    }
    &.select-arrow {
      @apply relative;
      @include select-arrow;
    }

    &[data-is-showing-errors='true'], &[data-invalid='true'] {
      .formulate-input-wrapper {
        @apply border-orange z-1 relative;

        .formulate-input-label {
          @apply text-orange;
        }
      }
    }

    label {
      @include label;
    }

    input {
      // Style all text-like input elements
      @apply pl-16 pb-8 rounded-6 w-full text-offblack-600 bg-transparent;

      &::placeholder {
        @apply text-grey-800;
      }
    }
  }

  // Slider inputs
  // -----------------------------------------------------------------------------

  &[data-classification='slider'] {
    input {
      // Style range inputs
    }
  }

  // Textarea inputs
  // -----------------------------------------------------------------------------

  &[data-classification='textarea'], &[data-type='textarea'] {
    &[data-is-showing-errors='true'], &[data-invalid='true'] {
      .formulate-input-wrapper {
        @apply border-orange z-1 relative;

        .formulate-input-label {
          @apply text-orange;
        }
      }
    }

    .formulate-input-element--textarea{
       @apply pr-8;
    }
    .formulate-input-wrapper {
      @include base-input-wrapper;

      &:has(> .formulate-input-element > .focus-visible) {
        @apply border-offblack-600;
      }

      input:-webkit-autofill,
      input:-webkit-autofill:hover,
      input:-webkit-autofill:focus,
      input:-webkit-autofill:active {
        box-shadow: 0 0 0 30px #fff inset !important;
      }

    }

    label {
      @include label;
    }

    textarea {
      @apply pl-16 pb-8 rounded-6 w-full text-offblack-600 bg-transparent;
      &:focus-visible {
        box-shadow: 0 0 0 30px #fff inset !important;
      }
      &::placeholder {
        @apply text-grey-800;
      }
    }

    &[data-is-showing-errors='true'], &[data-invalid='true'] {
      .formulate-input-wrapper {
        @apply border-orange z-1 relative;

        .formulate-input-label {
          @apply text-orange;
        }
      }
    }
  }

  // Button inputs
  // -----------------------------------------------------------------------------

  &[data-classification='button'] {
    &.btn-cta {
      .formulate-input-element--submit--label {
        @apply bold-16;
      }
    }
  }

  // Select lists
  // -----------------------------------------------------------------------------

  &[data-classification='select'] {
    &:has(select:-webkit-autofill),
    &:has(select:-webkit-autofill:hover),
    &:has(select:-webkit-autofill:focus),
    &:has(select:-webkit-autofill:active){
      .flag-sprite {
        @apply z-1;
      }
      label{
        @apply z-1 relative;
      }
      .formulate-input-wrapper {
        &::after {
          @apply z-1;
        }
      }

    }
    select:-webkit-autofill,
    select:-webkit-autofill:hover,
    select:-webkit-autofill:focus,
    select:-webkit-autofill:active {
      box-shadow: 0 0 0 30px #fff inset !important;
    }
    .formulate-input-wrapper {
      @apply relative pl-16 pt-8 h-[62px];

      @include select-arrow;
    }

    &[data-is-showing-errors='true'] {
      select {
        @apply border-orange;
      }
      .formulate-input-wrapper {
        @apply z-1 relative;

        .formulate-input-label {
          @apply text-orange;
        }
      }
    }

    &[data-has-value='true'] {
      .country-select {
        @apply pl-[56px];
      }

      select {
        @apply text-offblack-800;
      }

      label {
        @apply text-grey-900;
      }
    }
    label {
      @apply block w-full normal-14 text-offblack-800 truncate;
    }

    select {
      @include base-input-wrapper;

      @apply
        absolute
        inset-0
        bg-transparent
        pl-[14px]
        pt-24
        w-full
        appearance-none
        cursor-pointer
        active:border-grey-900
        focus:border-grey-900
        bg-no-repeat;

      -webkit-appearance:none;
    }
  }

  // Box radio
  // -----------------------------------------------------------------------------
    &[data-classification='box'], &[data-family="box"]{
      &[data-type='radio']{
        input {
           appearance: none;
          -webkit-appearance: none;
          -moz-appearance: none;
          @apply w-24 h-24 border-2 border-solid border-neutral-600 rounded-full cursor-pointer hover:bg-neutral-300 basic-transition transition;
          &:checked {
            @apply border-black relative;
            &::before {
              content: '';
              @apply absolute w-12 h-12 bg-black rounded-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
            }
          }
        }
      }
    }
// Box checkobox
// -----------------------------------------------------------------------------
  &[data-classification='box'], &[data-family="box"]{
    &[data-type='checkbox'] {
      &[data-is-showing-errors='true'], &[data-invalid='true'] {
         &.error-box-padding {
          @apply mb-32;
         }
        .formulate-input-element {
          input {
            @apply border-orange;
          }
        }
      }

      .formulate-input-errors {
        @apply pl-28;
      }
      .formulate-input-wrapper {
        @apply flex;
      }
      .formulate-input-wrapper {
        @apply flex;
      }
      .formulate-input-label {
        @apply cursor-pointer;
      }
      .formulate-input-element {
        @apply pl-8;
      // Box input (checkbox and radio) wrapper (might want flex here)
      input {
        appearance: none;

        &:checked {
          @apply border-black bg-black hover:bg-neutral-750 hover:border-neutral-750;
          &::before {
            @apply block;
          }
        }

        &::before {
          content: '';
          @apply
            bg-no-repeat
            bg-[url('~/assets/icons/accept-white.svg')]
            absolute
            top-4
            left-2
            right-0
            bottom-0
            hidden;
        }

        @apply
          m-0
          cursor-pointer
          relative
          bg-white
          h-24
          w-24
          mr-8
          border-2
          border-solid
          border-neutral-500
          rounded-2
          hover:bg-neutral-300
          basic-transition
          transition;
      }
    }
    }
  }

  // File inputs
  // -----------------------------------------------------------------------------

  &[data-classification='file'] {
    .formulate-input-wrapper{
        position: relative;
    }
  }

  // Image uploads
  // -----------------------------------------------------------------------------

  [data-type='image'] {
    // image uploads
    .formulate-file-add {
        @apply absolute top-0 left-0;
        @apply inline-flex items-center justify-center bg-orange text-offwhite-600 bold-16 cursor-pointer;
        @apply rounded-[30px] py-[10px] px-32;

        transition: background-color 0.3s ease, color 0.3s ease, transform 0.3s ease, border-color 0.3s ease;

        @apply hover:bg-orange-900 active:bg-orange-900;
        @apply disabled:bg-grey-600 disabled:text-grey-700 disabled:cursor-not-allowed;

        input {
            @apply cursor-pointer absolute top-0 left-0 right-0 bottom-0 opacity-0;
            display: block !important;
        }
    }

    .formulate-input-upload-area {
        input {
          @apply hidden;
        }
    }
  }
}

// Form-level errors
// -----------------------------------------------------------------------------

.formulate-form-errors {
  .formulate-form-error {
    // form errors (not specific to a field)
  }
}
.formkit-input[type=number]::-webkit-outer-spin-button,
.formkit-input[type=number]::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
.formkit-input[type=number] {
    -moz-appearance:textfield;
}
.ty-input__credentials {
  &[data-type='tyPassword'], &:not([data-empty='true']) {
    input::-webkit-credentials-auto-fill-button,
    input::-webkit-contacts-auto-fill-button {
        right: 50px;
      }
  }

  input::-webkit-credentials-auto-fill-button,
  input::-webkit-contacts-auto-fill-button {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
  }
}
