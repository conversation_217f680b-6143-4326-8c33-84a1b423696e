nav#feed {
  @apply top-[var(--ribbon-height)] right-0 md-max:left-0 fixed z-10;

  .content-card-background{
    @apply fixed z-[-1] top-0 -left-full h-screen w-full bg-black opacity-40;
  }

  .ab-feed {
    @apply flex flex-col w-full relative md:w-[600px] bg-neutral-200 h-[calc((100vh)-var(--ribbon-height))] overflow-hidden;

    & .ab-pinned-indicator .fa-star {
      right: -20px;
      top: 3px;
    }

    & .ab-feed-body {
      @apply flex flex-col p-16 pt-48 md:pt-96 md:p-48 h-full bg-neutral-200 border-l-neutral-200 border-0 overflow-auto;
    }

    & .ab-no-cards-message {
      @apply text-offblack-600 text-16 leading-[1.4] font-normal tracking-[0.2px] m-auto;
    }

    /* buttons on top*/
    & .ab-feed-buttons-wrapper {
      @apply bg-neutral-200 border-l-neutral-200 absolute top-0 right-0 w-full;
      box-shadow: none;
    }

    & .fa.fa-refresh.ab-refresh-button::before {
      content: ''
    }

    & .fa.fa-times.ab-close-button {
      @apply w-24 md:w-48 h-24 md:h-48 p-0 absolute right-16 top-16 md:right-24 md:top-24 md:bg-neutral-100 rounded-full flex items-center justify-center;
    }

    & .fa.fa-times.ab-close-button::before {
      content: '';
      @apply w-24 h-24 block bg-[url('~/assets/icons/close.svg')] bg-center bg-no-repeat;

    }

    /* single card */
    & .ab-card {
      @apply border-0 rounded-8 shrink-0;
      box-shadow: none;

      & .ab-title, .ab-title a {
        @apply block p-0 text-offblack-600 text-16 leading-[20px] font-medium tracking-[0.2px];
      }

      & .ab-description {
        @apply p-0 pt-2 text-offblack-600 text-14 leading-[20px] font-normal tracking-[0.2px];
      }

      & .ab-url-area {
        @apply m-0 text-offblack-600 pt-8 md:pt-16;
      }

      & .ab-url-area a {
        @apply w-fit mt-0 block p-0 border-b text-offblack-600 border-b-neutral-900 min-h-20 text-12 leading-[18px] font-medium tracking-[0.2px] pl-0 hover:border-0;
      }

      /* card with image */
      &.ab-classic-card.with-image {
        & .ab-image-area {
          @apply left-0 w-[104px] h-full md:w-[138px] md:h-full p-0 border-0 rounded-8;

          & img {
            @apply w-full h-full object-cover;
          }
        }

        & .ab-description {
          @apply pt-2;
        }

        & .ab-card-body {
          @apply pt-[6px] pb-8 pr-16 pl-120 md:py-12 md:pl-[154px];
        }
      }

      &.ab-effect-card.ab-captioned-image.with-image {
        & .ab-card-body {
          @apply p-16;
        }

        & a {
          @apply p-0;

        }
      }

      &.ab-effect-card.ab-image-only.with-image a{
        @apply p-0;
      }
    }
  }

  .content-card-background:has(+.ab-feed) {
    left: 0;
  }
}
