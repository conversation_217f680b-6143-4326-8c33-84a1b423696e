<svg width="51" height="63" viewBox="0 0 51 63" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2712_63048)">
<g filter="url(#filter0_d_2712_63048)">
<path d="M25.2145 0.842773C19.59 0.849154 14.1977 3.0863 10.2206 7.0634C6.24352 11.0405 4.00638 16.4328 4 22.0573C4 40.2101 23.2859 53.92 24.108 54.4938C24.4322 54.7209 24.8186 54.8428 25.2145 54.8428C25.6104 54.8428 25.9967 54.7209 26.321 54.4938C27.1431 53.92 46.429 40.2101 46.429 22.0573C46.4226 16.4328 44.1855 11.0405 40.2084 7.0634C36.2313 3.0863 30.839 0.849154 25.2145 0.842773Z" fill="#FF3C00"/>
</g>
<path d="M26.3552 15.8428H23.3307V20.7563H21V23.5043H23.3307V35.8428H29.6111V33.0948H26.3552V23.5043H29.6111V20.7563H26.3552V15.8428Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_2712_63048" x="0" y="0.842773" width="50.4287" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2712_63048"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2712_63048" result="shape"/>
</filter>
<clipPath id="clip0_2712_63048">
<rect width="51" height="62" fill="white" transform="translate(0 0.842773)"/>
</clipPath>
</defs>
</svg>
