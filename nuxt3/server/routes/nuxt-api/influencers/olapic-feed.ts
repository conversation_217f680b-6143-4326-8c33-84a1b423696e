export default defineCachedEventHandler(async () => {
  try {
    const data = await $fetch('https://photorankapi-a.akamaihd.net/customers/220749/media/recent?auth_token=b71903afda0e586b72842e785c1df447b159f040714c8cb63aa6ae8b2eb41470&version=v2.2');

    return data?.data?._embedded?.media.map((item: any) => ({
      img: item.images.normal,
      alt: item.caption,
      avatar: item._embedded.uploader.avatar_url,
      name: item._embedded.uploader.name,
      id: item.id
    }));
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}, {
  base: 'redis',
  maxAge: 60 * 60 * 2
});
