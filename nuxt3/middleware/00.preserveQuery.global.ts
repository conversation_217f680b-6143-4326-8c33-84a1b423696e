import { isNil, isEmpty } from 'lodash-es';

/**
 * Middleware to preserve marketing campaign query parameters across SPA navigation
 * 
 * This middleware ensures that important marketing campaign parameters (UTM and GCLID)
 * are maintained when users navigate between pages in the single-page application.
 * 
 * Preserved parameters:
 * - utm_source: Identifies the source of traffic
 * - utm_medium: Specifies the marketing medium
 * - utm_campaign: Names the specific campaign
 * - gclid: Google Click Identifier for tracking Google Ads
 * 
 * The middleware only runs on client-side navigation and when there are existing query parameters
 * to preserve. It checks if any of these parameters are missing in the destination route
 * and adds them if necessary.
 */
export default defineNuxtRouteMiddleware(async (to, from) => {
  
  if (import.meta.server || isEmpty(from.query)) { return; }

  const fromQuery = 
    ['utm_source', 'utm_medium', 'utm_campaign', 'gclid']
    .map(queryKey =>
      isNil(from?.query[queryKey]) ? null : queryKey)
      .filter(Boolean)

  if(fromQuery?.length){
    const isQueryLacking = fromQuery.some(key => isNil(to?.query[key]))
    
    if(isQueryLacking) {
      const queryToPass = fromQuery.reduce((acc, key) => {
        if (key && from.query[key]) {
          acc[key] = from.query[key];
        }
        return acc;
      }, {});
      
      return navigateTo({
        path: to.path,
        query: {
          ...to.query,
          ...queryToPass
        }
      });
    }
    
  }  
});
