<template>
  <div class="grid grid-cols-12">
    <TheFAQBreadcrumbs
      class="col-span-12 lg:mb-48"
      v-bind="{
        categories,
        tags
      }"
    />

    <TheFAQSideMenu
      class="hidden col-span-3 lg:block xl2:col-span-2"
      v-bind="{
        categories
      }"
    />

    <div class="col-span-12 lg-max:mb-16 lg:col-start-5 lg:col-end-12 xl:col-start-5 xl:col-end-11 xl2:col-start-4 xl2:col-end-10">
      <NuxtPage
        v-bind="{
          tags,
          articles,
          categories
        }"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>

defineProps<{
  tags: FaqTag[],
  categories: FaqCategory[],
  articles: FaqArticle[],
}>();

</script>
