<template>
  <main class="reset-modern-navigation-page-padding bg-beige-100 text-neutral-900">
    <Head>
      <Title>{{ t('lp.reviews.meta.title') }}</Title>
      <Meta
        name="og:title"
        hid="og:title"
        :content="t('lp.reviews.meta.title')"
      />
      <Meta
        name="description"
        hid="description"
        :content="t('lp.reviews.meta.description', { count: reviewsCount })"
      />
      <Meta
        name="og:description"
        hid="og:description"
        :content="t('lp.reviews.meta.description', { count: reviewsCount })"
      />
    </Head>

    <SectionHeroImage
      data-section="hero"
      v-bind="{
        variant: 'center',
        containerAdditionalClasses: 'bg-gradient-to-b from-transparent to-black/50 lg:pb-96',
        imagePath: 'lp/sofa-teaser/hero',
        imageAlt: t('lp.reviews-list.hero.headline'),
        imageType: 'M T SD LD XLD',
        headingCopy: t('lp.reviews-list.hero.headline'),
        subheadingCopy: t('lp.reviews-list.hero.subheading'),
        headerColorClass: 'text-offwhite-600',
        additionalClassHeading: 'semibold-32 lg:semibold-54 mb-32',
        additionalClassSubheading: '!normal-12 lg:!semibold-18 !mb-8',
        additionalTextClass: 'lg:normal-18 lg:!mt-8'
      }"
    >
      <template #ctaSlot>
        <div class="flex flex-row gap-16 justify-center">
          <BaseLink
            variant="filled-dark"
            class="py-12 px-20 lg:py-20 lg:px-32 rounded-full"
            v-bind="{
              href: $addLocaleToPath('review-form'),
              trackData: {
                eventType: 'NOEEC',
                eventCategory: 'lp_clicks',
                eventAction: 'Reviews_list',
                eventLabel: 'Write a review'
              }
            }"
          >
            {{ t('lp.reviews-list.hero.cta_write') }}
          </BaseLink>
          <BaseButton
            variant="outlined-dark"
            class="py-12 px-20 lg:py-20 lg:px-32 rounded-full"
            v-on:click="scrollToElement({ element: '#reviews-list', offset: 32 })"
          >
            {{ t('lp.reviews-list.hero.cta_check') }}
          </BaseButton>
        </div>
      </template>
    </SectionHeroImage>

    <section class="bg-white">
      <BaseCarousel
        data-section="category-list-carousel"
        class="py-16 grid-container overflow-hidden"
        v-bind="{
          isNavigation: false,
          name: `categoryListCarousel`,
          options: swiperOptions,
          swiperRootClasses: '!w-full !overflow-visible',
          swiperWrapperClasses: 'items-center'
        }"
      >
        <BaseCarouselSlide
          v-for="category in categories"
          :key="category.name"
          class="mr-0 !w-max"
        >
          <BaseButton
            data-testid="category-list-carousel-slide"
            class="rounded-full normal-16 py-12 px-16 lg:py-16"
            :variant="activeCategory.name === category.name ? 'filled' : 'custom'"
            :class="activeCategory.name !== category.name ? 'text-neutral-900' : ''"
            v-on:click="activeCategory = category"
          >
            {{ t(category.nameKey) }}
          </BaseButton>
        </BaseCarouselSlide>
      </BaseCarousel>
    </section>

    <section
      id="reviews-list"
      class="grid-container  py-48 lg:py-96"
    >
      <div class="grid grid-cols-1 lg:grid-cols-12">
        <div class="col-span-12 lg:col-span-3">
          <aside class="flex flex-col items-center px-24 py-48 rounded-12 border border-neutral-500 lg:mt-56 lg:sticky lg:top-[128px] lg:left-0">
            <p
              class="semibold-72 lg:semibold-96 mb-24"
              data-testid="review-score"
              v-html="`${reviewsAverageScore}/5`"
            />
            <div
              class="flex rating-stars"
              data-testid="review-global-stars"
            >
              <IconStar
                v-for="number in 5"
                :key="number"
                data-testid="review-global-star"
                class="w-24 h-24 gap-4"
                :style="{
                  fill: number <= reviewsAverageScore ? 'var(--active)' : 'var(--inactive); opacity: 0.38',
                  gap: 4
                }"
              />
            </div>
            <p
              class="mt-12 normal-16 text-center"
              v-html="t('lp.reviews-list.globalscore')"
            />
            <BaseLink
              class="inline-flex semibold-16 text-neutral-900 mt-24"
              data-testid="reviews-learn-more"
              variant="underlined"
              target="_blank"
              v-bind="{
                href: $addLocaleToPath('review-list'),
                trackData: {
                  eventType: 'NOEEC',
                  eventCategory: 'pdp_clicks',
                  eventAction: 'Reviews',
                  eventLabel: 'See all reviews'
                }
              }"
            >
              {{ t('lp.reviews-list.globalscorecta') }}
            </BaseLink>
          </aside>
        </div>
        <div class="col-span-12 lg:col-span-8 lg:col-start-5 lg-max:mt-48">
          <aside class="flex flex-row lg-max:flex-wrap items-center pb-16 lg:border-b border-neutral-500 gap-12">
            <span class="semibold-16 min-w-max">{{ t('lp.reviews-list.filterby') }}</span>
            <ul class="flex flex-row flex-wrap gap-12">
              <li
                v-for="filter in filters"
                :key="filter.name"
              >
                <BaseButton
                  class="rounded-full normal-16 px-16 py-8 h-48 border-neutral-500 border"
                  :variant="activeFilters.includes(filter.value) ? 'filled' : 'outlined'"
                  :class="!activeFilters.includes(filter.value) ? 'text-neutral-900' : ''"
                  v-on:click="toggleFilter(filter.value)"
                >
                  {{ t(filter.name) }}
                </BaseButton>
              </li>
            </ul>
            <div class="flex flex-row lg-max:flex-wrap lg-max:justify-end items-center ml-auto lg-max:w-full gap-16">
              <span class="block w-full h-1 lg:w-1 lg:h-24 bg-neutral-500" />
              <FormKit
                v-model="sort"
                type="tySelect"
                v-bind="{
                  name: 'sort',
                  options: sortOptions,
                  classes: {
                    wrapper: 'rounded-full h-full',
                    outer: 'h-48',
                    input: '!pt-0 pr-[42px] pb-0 pl-16',
                    dropdown: 'h-24 w-24'
                  }
                }"
              />
            </div>
          </aside>
          <section class="mt-32">
            <article
              v-for="(review, index) in reviews"
              :key="index"
              class="py-24 border-b border-neutral-400 flex flex-col"
              data-testid="review-article"
            >
              <div
                class="flex flex-wrap rating-stars lg-max:order-1"
                data-testid="review-article-stars"
              >
                <IconStar
                  v-for="number in 5"
                  :key="number"
                  data-testid="review-article-star"
                  class="w-16 h-16 gap-4"
                  :style="{
                    fill: number <= review.score ? 'var(--active)' : 'var(--inactive)',
                    gap: 4
                  }"
                />
                <span class="normal-16 ml-12">{{ review.name }}</span>
                <NuxtTime
                  class="ml-auto text-neutral-700 normal-10 normal-14"
                  data-testid="review-article-date"
                  :datetime="new Date(review.createdAt)"
                  year="numeric"
                  month="long"
                  day="numeric"
                />
                <div class="w-full flex flex-row gap-8 text-neutral-700 normal-14 lg-max:flex-wrap lg-max:mt-4">
                  <span>{{ t('lp.reviews-list.category') }} {{ review.category }}</span>
                  <span>{{ t('lp.reviews-list.productLine') }} {{ review.productLine }}</span>
                  <span>{{ t('lp.reviews-list.material') }} {{ review.material }}</span>
                  <span>{{ t('lp.reviews-list.color') }} {{ review.color }}</span>
                </div>
              </div>
              <img
                v-bind="{
                  src: review.photoSmallWebp,
                  alt: review.showingOriginal ? review.originalTitle : review.translatedTitle
                }"
                class="aspect-square w-full lg:max-w-[114px] object-cover lg:mt-16 lg-max:mb-32 rounded-8 cursor-pointer lg-max:order-0"
                data-testid="review-article-img"
                v-on:click="handleImageZoom(review)"
              >
              <h3
                class="mt-16 semibold-24 lg-max:order-2"
                data-testid="review-article-title"
                v-html="review.showingOriginal ? review.originalTitle : review.translatedTitle"
              />
              <p
                class="mt-8 normal-16 lg-max:order-3"
                data-testid="review-article-location"
                v-html="review.showingOriginal ? review.originalDescription : review.translatedDescription"
              />
              <p class="mt-16 normal-14 text-neutral-700 lg-max:order-4">
                {{ t('lp.reviews-list.originalLanguage') }}:
                <span class="capitalize">{{ getLanguageFromCountry(review.country) }}</span>
                <span
                  v-if="review.country !== regionName"
                  class="underline cursor-pointer ml-8"
                  v-on:click="handleShowOriginal(review)"
                >{{ review.showingOriginal ? t('lp.reviews-list.showTranslated') : t('lp.reviews-list.showOriginal') }}</span>
              </p>
            </article>
            <BaseButton
              v-if="hasMore"
              class="mt-32"
              variant="outlined"
              v-on:click="loadMore"
            >
              {{ t('lp.reviews-list.loadMore') }}
            </BaseButton>
          </section>
        </div>
      </div>
    </section>

    <LazyBaseModal
      v-if="isDrawerOpen && activeDrawer"
      v-model="isDrawerOpen"
      wrapper-class="flex items-center justify-center"
    >
      <template #default>
        <div class="flex flex-row items-center w-max h-max justify-center relative">
          <img
            class="rounded-24"
            :src="activeDrawer?.photoMediumWebp"
            :alt="activeDrawer?.title"
          >
          <BaseButton
            class="block z-2 ml-auto top-16 right-16 absolute lg:top-24 lg:right-24"
            data-testid="drawer-close-button"
            v-bind="{
              variant: 'close',
              trackData: {}
            }"
            v-on:click="isDrawerOpen = false"
          >
            <IconPlus class="rotate-45" />
          </BaseButton>
        </div>
      </template>
    </LazyBaseModal>
  </main>
</template>

<script setup lang="ts">
import type { Swiper } from 'swiper';
import type { SwiperOptions } from 'swiper/types';
import scrollToElement from '~/helpers/scrollToElement';
import { REVIEWS } from '~/api/reviews';
import { GRID_CATEGORIES } from '~/consts/categories';
import { getLanguageFromCountry } from '~/utils/countryToLanguage';
import IconStar from '~/assets/icons/star.svg';
import IconPlus from '~/assets/icons/plus.svg';

const { t } = useI18n();
const { locale } = useLocale();
const { $logException } = useNuxtApp();
const { reviewsCount, reviewsAverageScore, reviewsCategories, regionName } = useGlobal();

interface item {
  name: string,
  title: string,
  translatedTitle: string,
  translatedDescription: string,
  description: string,
  originalTitle?: string,
  originalDescription?: string,
  showingOriginal?: boolean,
  score: number,
  createdAt: string,
  country: string,
  photoSmallWebp: string
  photoMediumWebp: string,
  productLine: string,
  category: string,
  productName: string,
  material: string,
  color: string,
  tags: string[]
}

const sortOptions = [
  { label: 'Newest', value: '-created_at' },
  { label: 'Oldest', value: 'created_at' },
  { label: 'Highest rating', value: '-score' },
  { label: 'Lowest rating', value: 'score' }
] as const;

const hasMore = ref(true);
const currentPage = ref(1);
const activeFilters = ref<string[]>([]);
const isDrawerOpen = ref(false);
const reviews = ref<item[]>([]);
const sort = ref(sortOptions[0].value);
const activeDrawer = ref<item | null>(null);
const activeCategory = ref(GRID_CATEGORIES().all);

const { data } = await REVIEWS(locale.value, 1);

reviews.value = data.value?.results?.map((review: any) => ({
  ...review,
  originalTitle: review.title,
  originalDescription: review.description,
  showingOriginal: false
})) || [];

const categories = [GRID_CATEGORIES().all, ...reviewsCategories.map(category => GRID_CATEGORIES()[category])];

const filters = [
  { name: 'Assembly', value: 'assembly' },
  { name: 'Comfort', value: 'comfort' },
  { name: 'Customer Support', value: 'customer_support' },
  { name: 'Delivery', value: 'delivery' },
  { name: 'Fabric', value: 'fabric' },
  { name: 'Style', value: 'style' },
  { name: 'Quality', value: 'quality' }
];

const swiperOptions: SwiperOptions = {
  slidesPerView: 'auto',
  slidesPerGroupAuto: true,
  slideToClickedSlide: true,
  shortSwipes: false,
  freeMode: true,
  threshold: 4
};

const loadMore = async () => {
  currentPage.value++;

  try {
    const tags = activeFilters.value.length > 0 ? activeFilters.value : undefined;
    const { data } = await REVIEWS(locale.value, currentPage.value, activeCategory.value.name === 'allshelves' ? '' : activeCategory.value.name, sort.value, tags);

    if (data.value?.next === null) {
      hasMore.value = false;
    }

    const newReviews = data.value?.results?.map((review: any) => ({
      ...review,
      originalTitle: review.title,
      originalDescription: review.description,
      showingOriginal: false
    })) || [];
    reviews.value.push(...newReviews);
  } catch (e) {
    return $logException(e);
  }
};

const handleImageZoom = (review: item) => {
  activeDrawer.value = review;
  isDrawerOpen.value = true;
};

const handleShowOriginal = (review: item) => {
  review.showingOriginal = !review.showingOriginal;
};

const toggleFilter = (filterValue: string) => {
  const index = activeFilters.value.indexOf(filterValue);

  if (index > -1) {
    activeFilters.value.splice(index, 1);
  } else {
    activeFilters.value.push(filterValue);
  }
};

watch(activeCategory, async (category) => {
  const tags = activeFilters.value.length > 0 ? activeFilters.value : undefined;

  const { data } = await REVIEWS(locale.value, 1, category.name === 'allshelves' ? '' : category.name === 'vinylstorage' ? 'vinyl_storage' : category.name, sort.value, tags);

  hasMore.value = data.value?.next !== null;

  reviews.value = data.value?.results?.map((review: any) => ({
    ...review,
    originalTitle: review.title,
    originalDescription: review.description,
    showingOriginal: false
  })) || [];
  currentPage.value = 1;
});

watch(sort, async (sort) => {
  const tags = activeFilters.value.length > 0 ? activeFilters.value : undefined;
  const { data } = await REVIEWS(locale.value, 1, activeCategory.value.name === 'allshelves' ? '' : activeCategory.value.name, sort, tags);

  hasMore.value = data.value?.next !== null;

  reviews.value = data.value?.results?.map((review: any) => ({
    ...review,
    originalTitle: review.title,
    originalDescription: review.description,
    showingOriginal: false
  })) || [];
  currentPage.value = 1;
});

watch(activeFilters, async (filters) => {
  const tags = filters.length > 0 ? filters : undefined;
  const { data } = await REVIEWS(locale.value, 1, activeCategory.value.name === 'allshelves' ? '' : activeCategory.value.name, sort.value, tags);

  hasMore.value = data.value?.next !== null;

  reviews.value = data.value?.results?.map((review: any) => ({
    ...review,
    originalTitle: review.title,
    originalDescription: review.description,
    showingOriginal: false
  })) || [];
  currentPage.value = 1;
}, { deep: true });

definePageMeta({ tag: ['reviews'] });
</script>

<style lang="scss">
.rating-stars {
  --active: #1D1E1F;
  --inactive: #6F7173;
}
</style>
