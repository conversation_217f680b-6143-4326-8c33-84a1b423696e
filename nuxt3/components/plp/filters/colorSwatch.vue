<template>
  <BaseLink
    as-nuxt-link
    class="flex flex-col items-center transition-all basic-transition"
    variant="custom"
    v-bind:class="[
      { 'opacity-20 pointer-events-none' : !isAvailable },
      { 'disabled': !isAvailable }
    ]"
    v-bind="{
      to: {
        path: routePath,
        query: handleQuery,
      },
      disabled: !isAvailable,
      trackData: {
        event: 'userInteraction',
        eventCategory: 'grid-filters',
        eventAction: 'in_grid_drawer',
        eventLabel: `${color.trackDataEventLabel}|${color.queryValue}`
      }
    }"
  >
    <span
      class="inline-block bg-cover overflow-hidden rounded-full border-2 transition-all basic-transition w-[36px] h-[36px]"
      v-bind:class="[ isActive ? 'border-orange-900': 'border-grey-600 hover:border-orange-900']"
      v-bind="{
        [`data-testid`]: color['data-testid'],
        style: { backgroundImage: `url(${color.iconPath})` },
      }"
    />
    <span
      class="text-center mt-4 normal-14 text-offblack-600"
      v-html="$t(color.labelKey)"
    />
  </BaseLink>
</template>

<script setup lang="ts">

import useFiltersUtils from '~/composables/plp/useFiltersUtils';
import { FilterQueryParams, type IPLPColorFilter } from '~/utils/filters';
import useFilters from '~/composables/plp/useFilters';

const props = withDefaults(defineProps<{
  color: IPLPColorFilter;
  queryVariableName?: FilterQueryParams;
}>(), {
  queryVariableName: FilterQueryParams.COLORS
});
const route = useRoute();

const { isFilterActive, buildQuery } = useFiltersUtils();
const { isFilterValueAvailable } = useFilters();

const isActive = computed(() => route.query && isFilterActive(route.query, props.queryVariableName, props.color.queryValue).isActive);
const isAvailable = computed(() => isFilterValueAvailable(props.queryVariableName, props.color.queryValue));
const handleQuery = computed(() => buildQuery(route.query, props.queryVariableName, props.color.queryValue));
const routePath = computed(() => route.path);

</script>
