<template>
  <section>
    <BreadCrumbs
      data-section="section-breadcrumbs"
      class="grid-container text-neutral-900"
      :links="
        categoryBreadCrumbs?.map((category) => ({
          isNuxtLink: true,
          label: $t(category.pluralNameKey || category.nameKey),
          url: `${$addLocaleToPath('plp')}${$t(category.urlPathKey)}`
        }))
      "
    />
    <div class="mt-32 grid-container">
      <h1
        class="semibold-36 lg:semibold-42 xl:bold-46 text-offblack-600"
        data-testid="plp-category-title"
        v-html="$t(currentCategory.pluralNameKey || currentCategory.nameKey)"
      />
      <!--      <p-->
      <!--        class="semibold-16 lg:semibold-24 text-offblack-600"-->
      <!--        data-testid="plp-category-description"-->
      <!--        v-html="$t(currentCategory.descriptionKey)"-->
      <!--      />-->
    </div>
    <BaseCarousel
      data-section="grid-navigation"
      is-scrollbar
      class="!justify-start max-w-[1632px] mx-auto mt-12 lg:mt-32"
      v-bind="{
        name: `plpNavigationSwiper`,
        options: swiperOptions,
        class:[
          { 'pointer-events-none' : isPendingState },
          { '[&_.swiper-wrapper]:!translate-x-0' : isSwiperLocked }
        ],
        swiperRootClasses: '!mx-0 full-width-carousel w-full',
        navPrevElClasses: ['md:!left-16 plp-categories-nav-button', !isSwiperInitialized && 'swiper-button-disabled'],
        navNextElClasses: ['md:!right-16 plp-categories-nav-button', !isSwiperInitialized && 'swiper-button-disabled'],
        scrollbarExtraClass: `!w-full !left-0 !mt-12 lg:!mt-32 !rounded-2 !h-2 !my-0 !bg-neutral-500 !inline-block ${isSwiperLocked ? '[&_.swiper-scrollbar-drag]:hidden' : '' }`
      }"
    >
      <BaseCarouselSlide
        v-for="category in subCategories"
        :key="category.name"
        class="!w-[140px] mr-8 last:mr-0 !h-auto relative"
      >
        <BaseBadge
          v-if="category.promoLabel && extraData?.categoryBadges"
          variant="custom"
          class="absolute top-8 right-8 z-1 rounded-30 custom-color custom-bg-color capitalize"
        >
          {{ category.promoLabel }}
        </BaseBadge>
        <BaseBadge
          v-else-if="category.promoLabel"
          variant="generic"
          class="absolute top-8 right-8 z-1 rounded-30 capitalize"
        >
          {{ category.promoLabel }}
        </BaseBadge>
        <BaseBadge
          v-else-if="category.labelKey"
          variant="attribute"
          class="absolute top-8 right-0 z-1 rounded-30 capitalize"
        >
          <span v-html="$t(category.labelKey)" />
        </BaseBadge>
        <NuxtLink
          :to="`${$addLocaleToPath('plp')}${$t(category.urlPathKey)}${category.query ? '?' + Object.entries(category.query).map(([key, value]) => `${key}=${value}`).join('&') : ''}`"
          class="flex h-full flex-col justify-between items-center
                  ty-link--underline hover:text-offblack-900 text-grey-900"
          :class="{
            'text-offblack-900 active' : category.name === currentCategory.name
          }"
        >
          <BasePicture
            class="transition-transform group-hover:scale-[1.03] w-full mt-16"
            img-classes="w-full min-w-[140px] object-cover aspect-[980/481]"
            type="A"
            v-bind="{
              path: category.imagePath,
              alt: $t(category.nameKey),
              isRetinaUploaded: false
            }"
            disable-lazy
            disable-placeholder
          />
          <div class="flex-1 flex flex-col items-center justify-center text-center p-8">
            <span
              class="semibold-14"
              v-html="$t(category.pluralNameKey || category.nameKey)"
            />
          </div>
        </NuxtLink>
      </BaseCarouselSlide>
    </BaseCarousel>
  </section>
</template>

<script setup lang="ts">
import { intersection } from 'lodash-es';
import type Swiper from 'swiper';
import {
  type NavigationCategory,
  CATEGORIES_MAP,
  findBreadCrumbsByName
} from '~/consts/navigation';

const { $addLocaleToPath } = useNuxtApp();
const { t: $t } = useI18n();
withDefaults(defineProps<{
  isPendingState?: boolean
}>(), {
  isPendingState: false
});
const { extraData, IS_SMOOTH_AVAILABLE } = storeToRefs(useGlobal());
const categoriesMap = shallowRef<NavigationCategory[]>(CATEGORIES_MAP($addLocaleToPath, $t, IS_SMOOTH_AVAILABLE.value));
const route = useRoute();
const { categories: categoriesInPromotion } = storeToRefs(usePromoStore());

const findCategoryByURL = (categories: NavigationCategory[], targetName: string): any | null => {
  for (const category of categories) {
    if (category.children) {
      const nestedResult = findCategoryByURL(category.children, targetName);

      if (nestedResult) {
        return nestedResult;
      }
    }

    if ($t(category.urlPathKey)?.replaceAll('/', '') === targetName) {
      if (category?.query) {
        const result = Object.keys(category.query).some(queryKey =>
          intersection(category.query[queryKey].split(','), route?.query?.[queryKey]?.split(',')).length
        );

        if (result) {
          return category;
        }
      } else {
        return category;
      }
    }
  }

  return null;
};

const currentCategory = computed(() =>
  findCategoryByURL(categoriesMap.value, route.params?.category as string) ||
  categoriesMap.value[0]
);

const categoryBreadCrumbs = computed(() => findBreadCrumbsByName(categoriesMap.value, currentCategory.value.name));

const subCategories = computed(() => {
  const subCategories = categoryBreadCrumbs.value?.[categoryBreadCrumbs.value?.length - 1]?.children ||
      categoryBreadCrumbs.value?.[categoryBreadCrumbs.value?.length - 2]?.children;

  return subCategories?.map(el => ({
    ...el,
    promoLabel: categoriesInPromotion.value?.find(item =>
      item.categoryName === el.trackDataEventLabel ||
      item.categoryName === el.name?.split('___')?.[0])?.value ||
      (el?.promoLabelKey && $t(el?.promoLabelKey))
  }));
});

const isSwiperInitialized = ref(false);
const isSwiperLocked = ref(false);

const onResize = (swiper: Swiper) => {
  const diff = swiper.virtualSize - swiper.width;

  if (diff > 0) {
    isSwiperLocked.value = false;
    swiper.enable();
  } else {
    isSwiperLocked.value = true;
    swiper.disable();
  }
};

const swiperOptions = {
  slidesPerView: 'auto',
  slidesPerGroupAuto: true,
  centeredSlides: false,
  centeredSlidesBounds: true,
  centerInsufficientSlides: true,
  slideToClickedSlide: true,
  shortSwipes: false,
  freeMode: true,
  threshold: 4,
  on: {
    afterInit: (swiper:Swiper) => {
      isSwiperInitialized.value = true;
      nextTick(() => {
        onResize(swiper);
      });
    },
    resize: onResize,
    update: onResize

  }
};

</script>
