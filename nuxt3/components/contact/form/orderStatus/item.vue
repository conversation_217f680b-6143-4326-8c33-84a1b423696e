<template>
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 mt-16 mb-32 lg:mt-32 lg:mb-96">
    <div>
      <ContactFormOrderStatusItemPicture
        class="mb-32 lg:m-0 hidden lg:block"
        v-bind="{
          src: productPreviewImagePath
        }"
      />
    </div>
    <div>
      <ContactFormOrderStatusItemNotification
        v-if="!hasOrderPaidStatus && isFirstOrderItem && !isComplaint"
        v-bind="{
          title:$t('contact.forms.order_status.awaiting_payment_title'),
          description: $t('contact.forms.order_status.awaiting_payment_body_2')
        }"
      />
      <ContactFormOrderStatusItemNotification
        v-if="isDelayedNotificationVisible"
        v-bind="{
          title: $t('contact.forms.order_status.delay_title'),
          description: $t('contact.forms.order_status.delay_body')
        }"
      >
        <ContactFormOrderStatusItemDeliverTime
          class="mt-16"
          splited-lines-mode
          v-bind="{
            isDelayedNotificationVisible,
            hasAssemblyService: hasAssemblyService,
            deliveryDateStart: data.delivery_range_start,
            deliveryDateEnd: data.delivery_range_end,
          }"
        />
      </ContactFormOrderStatusItemNotification>
      <ContactFormOrderStatusItemHeader
        v-bind="{
          title: productName,
          isSampleBox,
          itemId: data.product_id ? data.product_id : data.id,
          color: data.color,
          dimensions: data.dimensions,
          assembly: data.is_assembly,
          quantity: data.quantity
        }"
      />
      <ContactFormOrderStatusItemPicture
        class="mt-16 lg:hidden"
        v-bind="{
          src: productPreviewImagePath
        }"
      />
      <ContactFormOrderStatusItemSelectedDeliveryTimeFrames
        v-if="areDeliveryTimeslotsVisible"
        class="mt-16 lg:mt-32"
        :selected-time-slots="data.delivery_time_frames.selected_dates"
      />
      <ContactFormOrderStatusItemDeliverTime
        v-if="isDeliveryInfoVisible && !areDeliveryTimeslotsVisible"
        class="mt-16 lg:mt-32"
        v-bind="{
          isItemDelivered,
          isDelayedNotificationVisible,
          deliveryDateStart: data.delivery_range_start,
          deliveryDateEnd: data.delivery_range_end,
          deliveryDate: data.delivery_date,
          isDeliveryTimeForSamples: isSampleBox,
          hasAssemblyService: hasAssemblyService,
          preciseDeliveryDate: getPreciseDeliveryDate(),
          shouldDisplayDeliveredDate: shouldDisplayFullDetails,
          isItemShipped,
        }"
      />
      <ContactFormOrderStatusItemProductionRoadMap
        v-if="isDeliveryRoadMapVisible"
        v-bind="{
          currentStatus: data.status,
          assemblyService: data.assembly_service,
          email24: data.email24,
          deliveryTimeFrames: data.delivery_time_frames,
          trackingInfo: data.tracking_info,
          shouldDisplayFullDetails: shouldDisplayFullDetails,
          isSampleBox,
          productStatus: data.product_status,
          isAssemblyServiceMix,
          isAssemblyServiceMixNoAssembly: data.is_assembly_service_mix_no_assembly,
          isDedicatedTransport: data.is_dedicated_transport,
          estimatedProductionDate: data.estimated_production_date,
          asMaxStatus: data.as_max_status,
          asMaxPlanningDate: data.as_max_planning_date,
          isComplaint: data.is_complaint
        }"
      />
      <ContactFormOrderStatusItemFooter
        v-bind="{
          isFAQEnabled: isLastOrderItem,
          isAssemblyEnabled: !(isSampleBox || data.furniture_category === 'cover')
        }"
        v-on="{
          onAssemblyClicked,
          onFAQClicked
        }"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import useOrderStatusItem from '~/composables/contact/useOrderStatusItem';

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  isLastOrderItem: {
    type: Boolean,
    required: true
  },
  isFirstOrderItem: {
    type: Boolean,
    required: true
  },
  isOrderPaid: {
    type: Boolean,
    required: true
  },
  isComplaint: {
    type: Boolean,
    required: true
  },
  isAssemblyServiceMix: {
    type: Boolean,
    required: true
  }
});

const {
  getPreciseDeliveryDate,
  hasOrderPaidStatus,
  isSampleBox,
  isDelayedNotificationVisible,
  hasAssemblyService,
  areDeliveryTimeslotsVisible,
  isDeliveryInfoVisible,
  isDeliveryRoadMapVisible,
  isItemShipped,
  isItemDelivered,
  shouldDisplayFullDetails,
  productName,
  productPreviewImagePath
} = useOrderStatusItem(props as any);

const emit = defineEmits([
  'toggleFaqModal',
  'toggleAssemblyModal'
]);

const onAssemblyClicked = () => {
  emit('toggleAssemblyModal', props.data);
};

const onFAQClicked = () => {
  emit('toggleFaqModal');
};
</script>
