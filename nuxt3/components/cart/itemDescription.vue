<template>
  <ul>
    <li v-if="nameVisible">
      {{ getCartItemName(cartItem) }}
    </li>
    <li v-if="isSampleBox && cartItem.shelf_type === 3">
      {{
        cartItem.is_exterior ?
          $t('lp.samples.sets.tone-wardrobes.exterior') :
          $t('lp.samples.sets.tone-wardrobes.interior')
      }}
    </li>
    <li class="flex gap-4">
      {{ $t('scart.item.label.color') }}
      <span class=" text-pretty">
        {{ colorName }}
      </span>
    </li>
    <li v-if="!isSampleBox">
      {{ $t('scart.item.label.size') }}: {{ $t('scart.item_dimensions', { height: cartItem.height,
                                                                          width: cartItem.width,
                                                                          depth: cartItem.depth }) }}
    </li>
    <li v-if="!isSampleBox && quantityVisible">
      {{ $t('scart.item.label.quantity') }}: {{ cartItem.quantity }}
    </li>
    <li>
      <slot />
    </li>
  </ul>
</template>

<script setup lang="ts">
import type { CartItem } from '~/types/userStatus';
import useSofaCartInfo from '~/composables/useSofaCartInfo';

const props = withDefaults(defineProps<
    {cartItem: CartItem, quantityVisible?: boolean, nameVisible?:boolean}>(),
{ quantityVisible: false, nameVisible: false });

let colorName = '';

if (props.cartItem.content_type === 'sotty') {
  const {
    sofaColorName
  } = useSofaCartInfo(props.cartItem.material, props.cartItem.itemId);
  colorName = sofaColorName;
} else {
  colorName = computed(() => getCartItemColorName(props.cartItem));
}

const { getCartItemName, getCartItemColorName, isItSampleBox } = useCart();
const isSampleBox = computed(() => isItSampleBox(props.cartItem));

</script>
