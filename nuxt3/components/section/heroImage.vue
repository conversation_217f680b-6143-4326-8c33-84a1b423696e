<template>
  <section
    class="relative w-full border-box min-h-[475px] max-h-[calc(100vh-132px)]"
    v-bind="{ style: { backgroundColor } }"
  >
    <BasePicture
      disable-lazy
      img-classes="block min-h-[475px] h-full max-w-full w-full max-h-[calc(100vh-132px)] object-cover object-bottom"
      class="after:block after:absolute after:w-full after:h-3/4 after:top-0"
      v-bind="{
        path: imagePath,
        alt: imageAlt,
        type: imageType,
        class: { 'after:bg-gradient-to-b after:from-black/50 after:via-black/20' : gradient }
      }"
    />
    <div
      v-if="headingCopy"
      class="container-cstm-fluid !box-border absolute w-full md:h-auto left-0
              pt-[--navigation-modern-padding]
              md:pt-[calc(var(--navigation-modern-padding)/4)]
              lg:pt-[calc(var(--navigation-modern-padding)/2)]"

      :class="[
        {
          'top-0 md:top-1/4 transform md:translate-y-[-20%]': variant === 'left',
          'bottom-0 pb-32 md:pb-64': variant === 'center'
        },
        containerAdditionalClasses
      ]"
    >
      <div
        class="grid grid-cols-12"
        :class="{
          'text-center': variant === 'center'
        }"
      >
        <div
          class="col-span-12"
          :class="{
            'md:col-span-8 lg:col-span-6': variant === 'left',
            'md:col-start-3 md:col-span-8 lg:col-start-4 lg:col-span-6': variant === 'center'
          }"
        >
          <slot name="subheadingSlot" />
          <h2
            v-if="subheadingCopy"
            class="normal-16 "
            :class="[
              headerColorClass,
              additionalClassSubheading,
              {
                'md:normal-24 mb-8 mt-24 md:mt-0 xl:mb-16 sm:normal-20': variant === 'left',
                'md:normal-18 mb-16 md:mb-24 uppercase': variant === 'center'
              }
            ]"
            v-html="subheadingCopy"
          />
          <h1
            :class="[
              headerColorClass,,
              additionalClassHeading,
              subheadingCopy ? 'text-left' : 'text-center',
              {
                'md:bold-54 lg:bold-54 xl:bold-54 pb-24 md:pb-0 pt-24 md:pt-0 md:text-left': variant === 'left',
                '!text-center mb-16 md:bold-74': variant === 'center'
              },
            ]"
            class="bold-32 "
            v-html="headingCopy"
          />
          <p
            v-if="additionalText.length"
            class="text-white normal-16 mt-16 md:mt-24 md:normal-18"
            :class="additionalTextClass"
          >
            {{ additionalText }}
          </p>
          <BaseLink
            v-if="ctaCopy && ctaUrl"
            :variant="ctaVariant"
            v-bind="{
              href: ctaUrl,
              trackData: {
                eventLabel: 'cta',
                eventPath: ctaUrl
              }
            }"
            class="lg:ty-btn--l"
            :class="[
              {
                'mt-16 xl:mt-32 md-max:hidden': variant === 'left',
                'mt-24 md:mt-48': variant === 'center'
              },
              ctaAdditionalClasses
            ]"
          >
            {{ ctaCopy }}
          </BaseLink>
          <BaseButton
            v-else-if="ctaCopy && ctaCallback"
            :variant="ctaVariant"
            v-bind="{
              trackData: {
                eventLabel: 'cta',
              }
            }"
            class="lg:ty-btn--l"
            :class="[
              {
                'mt-16 xl:mt-32 md-max:hidden': variant === 'left',
                'mt-24 md:mt-48': variant === 'center'
              },
              ctaAdditionalClasses
            ]"
            v-on:click="$emit('ctaAction')"
          >
            {{ ctaCopy }}
          </BaseButton>
        </div>
      </div>
      <BaseLink
        v-if="ctaCopy && ctaUrl && ctaMode === 'link'"
        :variant="ctaVariant"
        v-bind="{
          href: ctaUrl,
          trackData: {
            eventLabel: 'cta',
            eventPath: ctaUrl
          }
        }"
        class="md:hidden  lg:ty-btn--l"
        :class="{
          'md-max:hidden': variant === 'center'
        }"
      >
        {{ ctaCopy }}
      </BaseLink>
      <BaseButton
        v-else-if="ctaCopy && ctaCallback"
        :variant="ctaVariant"
        v-bind="{
          trackData: {
            eventLabel: 'cta',
          }
        }"
        class="md:hidden  lg:ty-btn--l mt-24"
        :class="{
          'md-max:hidden': variant === 'center'
        }"
        v-on:click="$emit('ctaAction')"
      >
        {{ ctaCopy }}
      </BaseButton>
      <slot name="ctaSlot" />
    </div>
    <slot name="notice" />
  </section>
</template>

<script setup lang="ts">

defineEmits(['ctaAction']);

defineProps({
  ctaMode: {
    type: String,
    default: 'link'
  },
  ctaAdditionalClasses: {
    type: String,
    default: ''
  },
  containerAdditionalClasses: {
    type: String,
    default: ''
  },
  ctaVariant: {
    type: String,
    default: 'accent'
  },
  imagePath: {
    type: String,
    required: true
  },
  imageAlt: {
    type: String,
    required: true
  },
  imageType: {
    type: String,
    required: true
  },
  headingCopy: {
    type: String,
    default: ''
  },
  subheadingCopy: {
    type: String,
    default: ''
  },
  headerColorClass: {
    type: String,
    default: 'text-offblack-600'
  },
  additionalClassHeading: {
    type: String,
    default: ''
  },
  additionalClassSubheading: {
    type: String,
    default: ''
  },
  ctaCopy: {
    type: String,
    default: ''
  },
  ctaUrl: {
    type: String,
    default: ''
  },
  ctaCallback: {
    type: Boolean,
    default: null
  },
  variant: {
    type: String,
    default: 'left'
  },
  additionalText: {
    type: String,
    default: ''
  },
  additionalTextClass: {
    type: String,
    default: ''
  },
  // Average image color
  backgroundColor: {
    type: String,
    default: '#fff'
  },
  gradient: {
    type: Boolean,
    default: true
  }
});
</script>
