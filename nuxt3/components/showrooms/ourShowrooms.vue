<template>
  <div class="py-48 md:py-64 xl:py-96 bg-beige-100">
    <UiHeadline
      class="mb-32 lg:mb-48"
      v-bind="{
        label: $t('showrooms_our_subheading'),
        title: $t('showrooms_our_heading'),
        isDarkMode: false,
        subtitleClasses: 'md-max:!mb-8'
      }"
    />
    <div class="lg:grid-container">
      <ToggleScroll
        v-model="activeTab"
        scroll-wrapper-classes="h-48 md-max:w-full"
        toggle-button-classes="ml-8 first:ml-0 flex items-center px-16 normal-16 border-[#B2B6B8]"
        toggle-wrapper-classes="relative inline-flex items-stretch justify-center flex-row whitespace-nowrap capitalize h-48 rounded-20 md-max:px-16"
        toggle-active-button-classes="active border-neutral-900"
        overlay-classes="hidden"
        v-bind="{
          variant: 'outlined',
          options: options.map((item, index) => ({ label: item, value: index })),
        }"
      />
    </div>
    <div class="grid-container">
      <div class="grid grid-cols-12 lg-max:gap-y-24 lg:gap-x-16 mt-32 lg:mt-48">
        <template v-if="activeTab === 0">
          <ShowroomsOurShowroomsItem
            v-bind="{
              imgSrc: 'showrooms/partner',
              heading: $t('showrooms_our_heading_tab2')
            }"
          >
            <p
              class="normal-16 text-neutral-900 mt-16"
              v-html="$t('showrooms_our_tab2_content1')"
            />
            <p
              class="normal-16 text-neutral-900 mt-8"
              v-html="$t('showrooms_our_tab2_content2')"
            />
          </ShowroomsOurShowroomsItem>
        </template>
        <template v-if="activeTab === 1">
          <ShowroomsOurShowroomsItem
            v-bind="{
              imgSrc: 'showrooms/friends',
              heading: $t('showrooms_our_heading_tab3')
            }"
          >
            <p
              class="normal-16 text-neutral-900 mt-16"
              v-html="$t('showrooms_our_tab3_content1')"
            />
            <p
              class="normal-16 text-neutral-900 mt-8"
              v-html="$t('showrooms_our_tab3_content2')"
            />
            <h3
              class="semibold-16 mt-32"
              v-html="$t('showrooms_our_heading2_tab3')"
            />
            <p
              class="normal-16 text-neutral-900 mt-8"
              v-html="$t('showrooms_our_tab3_content3')"
            />
            <BaseLink
              variant="outlined"
              class="mt-16"
              v-bind="{
                href: $addLocaleToPath('contact'),
                trackData: { eventLabel: 'cart-empty-shop' },
                'data-testid': 'cart-empty-shop'
              }"
            >
              {{ $t('contact.heading') }}
            </BaseLink>
          </ShowroomsOurShowroomsItem>
        </template>
        <template v-if="activeTab === 2">
          <ShowroomsOurShowroomsItem
            v-bind="{
              imgSrc: 'showrooms/flagship',
              heading: $t('showrooms_our_heading_tab1')
            }"
          >
            <p
              class="normal-16 text-neutral-900 mt-16"
              v-html="$t('showrooms_our_tab1_content1')"
            />
            <p
              class="normal-16 text-neutral-900 mt-8"
              v-html="$t('showrooms_our_tab1_content2')"
            />
            <p
              class="normal-16 text-neutral-900 mt-8"
              v-html="$t('showrooms_our_tab1_content3')"
            />
          </ShowroomsOurShowroomsItem>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const { $i18n } = useNuxtApp();
const activeTab = ref(0);
const { $addLocaleToPath } = useNuxtApp();
const options = [
  $i18n.t('showrooms_our_tab2'),
  $i18n.t('showrooms_our_tab3'),
  $i18n.t('showrooms_our_tab1')
];
</script>
