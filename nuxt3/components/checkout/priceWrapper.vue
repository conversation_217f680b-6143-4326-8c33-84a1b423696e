<template>
  <Fragment>
    <div v-if="promotion">
      <span
        v-bind:class="promotionClasses"
        class="text-orange px-4 mr-4 border border-orange border-solid rounded-8"
        data-testid="wishlist-promo-percent-value"
        v-html="`-${promotion}%`"
      />
      <span
        v-bind:class="lineThroughClasses"
        class="line-through"
        data-testid="wishlist-price-without-discount"
        v-html="priceWithoutDiscount"
      />
      <strong
        v-bind:class="priceClasses"
        data-testid="wishlist-price"
        v-html="priceRegionalizedNumber"
      />
    </div>
    <strong
      v-else
      v-bind:class="priceClasses"
      data-testid="wishlist-price"
      v-html="priceRegionalizedNumber"
    />
  </Fragment>
</template>

<script setup lang="ts">
import usePrice from '~/composables/usePrice';
const global = useGlobal();
const { format } = usePrice();

const props = defineProps({
  promotionClasses: {
    type: String,
    default: 'normal-10'
  },
  lineThroughClasses: {
    type: String,
    default: 'normal-12 text-grey-900 mr-4'
  },
  priceClasses: {
    type: String,
    default: 'normal-24 text-offblack-800'
  },
  promotion: {
    type: Number,
    required: true
  },
  itemPriceWithoutDiscountRegionalizedNumber: {
    type: Number,
    required: true
  },
  itemPriceRegionalizedNumber: {
    type: Number,
    required: true
  }
});

const priceWithoutDiscount = computed(() => format(
  props.itemPriceWithoutDiscountRegionalizedNumber,
  global.currencyCode,
  global.countryLocale
));

const priceRegionalizedNumber = computed(() => format(
  props.itemPriceRegionalizedNumber,
  global.currencyCode,
  global.countryLocale
));

</script>
