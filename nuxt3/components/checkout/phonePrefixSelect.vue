<template>
  <div>
    <Listbox
      v-model="selectedCountry"
      class="relative tylko-phone-prefix"
      as="div"
    >
      <ListboxButton
        class="
        relative
        text-left
        pl-16
        pr-[47px]
        py-8
        block
        bg-white
        border
        border-grey-800
        rounded-6
        normal-14
        text-offblack-800
        basic-transition
        transition-border-color
        hover:border-grey-900"
      >
        <span class="block mb-[9px]">{{ label }}</span>
        <span class="flex pb-2">
          <span class="normal-16 !leading-1 mr-[5px]">{{ selectedCountry.flag }}</span>
          <span class="!leading-1">+{{ selectedCountry.countryCallingCode }}</span>
        </span>
        <IconChevronDown class="bottom-8 right-16 absolute phone-prefix-icon" />
      </ListboxButton>
      <ListboxOptions
        class="
      bg-white
      absolute
      overflow-y-scroll
      h-[300px]
      md:w-[300px]
      w-[calc(100vw-32px)]
      max-w-[388px]
      top-[63px]
      left-0
      z-10
      border
      rounded-b-6
      focus-visible:shadow-none
      border-grey-900"
        as="ul"
      >
        <!-- Use the `active` state to conditionally style the active option. -->
        <!-- Use the `selected` state to conditionally style the selected option. -->
        <ListboxOption
          v-for="(item, index) in sortedData"
          v-bind:key="index"
          v-slot="{ active, selected }"
          v-bind:value="item"
        >
          <li
            class="px-16 py-12 flex justify-between cursor-pointer"
            v-bind:class="{
              'bg-offwhite-700 text-black': active,
              'bg-white text-black': !active,
            }"
          >
            <span class="pr-8">
              <span class="mr-8">{{ item.flag }}</span>
              <span>{{ item.countryNameEn }} ({{ item.countryNameLocal }})</span>
            </span>
            <span>+{{ item.countryCallingCode }}</span>
          </li>
        </ListboxOption>
      </ListboxOptions>
    </Listbox>
  </div>
</template>

<script setup lang="ts">
import {
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption
} from '@headlessui/vue';

import * as countryCodes from 'country-codes-list';
import { regions } from '~/consts/regions';

const props = defineProps({
  modelValue: {
    type: String,
    required: true
  },
  label: {
    type: String,
    required: true
  }
});
const global = useGlobal();

const sortObjectsByCountryCode = (objects, order) => {
  return objects.sort((a, b) => {
    const indexA = order.indexOf(a.countryCode); // Get the index of a in the order array
    const indexB = order.indexOf(b.countryCode); // Get the index of b in the order array

    // If both elements are in the order array, sort them by their index in the order array
    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }

    // If both elements are not in the order array, keep their current order
    if (indexA === -1 && indexB === -1) {
      return 0;
    }

    // If element a is not in the order array, move it to the end
    if (indexA === -1) {
      return 1; // a is not in the order, so b comes first
    }

    // If element b is not in the order array, move it to the end
    return -1; // b is not in the order, so a comes first
  });
};

const gbHack = value => value === 'UK' ? 'GB' : value;

const sortedData = sortObjectsByCountryCode(countryCodes.all(), Object.values(regions).map(value => gbHack(value.iso2)));
const selectedCountry = ref();

watch(() => selectedCountry.value, (value) => {
  emit('update:modelValue', value.countryCode);
});

watch(() => global.regionCode, (value) => {
  selectedCountry.value = countryCodes.findOne('countryCode', gbHack(value.toUpperCase()));
}, { immediate: true });

const emit = defineEmits<{
  'update:model-value': [value: boolean]
}>();

</script>

<style lang="scss">
 .tylko-phone-prefix {
     ul, button {
         &:focus-visible {
             box-shadow: none;
         }
     }
     &[data-headlessui-state='open']{
            .phone-prefix-icon {
                @apply rotate-180
            }
            button {
             @apply rounded-b-none;
            }
     }
 }
</style>
