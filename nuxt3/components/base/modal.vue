<template>
  <section>
    <TransitionRoot
      :show="modelValue"
      as="template"
      enter="duration-300 ease-out"
      enter-from="opacity-0"
      enter-to="opacity-100"
      leave="duration-200 ease-in"
      leave-from="opacity-100"
      leave-to="opacity-0"
      class="z-9 relative"
      v-on:after-leave="$emit('afterClose')"
    >
      <Dialog
        v-bind="{
          class: {
            '!pointer-events-none' : !modelValue
          }
        }"
        :static="static"
        v-on:close="static ? () => {} : (defaultClose && $emit('update:model-value', false))"
      >
        <TransitionChild
          enter="duration-200 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div
            class="inset-0 fixed flex justify-end bg-black bg-opacity-40 overscroll-contain"
            aria-hidden="true"
          />
        </TransitionChild>

        <TransitionChild
          class="fixed inset-0"
          :class="wrapperClass"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <slot v-bind="{ close: () => $emit('update:model-value', false) }" />
        </TransitionChild>
      </Dialog>
    </TransitionRoot>
  </section>
</template>

<script setup lang="ts">
import { Dialog, TransitionRoot, TransitionChild } from '@headlessui/vue';

defineProps({
  defaultClose: {
    type: Boolean,
    default: true
  },
  modelValue: {
    type: Boolean,
    default: false
  },
  static: {
    type: Boolean,
    default: false
  },
  wrapperClass: {
    type: String,
    default: ''
  }
});

defineEmits<{
  'update:model-value': [value: false],
  'afterClose': []
}>();

</script>
