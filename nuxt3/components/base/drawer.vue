<template>
  <TransitionRoot
    :show="modelValue"
    as="template"
  >
    <Dialog
      class="relative z-8"
      v-on:close="modelValue = false"
    >
      <TransitionChild
        v-bind="{
          enter,
          leave
        }"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div
          :style="additionalOverlayStyle"
          :class="additionalOverlayClasses"
          class="inset-0 fixed flex justify-end bg-black bg-opacity-40 overscroll-contain"
          aria-hidden="true"
        />
      </TransitionChild>
      <TransitionChild
        class="fixed inset-0 overflow-y-auto overflow-x-hidden"
        v-bind="{
          enter,
          leave,
          'enter-from': enterFrom,
          'enter-to': enterTo,
          'leave-from': leaveFrom,
          'leave-to': leaveTo
        }"
      >
        <div
          v-if="modernDrawer"
          class="flex min-h-full flex-col justify-end"
          v-on:touchstart="handleTouchStart"
          v-on:touchmove="handleTouchMove"
          v-on:touchend="handleTouchEnd"
        >
          <DialogPanel
            :class="additionalDialogPanelClasses"
            class="bg-white w-full pt-16 pb-48 px-16 md-max:mt-48 md-max:rounded-t-24 md-max:min-h-[160px] md:w-[600px] md:absolute md:right-0 md:top-0 md:p-48 md:min-h-full"
          >
            <div
              class="drawerHeading md-max:pb-8"
              :class="additionalDrawerHeadingClasses"
              v-on:touchstart="handleTouchStart"
              v-on:touchmove="handleTouchMove"
              v-on:touchend="handleTouchEnd"
            >
              <BaseButton
                class="block z-2 ml-auto sticky top-16 right-16 md:fixed md:top-24 md:right-24"
                :class="[
                  hideCloseButton ? 'hidden' : 'visible',
                  customCloseButtonClass
                ]"
                data-testid="drawer-close-button"
                v-bind="{
                  variant: 'close',
                  trackData: {}
                }"
                v-on:click="modelValue = false"
              >
                <IconPlus class="rotate-45" />
              </BaseButton>
            </div>
            <div
              ref="slotContainer"
              :class="slotContainerClass"
            >
              <slot />
            </div>
          </DialogPanel>
        </div>

        <DialogPanel
          v-else
          :style="additionalDialogPanelStyle"
          :class="additionalDialogPanelClasses"
          class="fixed top-0 bottom-0 right-0 h-full max-w-full md:max-w-[600px] w-full bg-white overscroll-contain overflow-auto"
        >
          <button
            class="absolute z-2 top-8 right-8 w-32 h-32 flex justify-center items-center rounded-full"
            :class="hideCloseButton ? 'hidden' : 'visible'"
            data-testid="drawer-close-button"
            v-on:click="modelValue = false"
          >
            <IconPlus class="rotate-45" />
          </button>
          <slot />
        </DialogPanel>
      </TransitionChild>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { Dialog, DialogPanel, TransitionRoot, TransitionChild } from '@headlessui/vue';

defineProps({
  additionalOverlayStyle: {
    type: Object,
    default: null
  },
  additionalOverlayClasses: {
    type: String,
    default: ''
  },
  additionalDialogPanelClasses: {
    type: String,
    default: ''
  },
  additionalDrawerHeadingClasses: {
    type: String,
    default: ''
  },
  additionalDialogPanelStyle: {
    type: Object,
    default: null
  },
  hideCloseButton: {
    type: Boolean,
    default: false
  },
  enter: {
    type: String,
    default: 'duration-300 cubic-bezier(0, 0, 0.58, 1)'
  },
  leave: {
    type: String,
    default: 'duration-200 cubic-bezier(0.25, 0.1, 0.25, 1)'
  },
  enterFrom: {
    type: String,
    default: 'md-max:translate-y-[100%] md:translate-x-[100%]'
  },
  enterTo: {
    type: String,
    default: 'md-max:translate-y-0 md:translate-x-0'
  },
  leaveFrom: {
    type: String,
    default: 'md-max:translate-y-0 md:translate-x-0'
  },
  leaveTo: {
    type: String,
    default: 'md-max:translate-y-[100%] md:translate-x-[100%]'
  },
  modernDrawer: {
    type: Boolean,
    default: false
  },
  customCloseButtonClass: {
    type: String,
    default: ''
  },
  slotContainerClass: {
    type: String,
    default: ''
  }
});

const { $dixa } = useNuxtApp();

const modelValue = defineModel<boolean>();

const touchStartY = ref(0);
const touchEndY = ref(0);
const isSlotContentOverflowing = ref(false);
const slotContainer = ref<HTMLElement | null>(null);

const handleTouchStart = (event: TouchEvent) => {
  touchStartY.value = event.touches[0].clientY;
};

const handleTouchMove = (event: TouchEvent) => {
  touchEndY.value = event.touches[0].clientY;
};

const handleTouchEnd = (event: TouchEvent) => {
  const target = event.target as HTMLElement;
  const heading = target?.classList?.contains('drawerHeading');

  if (isSlotContentOverflowing.value && !heading) {
    return;
  }

  if (touchEndY.value - touchStartY.value > 50) {
    modelValue.value = false;
  }
};

const checkSlotContentHeight = () => {
  if (slotContainer.value) {
    const slotHeight = slotContainer.value.scrollHeight + 48 + 64 + 56; // padding and margins
    const viewportHeight = window.innerHeight;

    isSlotContentOverflowing.value = slotHeight >= viewportHeight;
  }
};

watch(modelValue, (newValue) => {
  if (newValue) {
    nextTick(() => {
      checkSlotContentHeight();
      $dixa.setWidgetVisibility(false);
    });
  } else {
    $dixa.setWidgetVisibility(true);
  }
});

onMounted(() => {
  window.addEventListener('resize', checkSlotContentHeight);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkSlotContentHeight);
});
</script>
