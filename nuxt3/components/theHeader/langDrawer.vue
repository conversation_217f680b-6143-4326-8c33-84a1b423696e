<template>
  <div class="lg:grid-container lg:mx-auto relative">
    <TheHeaderMegaMenuTab
      class="lg:hidden !mb-0"
      tab-name="regions"
      v-bind="{
        title: $t('menu.labels.regions')
      }"
      v-on:on-back="$emit('onClose')"
    />
    <TabGroup
      v-slot="{ selectedIndex }"
      as="div"
    >
      <TabList class="bg-white flex space-x-1 rounded-xl pb-2 pt-8 lg:hidden z-1 top-[56px] sticky">
        <Tab
          v-for="(tabNameKey, index) in ['menu.labels.regions', availableLanguagesList.length > 1 && 'menu.labels.language'].filter(Boolean)"
          :key="index"
          class="relative pb-8 mr-32 last:mr-0
                   bold-14 text-grey-900 hover:text-offblack-900 uppercase
                   transition-colors duration-300 ease-in-out
                   after:absolute after:left-0 after:bottom-0 after:w-[70%] after:h-[1px] after:bg-offblack-900
                   after:transform after:scale-x-0 after:origin-left
                   after:transition-transform after:duration-300 after:ease-in-out
                   ui-selected:text-offblack-900 ui-selected:after:!scale-x-100"
          data-testid="toggle-button"
        >
          {{ $t(tabNameKey) }}
        </Tab>
      </TabList>
      <div
        class="grid grid-cols-12 py-16 border-grey-700 lg:border-b"
        :class="selectedIndex === 0 ? 'lg-max:block' : 'lg-max:hidden'"
      >
        <div class="lg-max:hidden col-span-2 pr-16 normal-14 text-grey-900">
          <h4 v-html="$t('menu.region.header1')" />
          <p v-html="$t('menu.region.text1')" />
        </div>
        <ul class="flex flex-col lg:grid col-span-10 grid-cols-4 xl:grid-cols-6">
          <li
            v-for="region in regions"
            :key="region.name"
            :class="region.name === regionName ? 'lg-max:text-orange lg-max:order-first': 'text-offblack-700'"
          >
            <BaseButton
              variant="custom"
              class="flex items-center py-4 lg:py-[6px]
                          normal-16 text-capitalize hover:text-orange
                          transition-color basic-transition"

              v-bind="{
                'data-testid': `region-${region.name}`,
                trackData: {
                  eventCategory:'regions_menu',
                  eventAction: region.name,
                  eventLabel:'opened'
                },
              }"
              v-on:click="onRegionChange(region.name)"
            >
              <UiCountryFlag v-bind="{ regionName: region.name, regionTranslatedName: region.translatedName }" />
              <span
                class="-ml-12 leading-1"
                v-html="region.translatedName"
              />
            </BaseButton>
          </li>
        </ul>
      </div>
      <div
        v-if="availableLanguagesList.length > 1 "
        :class="selectedIndex === 1 ? 'lg-max:block' : 'lg-max:hidden'"
        class="py-16 lg:grid lg:grid-cols-12"
      >
        <div class="hidden lg:block col-span-2 pr-16 normal-14 text-grey-900">
          <h4
            class="lg:py-16"
            v-html="$t('menu.region.header2')"
          />
        </div>
        <ol class="col-span-10 lg:grid lg:grid-cols-12">
          <template
            v-for="language in availableLanguagesList"
            :key="language.code"
          >
            <li
              class="col-span-3 xl:col-span-2 group"
            >
              <BaseLink
                variant="custom"
                class="block py-12 lg:py-16 group hover:text-orange"
                :class="language.code === $i18n.locale.value ? 'text-orange' : 'text-offblack-700'"
                v-bind="{
                  'data-testid': `redirect-${switchLocalePath(language.code)}`,
                  trackData: {
                    eventCategory:'language_menu',
                    eventAction: language.name,
                    eventLabel:'opened'
                  },
                  href: switchLocalePath(language.code) || `/${language.code}`,
                  preventDefault: true
                }"
                v-on:click="onChangeLanguage(language.langCode)"
              >
                <span
                  class="inline-block px-2 pt-2 normal-12 rounded-4 uppercase mr-8 border group-hover:border-orange min-w-28 text-center"
                  :class="{ 'border-orange': language.code === $i18n.locale.value }"
                  v-html="language.code.split('-')[0]"
                />
                {{ language.name }}
              </BaseLink>
            </li>
          </template>
        </ol>
      </div>
    </TabGroup>
    <LazyModalChangeRegionT03
      v-if="isChangeRegionConfirmationModalOpened"
      v-model="isChangeRegionConfirmationModalOpened"
      :hydrate-when="isChangeRegionConfirmationModalOpened"
      v-bind="{
        regionName: selectedRegion
      }"
    >
      <template #description>
        <template v-if="ischangeRegionT03ModalOpened">
          {{ $t('menu.t03_popup.description') }}
        </template>
        <template v-if="ischangeRegionS01ModalOpened">
          {{ $t('menu.s01_popup.description') }}
        </template>
        <template v-if="ischangeRegionCorduroyModalOpened">
          {{ $t('menu.corduroy_popup.description') }}
        </template>
      </template>
    </LazyModalChangeRegionT03>
  </div>
</template>

<script setup lang="ts">
import { TabGroup, TabList, Tab } from '@headlessui/vue';
import { changeLanguage } from '~/api/account';

const switchLocalePath = useSwitchLocalePath();
const { $logException, $i18n } = useNuxtApp();
const { regionName } = storeToRefs(useGlobal());
const { getAvailableLanguages, createLangCode } = useLocale();

const { onRegionChange, sortedTranslatedRegionsWithOtherRegion: regions } = useRegions();
const availableLanguagesList = ref(getAvailableLanguages());
const { ischangeRegionT03ModalOpened, selectedRegion, ischangeRegionS01ModalOpened, ischangeRegionCorduroyModalOpened } = storeToRefs(useModalStore());
const isChangeRegionConfirmationModalOpened = computed({
  get () {
    return ischangeRegionT03ModalOpened.value || ischangeRegionS01ModalOpened.value || ischangeRegionCorduroyModalOpened.value;
  },
  set () {
    ischangeRegionT03ModalOpened.value = false;
    ischangeRegionS01ModalOpened.value = false;
    ischangeRegionCorduroyModalOpened.value = false;
  }
});

const onChangeLanguage = async (lang: Language) => {
  const { error } = await changeLanguage(lang);

  if (error.value) {
    $logException(error.value);
  } else {
    const langCode = createLangCode(lang);
    window.location.href = switchLocalePath(langCode) || `/${langCode}`;
  }
};

defineEmits(['onClose']);
</script>
