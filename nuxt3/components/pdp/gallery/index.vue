<template>
  <section class="relative pb-24 lg:pb-32 bg-beige-100">
    <div class="lg:grid-container">
      <div
        class="py-24 lg:py-12 md-max:px-0 lg:max-w-[40%] xl:max-w-1/2 lg:ml-auto"
        :class="[
          isABTest ? 'relative z-1 lg:z-[36]' : 'lg:sticky transition-top basic-transition z-2 lg:py-8',
          !isABTest ? isNavBarHidden ? 'top-[calc(var(--ribbon-height))]' : 'top-[calc(var(--header-with-ribbon-height))]' : 'top-0'
        ]"
      >
        <PdpGallerySwatches
          v-model="currentColor"
          class="lg:max-w-max lg:ml-auto"
          :colors="colorsToShow"
        />
      </div>

      <PdpGalleryMobile
        v-if="isSm"
        v-bind="{
          items,
          colorLabelKey: currentColor.nameKey
        }"
        class="lg:hidden"
        v-on:image-click="(activeSlideIndex) => toggleModal(activeSlideIndex)"
      />

      <PdpMosaic
        v-else
        class="mt-32"
        data-pdp-gallery-images-container
        v-bind="{
          items,
          eventAction: 'Gallery',
        }"
        v-on:cta-click="({ id }) => toggleModal(id - 1)"
      />

      <p class="mt-16 lg:mt-24 lg-max:grid-container">
        <BaseLink
          class="border-b border-solid semibold-16 text-neutral-900"
          target="_blank"
          v-bind="{
            href: currentColor.materialId === 15 ? $addLocaleToPath('lp.colab-andy') : $addLocaleToPath('plp'),
            variant: 'custom',
            trackData: {
              eventType: 'NOEEC',
              eventCategory: 'pdp_clicks',
              eventAction: 'Gallery',
              eventLabel: 'See more styles'
            }
          }"
        >
          {{ currentColor.materialId === 15 ? $t('pdp.achor_nav.pink.cta') : $t('pdp.anchor_nav.cta') }}
        </BaseLink>
      </p>
    </div>

    <ModalFullScreen
      v-model="isModalOpen"
      overlay-classes="pdp-section-gallery__zoom-carousel bg-beige-100 z-5"
    >
      <div class="h-full portrait:pb-[52px] flex items-center">
        <BaseCarousel
          ref="refCarouselZoom"
          class="portrait:max-h-full landscape:h-full w-full"
          v-bind="{
            options: {
              slidesPerView: 'auto',
              centeredSlides: true,
              initialSlide: activeZoomSlide,
              spaceBetween: 8,
              breakpoints: {
                [breakpoints.md.min]: {
                  spaceBetween: 16,
                },
                [breakpoints.lg.min]: {
                  spaceBetween: 32,
                },
              }
            },
            swiperRootClasses: 'aspect-square lg:aspect-[4/3]',
            isPaginationDesktop: true,
            isPaginationMobile: true,
            name: 'pdpGalleryCarouselZoom',
            isNavigation: true
          }"
        >
          <BaseCarouselSlide
            v-for="(item) in items"
            :key="item.id"
          >
            <BasePicture
              picture-classes="block"
              img-classes="w-full object-cover"
              type="M D"
              v-bind="{
                path: item.image.path,
                alt: item.image.alt,
                isRetinaUploaded: false
              }"
            />
          </BaseCarouselSlide>
        </BaseCarousel>
      </div>
    </ModalFullScreen>
  </section>
</template>

<script lang=ts setup>
import useHeader from '~/composables/useHeader';
import useGallery from '~/composables/pdp/useGallery';
import useMq, { breakpoints } from '~/composables/useMq';

import { FURNITURE_TYPES_KEYS } from '~/consts/types';

const { categories } = useCategories();
const { IS_DESKTOP } = storeToRefs(useGlobal());

const props = defineProps({
  category: {
    type: String,
    required: true
  },
  furnitureType: {
    type: String as PropType<FURNITURE_TYPES_KEYS>,
    required: true
  },
  shelfType: {
    type: Number as PropType<SHELF_TYPE>,
    required: true
  },
  configuratorType: {
    type: Number,
    required: true
  },
  materialValue: {
    type: Number,
    required: true
  },
  alt: {
    type: String,
    default: ''
  },
  depth: {
    type: Number,
    default: null
  },
  isABTest: {
    type: Boolean,
    default: false
  }
});

const $gtm = useGtm();

const { isNavBarHidden } = useHeader();
const { isSm } = useMq(IS_DESKTOP ? 'lg' : 'sm');
const { getColorSwatchesForProductCategory } = useColors();
const { galleryData } = useGallery();

const findColor = () => {
  return colorsToShow.value.findIndex(color => color.cv === props.materialValue && color.shelfType === props.shelfType);
};

const category = computed(() => categories[props.category]);
const colorsToShow = computed(() => getColorSwatchesForProductCategory(props.furnitureType, category.value));
const configuratorColorIndex = computed(() => findColor());
const galleryColorIndex = ref(configuratorColorIndex.value);
const currentColor = ref(colorsToShow.value[galleryColorIndex.value]);
const activeZoomSlide = ref<number | null>(null);

const isModalOpen = computed({
  get: () => activeZoomSlide.value !== null,
  set: (newValue) => {
    if (!newValue) {
      activeZoomSlide.value = null;
    }
  }
});

const items = computed(() => galleryData(currentColor.value.name, props.category, props.alt, props.depth, props.furnitureType, props.configuratorType));

const toggleModal = (activeSlideIndex: number) => {
  $gtm && $gtm.push({
    event: 'userInteraction',
    eventType: 'NOEEC',
    eventCategory: 'pdp_clicks',
    eventAction: 'Gallery',
    eventLabel: `image${activeSlideIndex + 1}`
  });

  activeZoomSlide.value = activeSlideIndex;
};

watch(configuratorColorIndex, (value) => {
  galleryColorIndex.value = value;
  currentColor.value = colorsToShow.value[value];
}, { immediate: true });

watch(colorsToShow, () => {
  galleryColorIndex.value = configuratorColorIndex.value;
  currentColor.value = colorsToShow.value[configuratorColorIndex.value];
}, { immediate: true });
</script>

<style lang="scss" scoped>
.pdp-section-gallery {
  &__zoom-carousel {
    .swiper {
      @apply h-full;
      @apply overflow-visible #{!important};

      picture,
      img {
        @apply h-full;
      }
    }

    .swiper-pagination {
      @apply flex items-center gap-x-8 justify-center portrait:px-16 portrait:justify-start;
      @apply absolute portrait:bottom-auto portrait:left-auto portrait:top-[calc(100%+8px)] #{!important};
    }

    .swiper-pagination-bullet {
      @apply hover:bg-neutral-800 after:rounded-2 after:block after:bg-neutral-500 after:h-2;
      @apply w-24 md:w-32 #{!important};

      background-color: transparent !important;
      margin: 0 !important;
      padding-bottom: 13px;
      padding-top: 13px;
    }

    .swiper-pagination-bullet-active {
      @apply after:bg-white portrait:after:bg-neutral-800;
    }

    .swiper-button-prev,
    .swiper-button-next {
      @apply border-0;
    }

    .swiper-button-prev {
      @apply left-24;
    }

    .swiper-button-next {
      @apply right-24;
    }

    .swiper-slide-active {
      @apply landscape:after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[100px] landscape:after:bg-gradient-to-t after:from-black/40 after:via-black/10;
    }
  }
}
</style>
