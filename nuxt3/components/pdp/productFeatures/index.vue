<template>
  <section class="bg-beige-100 md-max:pb-32">
    <PdpProductFeaturesMobile
      v-bind="{
        items,
        openDrawer: edgeShelf,
        eventAction: 'Additional info'
      }"
      v-on:cta-click="handleClick"
    />

    <PdpMosaic
      class="py-16 md:py-32 lg:py-48 xl:py-56 grid-container hidden lg:block"
      v-bind="{
        items,
        eventAction: 'Additional info'
      }"
      v-on:cta-click="handleClick"
    />

    <ModalProductFeatures
      v-if="openModal"
      v-model="openModal"
      v-bind="{
        data: activeItemSlide
      }"
    />

    <BaseDrawer
      v-if="edgeShelf"
      ref="productFeaturesDrawer"
      v-model="isDrawerOpen"
      v-bind:modern-drawer="true"
    >
      <PdpProductFeaturesDrawer
        v-bind="{
          drawerData,
          furnitureCategory
        }"
      />
    </BaseDrawer>
  </section>
</template>

<script setup lang="ts">
import useFeatures from '~/composables/pdp/useFeatures';

import { type Item } from '~/consts/productPage';
import { type FURNITURE_TYPES_KEYS, TYPE_TO_LINE } from '~/consts/types';

const props = defineProps({
  furnitureCategory: {
    type: String as PropType<'bookcase' | 'wallstorage' | 'desk' | 'vinylstorage' | 'sideboard' | 'chest' | 'bedsidetable' | 'tvstand' | 'wardrobe' | 'shoerack'>,
    required: true
  },
  furnitureType: {
    type: String as PropType<FURNITURE_TYPES_KEYS>,
    required: true
  },
  material: {
    type: Number,
    required: true
  }
});

const { drawer, getFeatureItems } = useFeatures();

const { locale } = useLocale();

const drawerData = computed(() => drawer(props.furnitureCategory));

const items = computed<Item[]>(() => getFeatureItems(props.furnitureType, props.furnitureCategory, props.material, locale.value));

const edgeShelf = TYPE_TO_LINE[props.furnitureType as FURNITURE_TYPES_KEYS] === 'edge' && props.furnitureCategory !== 'wardrobe';

const openModal = ref(false);
const isDrawerOpen = ref(false);
const activeItemSlide = ref<typeof items.value[number]['modalSlides'] | null>(null);

const handleClick = (item: typeof items.value[number]) => {
  if (item.modalSlides!.length > 0) {
    if (edgeShelf) {
      isDrawerOpen.value = true;
    } else {
      openModal.value = true;
      activeItemSlide.value = item.modalSlides;
    }
  }
};
</script>
