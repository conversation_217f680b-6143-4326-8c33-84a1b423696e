<template>
  <Fragment>
    <BasePicture
      type="M"
      v-bind="{
        path: `pdp/drawers/colors/${displayedImage}`,
        class: `overflow-hidden ${pictureClasses}`,
        imgClasses: `block h-full max-w-full w-full object-cover pointer-events-none ${imgClasses}`,
        alt: title,
        isRetinaUploaded: false
      }"
    />
    <div
      v-bind:class="`flex flex-wrap flex-auto ${swatchWrapperClasses}`"
    >
      <PdpDrawerMaterialSwatch
        v-for="(color, colorIndex) in colors"
        v-bind:key="`${groupIndex}-${colorIndex}`"
        v-bind="{
          variant,
          isSelected: colorsObj[color].iconPath === displayedImage,
          label: colorsObj[color].nameKey,
          iconPath: colorsObj[color].iconPath,
          'data-testid': `popup-colors-${colorsObj[color].materialId}`,
          trackData: {
            eventCategory: 'popup-colors',
            eventAction: 'popup-colors-action',
            eventLabel: `${colorIndex}`,
            eventParam_0: colorsObj[color].materialId,
          },
        }"
        v-on:click="changeDisplayedImage(colorsObj[color].iconPath)"
      />
    </div>
  </Fragment>
</template>

<script setup lang="ts">
import { FURNITURE_TYPES_KEYS } from '~/consts/types';
import { COLORS } from '~/consts/colors';

const props = defineProps({
  imgClasses: {
    type: String,
    default: ''
  },
  pictureClasses: {
    type: String,
    default: ''
  },
  swatchWrapperClasses: {
    type: String,
    default: ''
  },
  defaultImage: {
    type: String,
    required: true
  },
  groupIndex: {
    type: [String, Number],
    required: true
  },
  title: {
    type: String,
    required: true
  },
  colors: {
    type: Array,
    validator (val) {
      return Array.isArray(val) && val.length > 0;
    },
    required: true
  },
  shelfTypeTitle: {
    type: FURNITURE_TYPES_KEYS,
    required: true
  },
  variant: {
    type: String,
    default: 'm'
  }
});

const colorsObj = COLORS()[props.shelfTypeTitle as FURNITURE_TYPES_KEYS];
const displayedImage = ref(props.defaultImage);

const changeDisplayedImage = (iconPath: string) => {
  displayedImage.value = iconPath;
};
</script>
