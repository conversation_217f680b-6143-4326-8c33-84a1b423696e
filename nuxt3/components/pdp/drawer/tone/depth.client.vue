<template>
  <BaseDrawer
    ref="toneDepthDrawer"
    v-model="isDrawerOpen"
    v-bind:modern-drawer="true"
    v-on:update:model-value="$emit('update:modelValue', $event);"
  >
    <aside>
      <h2 class="semibold-24 md:semibold-28 xl:semibold-32">
        {{ $t('popup.cvert.depth_title') }}
      </h2>
      <div class="flex-auto md:grid md:grid-cols-2 md:gap-32 mt-32">
        <div
          v-for="(item, index) in data"
          v-bind:key="index"
          class="md-max:mb-24 md-max:last:mb-0"
        >
          <BasePicture
            img-classes="block max-w-full w-full object-cover pointer-events-none"
            class="block overflow-hidden rounded-12 mb-12"
            type="M T"
            v-bind="{
              path: `pdp/drawers/depth/${index}`,
              alt: item.title
            }"
          />
          <h3
            class="bold-20 text-offblack-900 mb-12"
            v-html="item.title"
          />
          <h4
            class="bol-12 text-offblack-900 mb-12"
            v-html="item.subtitle"
          />
          <p
            class="normal-12 text-offblack-600"
            v-bind:class="{ 'mb-8 md:mb-12': item.additional }"
            v-html="item.description"
          />
          <p
            v-if="item.additional"
            class="normal-10 text-offblack-600"
            v-html="item.additional"
          />
        </div>
      </div>
    </aside>
  </BaseDrawer>
</template>

<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  }
});

const i18n = useI18n();

const data = [
  {
    title: i18n.t('config.modal.depth.pic1.title'),
    subtitle: i18n.t('config.modal.depth.pic1.subtitle'),
    description: i18n.t('config.modal.depth.pic1.body'),
    additional: i18n.t('config.modal.depth.pic1.description')
  },
  {
    title: i18n.t('config.modal.depth.pic2.title'),
    subtitle: i18n.t('config.modal.depth.pic2.subtitle'),
    description: i18n.t('config.modal.depth.pic2.body')
  }
];

const isDrawerOpen = ref(props.modelValue || false);

defineEmits<{ 'update:modelValue': [value: false]}>();

watch(() => props.modelValue, (value) => {
  isDrawerOpen.value = value;
});
</script>
