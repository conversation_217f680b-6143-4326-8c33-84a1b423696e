<template>
  <NuxtErrorBoundary v-on:error="$logException">
    <section
      id="pdp-section-reviews"
      :class="extraClasses"
      class="pb-32 md:pb-32 lg:py-64 pdp-reviews bg-beige-100"
    >
      <div class="grid-container md:grid-cols">
        <p
          class="col-span-12 uppercase text-neutral-800 semibold-14 lg:semibold-18"
          v-html="$t('pdp.reviews.headline', { reviewsCount })"
        />
        <hr class="col-span-12 my-16 border-neutral-500 md:my-32">
        <aside class="md:col-span-4 lg:col-span-3">
          <p
            class="text-neutral-900 normal-54 md:bold-54 lg:normal-80"
            data-testid="review-score"
            v-html="`${reviewsAverageScore}/5`"
          />
          <p
            class="my-16 md:my-8 lg:my-24 text-neutral-800 normal-12 md:normal-10 lg:normal-12 pdp-reviews__terms"
            data-testid="review-terms"
            v-html="$t('pdp.reviews.terms')"
          />
          <BaseLink
            class="inline-flex semibold-16 text-neutral-900"
            data-testid="review-show-all-link"
            variant="underlined"
            target="_blank"
            v-bind="{
              href: $addLocaleToPath('review-list'),
              trackData: {
                eventType: 'NOEEC',
                eventCategory: 'pdp_clicks',
                eventAction: 'Reviews',
                eventLabel: 'See all reviews'
              }
            }"
          >
            {{ $t('pdp.reviews.cta1') }}
          </BaseLink>
        </aside>
        <div class="mt-32 md:col-span-8 md:col-start-5 lg:col-span-7 lg:col-start-6 md:mt-0">
          <TransitionGroup
            name="pdp-reviews__list"
            tag="div"
            class="flex flex-col gap-16"
          >
            <article
              v-for="(review, index) in reviews.slice(0, reviewsAmount)"
              :key="index"
              class="flex items-start cursor-pointer"
              data-testid="review-article"
              v-on:click="() => handleActiveDrawer(review)"
            >
              <img
                v-bind="{
                  src: review.photoSmallWebp,
                  alt: review.title
                }"
                class="aspect-square max-w-[104px] md:max-w-[120px] lg:max-w-[140px] w-full md:mr-16 mr-24 object-cover"
                data-testid="review-article-img"
              >

              <div>
                <div class="flex flex-row items-center">
                  <div
                    class="flex rating-stars"
                    data-testid="review-article-stars"
                  >
                    <Star
                      v-for="number in 5"
                      :key="number"
                      data-testid="review-article-star"
                      class="w-12 h-12 gap-4"
                      :style="{
                        fill: number <= review.score ? 'var(--active)' : 'var(--inactive)',
                        gap: 4
                      }"
                    />
                  </div>

                  <NuxtTime
                    class="ml-16 text-neutral-600 normal-10 sm:normal-14 md:normal-16"
                    data-testid="review-article-date"
                    :datetime="new Date(review.createdAt)"
                    year="numeric"
                    month="long"
                    day="numeric"
                  />
                </div>

                <h3
                  class="mt-16 neutral-900 bold-14 md:bold-16 lg:bold-18"
                  data-testid="review-article-title"
                  v-html="review.title"
                />

                <p
                  class="mt-4 neutral-900 normal-14 md:normal-16"
                  data-testid="review-article-location"
                  v-html="`${review.name}${$t('pdp.reviews.from')}${$t(`regions.${review.country}`)}`"
                />
              </div>

              <IconCaretRight
                class="shrink-0 hidden ml-auto md:block"
                data-testid="review-article-caret-right"
              />
            </article>
          </TransitionGroup>

          <BaseButton
            v-if="reviewsAmount === 3"
            class="mt-24 ty-link-m"
            data-testid="review-show-more-button"
            variant="underlined"
            v-bind="{
              trackData: {
                eventType: 'NOEEC',
                eventCategory: 'pdp_clicks',
                eventAction: 'Reviews',
                eventLabel: 'Show more'
              }
            }"
            v-on="{ click: () => reviewsAmount = 6 }"
          >
            <span>{{ $t('pdp.reviews.cta2') }}</span>
          </BaseButton>

          <BaseLink
            v-else
            class="mt-24 semibold-16"
            data-testid="review-show-all-link"
            variant="underlined"
            v-bind="{
              trackData: {
                eventType: 'NOEEC',
                eventCategory: 'pdp_clicks',
                eventAction: 'Reviews',
                eventLabel: 'See all reviews'
              },
              href: $addLocaleToPath('review-list')
            }"
            target="_blank"
          >
            {{ $t('pdp.reviews.cta1') }}
          </BaseLink>
        </div>
      </div>

      <BaseDrawer
        ref="reviewsDrawer"
        v-model="isDrawerOpen"
        :modern-drawer="true"
      >
        <aside v-if="activeDrawer">
          <h2 class="semibold-24 md:semibold-28 xl:semibold-32 md:mr-32">
            {{ $t(activeDrawer.title) }}
          </h2>
          <div class="flex flex-row items-center mb-12 mt-32 first:mt-0">
            <div class="flex rating-stars">
              <Star
                v-for="number in 5"
                :key="number"
                class="w-12 h-12 gap-4"
                :style="{
                  fill: number <= activeDrawer.score ? 'var(--active)' : 'var(--inactive)',
                  gap: 4
                }"
              />
            </div>

            <NuxtTime
              class="ml-16 text-neutral-600 normal-10 sm:normal-14 md:normal-16"
              data-testid="review-article-date"
              :datetime="new Date(activeDrawer.createdAt)"
              year="numeric"
              month="long"
              day="numeric"
            />
          </div>
          <p
            class="normal-16 text-neutral-900"
            v-html="$t(activeDrawer.description)"
          />
          <p
            class="my-24 text-neutral-900 normal-14 md:normal-16"
            v-html="`${activeDrawer.name}${$t('pdp.reviews.from')}${$t(`regions.${activeDrawer.country}`)}`"
          />
          <img
            :src="activeDrawer.photoSmallWebp"
            :alt="activeDrawer.title"
            class="aspect-square max-w-[280px] w-full object-cover"
          >
        </aside>
      </BaseDrawer>
    </section>
  </NuxtErrorBoundary>
</template>

<script setup lang="ts">
import Star from '~/assets/icons/star.svg';

interface item {
  name: string,
  title: string,
  description: string,
  score: number,
  createdAt: string,
  country: string,
  photoSmallWebp: string
}

defineProps({
  extraClasses: {
    type: String,
    default: ''
  },
  reviews: {
    type: Array as PropType<item[]>,
    required: true
  },
  reviewsCount: {
    type: Number,
    default: 0
  },
  reviewsAverageScore: {
    type: Number,
    default: 0
  }
});

const gtm = useGtm();
const reviewsAmount = ref(3);
const activeDrawer = ref<item | null>(null);

const isDrawerOpen = computed({
  get () {
    return !!activeDrawer.value;
  },
  set (value) {
    if (!value) {
      activeDrawer.value = null;
    }
  }
});

const handleActiveDrawer = (item: item) => {
  gtm && gtm.push({
    event: 'userInteraction',
    eventType: 'NOEEC',
    eventCategory: 'pdp_clicks',
    eventAction: 'Reviews',
    eventLabel: 'Show review'
  });

  activeDrawer.value = item;
};
</script>

<style lang="scss" scoped>
.pdp-reviews {
  &__terms {
    a {
      @apply underline;
    }
  }
}

.pdp-reviews__list-enter-active,
.pdp-reviews__list-leave-active {
  transition: all 1s;
}

.pdp-reviews__list-enter,
.pdp-reviews__list-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

.rating-stars {
  --active: theme('colors.neutral.900');
  --inactive: theme('colors.neutral.500');
}
</style>
