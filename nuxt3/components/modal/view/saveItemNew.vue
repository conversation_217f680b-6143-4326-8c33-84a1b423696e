<template>
  <div class="text-left">
    <div class="h-full grid items-center relative">
      <div class="p-24 md:p-48 lg:p-96">
        <FormKit
          v-model="form"
          type="form"
          :actions="false"
          v-on:submit="submit"
        >
          <h3
            class="text-neutral-900 bold-24 md:bold-32 mb-8 text-center px-[42px] md:px-0"
            v-html="$t('pdp.s4l.common_s4l_email_popup_title_new')"
          />
          <p
            class="normal-16 text-neutral-900 text-center mb-20 md:mb-24"
            data-testid="s4l-subtext"
            v-html="$t('pdp.s4l.common_s4l_email_popup_body_new')"
          />
          <FormKit
            type="email"
            name="email"
            data-testid="s4l-email-input"
            :validation="'required|email'"
            autocomplete="email"
            v-bind="{
              label: $t('pdp.s4l.common_s4l_email_popup_texfield_label'),
              placeholder: $t('pdp.s4l.common_s4l_email_popup_texfield_input'),
              outerClass: 'mb-24 md:w-full',
              validationMessages: {
                required: $t('common.validation.required')
              }
            }"
          />
          <div class="normal-10 text-offblack-600 flex flex-col gap-4 mt-16 mb-24">
            <FormKit
              class="normal-10"
              name="marketingPermission"
              type="tyBox"
              input-classes="normal-12 flex-none"
              data-testid="s4l-permission-checkbox"
            >
              <template #labelExtra>
                <span class="normal-12 block">
                  <span v-html="model !== 'sotty' ? $t('pdp.s4l.common_s4l_rodo_checkbox_text_3', { value: bonus }) : $t('pdp.s4l.common_s4l_rodo_checkbox_text_3_without_promo')" />
                  <input
                    id="s4l-read-more"
                    type="checkbox"
                    class="hidden focus:outline-none peer"
                  >
                  <label
                    for="s4l-read-more"
                    class="text-orange cursor-pointer pl-2
                      inline peer-checked:hidden"
                    v-html="$t('common.learn_more')"
                  />
                  <label
                    for="s4l-read-more"
                    class="text-orange cursor-pointer pl-2
                      hidden peer-checked:inline"
                    v-html="$t('common.see_less')"
                  />
                  <p
                    id="s4l-more-text"
                    class="hidden peer-checked:block mt-4"
                    v-html="$t('pdp.s4l.common_s4l_rodo_checkbox_text_2')"
                  />
                </span>
              </template>
            </FormKit>
          </div>
          <FormKit
            type="submit"
            data-testid="s4l-submit-button"
            v-bind="{
              inputClass: `btn-cta block w-full relative ${isFormBeingSent ? '!text-orange !bg-orange' : 'text-white'}`
            }"
          >
            <div class="p-4 bold-14">
              {{ $t('pdp.s4l.common_s4l_rodo_button_new') }}
              <UiDotsLoader
                v-if="isFormBeingSent"
                class="mt-2"
                bounce-class="bg-white"
              />
            </div>
          </FormKit>
        </FormKit>
        <div class="flex flex-row mt-24 md:mt-32">
          <div class="w-full text-center text-orange bold-12 md:bold-18 mr-8 md:mr-32">
            <IconS4lPencil class="mx-auto mt-4" />
            <p v-html="$t('pdp.s4l.common_s4l_rodo_usps_1')" />
          </div>
          <div class="w-full text-center text-orange bold-12 md:bold-18">
            <IconS4lPromo class="mx-auto mt-4" />
            <p v-html="$t('pdp.s4l.common_s4l_rodo_usps_2')" />
          </div>
          <div class="w-full text-center text-orange bold-12 md:bold-18 ml-8 md:ml-32">
            <IconS4lDurability class="mx-auto mt-4" />
            <p v-html="$t('pdp.s4l.common_s4l_rodo_usps_3')" />
          </div>
        </div>
        <Transition name="fade">
          <div
            v-if="isFormSent"
            class="h-full w-full absolute inset-0 z-1 flex items-center bg-white"
          >
            <div class="py-0 px-16 md:px-64 max-h-[90vh]">
              <h2
                class="bold-32 md:bold-54 text-offblack-800 text-center mb-24 md:mb-40"
                v-html="$t('pdp.s4l.common_s4l_success_popup_title')"
              />
              <p
                class="normal-20 md:normal-28 text-offblack-800 text-center"
                v-html="$t('pdp.s4l.common_s4l_success_popup_body')"
              />
            </div>
          </div>
        </Transition>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useToast } from 'vue-toastification';
import { storeToRefs } from 'pinia';
import type { CartItem } from '~/types/userStatus';
import { ADD_TO_UNSIGNED_WISHLIST_BY_ID } from '~/api/wishlist';
import useWishlist from '~/composables/cart/useWishlist';
import { pdpAnalytics } from '~/composables/pdp/pdpAnalytics';
import { useScartStore } from '~/stores/scart';
import { hashSHA256 } from '~/composables/useSha';
const { removeItemFromCart } = useWishlist();
const { s4lGa4Event, cartGenerateLead } = pdpAnalytics({});
const $gtm = useGtm();
const {
  cartItems
} = storeToRefs(useScartStore());

const emit = defineEmits<{
  close: [];
}>();
const props = withDefaults(defineProps<{
  furnitureId: string;
  model: TYPE_MODEL;
  isMoveToWishlist: boolean;
}>(), { isMoveToWishlist: false });

const isFormBeingSent = ref(false);
const isFormSent = ref(false);
const form = ref({
  email: '',
  marketingPermission: false
});

const i18n = useI18n();
const toast = useToast();
const { $logException } = useNuxtApp();
const { FETCH_GLOBAL } = useGlobal();
const { regionCode } = storeToRefs(useGlobal());

const bonusMap: Record<string, string> = {
  UK: '£325',
  CH: '380 CHF',
  PL: '1250 zł',
  NO: 'kr 3800 (NOK)',
  DK: '2700kr (DKK)',
  SE: '3800kr (SEK)'
};
const bonus = computed(() => bonusMap[regionCode.value?.toUpperCase()] || '300€');

const submit = async () => {
  if (isFormSent.value || props.furnitureId === null) { return; }

  isFormBeingSent.value = true;

  try {
    await ADD_TO_UNSIGNED_WISHLIST_BY_ID(
      props.furnitureId,
      props.model,
      form.value.email,
      form.value.marketingPermission,
      'cart'
    );

    if (props.isMoveToWishlist) {
      await removeItemFromCart(props.furnitureId, props.model);
    }

    await FETCH_GLOBAL();

    const furnitureItem = cartItems.value.find((item: CartItem) => item.itemId === props.furnitureId);

    $gtm?.push({ ecommerce: null });

    const eventData = furnitureItem && await s4lGa4Event(furnitureItem, furnitureItem.itemId);

    furnitureItem && $gtm?.push(eventData);

    if (form.value.marketingPermission) {
      $gtm?.push({ ecommerce: null });
      $gtm?.push(cartGenerateLead(hashSHA256(form.value.email)));
    }
  } catch (error) {
    toast.error(i18n.t('common.error.connection'));
    $logException(error);
  } finally {
    isFormSent.value = true;
    setTimeout(() => {
      emit('close');
    }, 10000);
    isFormBeingSent.value = false;
  }
};
</script>

<style lang="scss">
#s4l-read-more {
  &:checked + #s4l-more-text {
    height: auto;
    opacity: 1;
  }
}
</style>
