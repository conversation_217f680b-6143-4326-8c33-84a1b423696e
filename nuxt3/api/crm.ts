import { useApi } from '~/composables/useApi';

type FeedbackType = 'checkout' | 'configurator'
type ItemContentType = FurnitureModel

interface FeedbackPayload {
  score: number
  feedback: string
  orderId?: number
  itemId?: number
  lang: string
  itemContentType?: ItemContentType
}

export async function sendFeedback ({
  score,
  feedback,
  orderId,
  itemId,
  lang,
  type = 'checkout' as FeedbackType,
  itemContentType
}: FeedbackPayload & { type: FeedbackType }) {
  const payload = {
    type,
    body: {
      score,
      feedback,
      lang,
      ...(type === 'configurator'
        ? {
            itemContentType,
            itemId
          }
        : {
            orderId
          })
    }
  };

  try {
    return await useApi('/api/v1/ecommerce/survey/', {
      method: 'POST',
      body: payload
    });
  } catch (error) {
    console.error('Failed to send feedback:', error);
    throw new Error('Failed to submit feedback. Please try again later.');
  }
}
