import { useApi } from '~/composables/useApi';

export const REVIEWS_GLOBAL = () => useApi<{ avgScore: number, reviewsCount:number, categories: string[] }>('api/v1/global-review-score/', {
  transform: (response: {avg_score: number, reviews_count: number, categories: string[]}) => {
    return {
      avgScore: response.avg_score,
      reviewsCount: response.reviews_count,
      categories: response.categories || []
    };
  }
});

export const REVIEWS = (regionCode: string, page: number, category?: string, ordering?: string, tags?: string[]) => useApi(`/api/v2/reviews/?language=${regionCode}&page=${page}${category ? `&category=${category}` : ''}${ordering ? `&ordering=${ordering}` : ''}${tags && tags.length > 0 ? `&tags=${tags.join(',')}` : ''}`);
