export default defineNuxtPlugin(() => {
  return {
    provide: {
      addLocaleToPath: (pageKey: string, params?: Record<string, string>) => {
        const { $i18n } = useNuxtApp();

        const lang = `/${$i18n.locale.value}`;

        const translation = $i18n.t(`url.${pageKey}`);
        let url = `${lang}${translation}`;

        if (params) {
          const queryString = Object.entries(params).reduce((previousValue, [key, value], index) => {
            const prefix = index ? '&' : '';
            return `${previousValue}${prefix}${key}=${value}`;
          }, '?');

          url += queryString;
        }

        return url;
      },
      addLocaleToURL: (path: string, params?: Record<string, string>) => {
        const { $i18n } = useNuxtApp();

        const lang = `/${$i18n.locale.value}`;

        let url = `${lang}${path}`;

        if (params) {
          const queryString = Object.entries(params).reduce((previousValue, [key, value], index) => {
            const prefix = index ? '&' : '';
            return `${previousValue}${prefix}${key}=${value}`;
          }, '?');

          url += queryString;
        }

        return url;
      },
      getLocale: () => {
        const { $i18n } = useNuxtApp();

        return $i18n.locale.value.split('-')[0] as Language;
      },

      retrieveErrorMessageFromResponse: (response: any) => {
        if (response) {
          if (response?.detail) { return response.detail; }

          const errorKey = Object.keys(response?.error_codes || response?.errorCodes || [])?.[0];

          if (errorKey) {
            const errorMessage = response[errorKey]?.[0] || response[errorKey];

            if (errorMessage) {
              return errorMessage;
            }
          }
        }

        return null;
      }
    }
  };
});

// index.d.ts
declare module '#app' {
  interface NuxtApp {
    $addLocaleToPath: (pageKey: string, params?: Record<string, string>) => string
    $addLocaleToURL: (path: string, params?: Record<string, string>) => string
    $getLocale: () => Language
    $retrieveErrorMessageFromResponse: (response: any) => string | null
  }
}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $addLocaleToPath: (pageKey: string, params?: Record<string, string>) => string
    $addLocaleToURL: (path: string, params?: Record<string, string>) => string
    $getLocale: () => Language
    $retrieveErrorMessageFromResponse: (response: any) => string | null
  }
}
