import { isNil } from 'lodash-es';
import { GET_SHELF_TYPE } from '~/utils/types';
import { useAnalytics } from '~/composables/analytics/useAnalytics';
import useColors from '~/composables/useColors';
import { useScartStore } from '~/stores/scart';
import { dnaName } from '~/helpers/getDnaName';

export const pdpAnalytics = (dataState: any) => {
  const { userId, userHashEmail } = storeToRefs(useGlobal());
  const { cartItems, promocodeName, cartUsedAssembly } = storeToRefs(useScartStore());
  const { getColor } = useColors();
  const { getFurnitureEnglishName: getItemName } = useAnalytics();
  const { $logException } = useNuxtApp();
  const { code: promoCode } = usePromoStore();
  const { FETCH_GLOBAL, REGION_NAME } = useGlobal();

  const viewItemGA4 = (productID: any) => {
    const { materialName } = getColor(dataState?.shelfType, dataState?.material);

    return {
      event: 'view_item',
      userId: userId.value || null,
      ecommerce: {
        currency: 'EUR',
        value: dataState?.priceInEuro ? parseFloat(dataState.priceInEuro).toFixed(2) : null,
        value_f: dataState?.priceInEuro ? parseFloat(dataState.priceInEuro).toFixed(2) : null,
        product_type: 'furniture',
        items: [{
          item_id: productID || null,
          item_name: getItemName(
            dataState?.shelfType <= 2 ? 'jetty' : dataState?.shelfType === 10 ? 'sotty' : 'watty',
            dataState?.patternName,
            dataState?.furnitureCategory,
            dataState?.shelfType,
            dataState?.contentType
          ) || null,
          affiliation: 'Tylko',
          quantity: 1,
          coupon: promoCode || null,
          item_brand: 'furniture',
          item_category: dataState?.furnitureCategory || null,
          item_category2: dataState?.patternName || null,
          item_category3: dataState?.configuratorType || null,
          item_category5: materialName || null,
          item_list_name: null, // TODO: BE
          item_variant: !isNil(dataState?.shelfType) ? GET_SHELF_TYPE(dataState.shelfType, true) : null,
          price: dataState?.priceInEuro ? parseFloat(dataState.priceInEuro).toFixed(2) : null,
          price_discounted: dataState?.priceWithDiscountInEuro ? parseFloat(dataState.priceWithDiscountInEuro).toFixed(2) : null,
          assembly: dataState?.shelfType === 3 ? 'built-in' : 'additional', // TODO: Nie ma dostepu do carta. Do potwierdzenia czy tak to ma wyglądać
          item_id_preset: productID || null
        }]
      }
    };
  };

  const configureItemGA4 = (productID: any) => {
    const viewItem = viewItemGA4(productID);
    const { event, ...viewItemData } = viewItem;
    return {
      event: 'view_configure',
      viewItem: viewItemData
    };
  };

  const a2cGa4Event = async (productID: any) => {
    if (!userId.value) {
      await FETCH_GLOBAL();
    }

    if (!cartItems.value) {
      $logException('a2cGa4Event: cart is empty');
      return null;
    }

    const item = cartItems.value[0];

    if (!item) {
      return null;
    }

    const payloadPrice = item?.region_price_in_euro ? parseFloat(item.region_price_in_euro).toFixed(2) : null;
    const { materialName } = getColor(item?.shelf_type, item?.material);
    const properCategoryNameList: any = {
      'vinyl storage': 'vinyl_storage',
      'bedside table': 'bedside_table'
    };

    const properCategoryName = item?.category === 'vinyl storage' || item?.category === 'bedside table' ? properCategoryNameList[item.category] : item?.category;

    return {
      event: 'add_to_cart',
      userId: userId.value || null,
      ecommerce: {
        currency: 'EUR',
        value: payloadPrice,
        value_f: payloadPrice,
        value_netto: item?.price_netto ? parseFloat(item.price_netto).toFixed(2) : null,
        product_type: 'furniture',
        coupon: promocodeName.value || null,
        items: [{
          item_id: item?.itemId || null,
          item_name: getItemName(
            item?.shelf_type <= 2 ? 'jetty' : item?.shelf_type === 10 ? 'sotty' : 'watty',
            item?.pattern_name,
            item?.category,
            item?.shelf_type,
            item?.content_type
          ) || null,
          affiliation: 'Tylko',
          quantity: 1,
          coupon: promocodeName.value || null,
          item_brand: 'furniture',
          item_category: properCategoryName || null,
          item_category2: item?.pattern_name || null,
          item_category3: item?.configuratorType || null,
          item_category5: materialName || null,
          item_list_name: null, // TODO: BE
          item_variant: item?.shelf_type ? GET_SHELF_TYPE(item.shelf_type, true) : null,
          price: payloadPrice,
          price_discounted: item?.region_price_with_discount_in_euro ? parseFloat(item.region_price_with_discount_in_euro).toFixed(2) : null,
          assembly: item?.shelf_type === 3 ? 'built-in' : (cartUsedAssembly.value ? 'additional' : null), // TODO: BE
          item_id_preset: productID || null // TODO: BE,
        }]
      }
    };
  };

  const cartGenerateLead = (hashEmail: string) => ({
    event: 'generate_lead',
    userId: userId.value || null,
    placement: 'save_for_later_cart',
    leadsUserData: {
      ea: hashEmail || null,
      address: {
        country: REGION_NAME || null
      }
    }
  });

  const s4lGa4Event = async (item: any, _productID: any, email?: any) => {
    if (!item) {
      return null;
    }

    const { materialName } = getColor(item?.shelf_type, item?.material);
    const payloadPrice = item?.region_price_in_euro ? parseFloat(item.region_price_in_euro).toFixed(2) : null;
    const properCategoryNameList: any = {
      'vinyl storage': 'vinyl_storage',
      'bedside table': 'bedside_table'
    };
    const properCategoryName = item?.category === 'vinyl storage' || item?.category === 'bedside table' ? properCategoryNameList[item.category] : item?.category;

    if (!userId.value) {
      await FETCH_GLOBAL();
    }

    return {
      event: 'add_to_wishlist',
      eventLabel: undefined,
      userId: userId.value,
      ea: email || userHashEmail.value,
      ecommerce: {
        currency: 'EUR',
        value: payloadPrice,
        value_f: payloadPrice,
        value_netto: null, // TODO: BE,
        product_type: 'furniture',
        items: [{
          item_id: (!isNil(item?.id) ? item?.id : item?.shelfId) || null,
          item_name: getItemName(
            item?.shelf_type <= 2 ? 'jetty' : item?.shelf_type === 10 ? 'sotty' : 'watty',
            item?.pattern_name,
            item?.category,
            item?.shelf_type,
            item?.content_type
          ) || null,
          affiliation: 'Tylko',
          quantity: 1,
          coupon: promoCode || null,
          item_brand: 'furniture',
          item_category: properCategoryName || null,
          item_category2: item?.pattern_name ? item.pattern_name : (item?.pattern && typeof item.pattern === 'number' && dnaName[item.pattern as keyof typeof dnaName] ? dnaName[item.pattern as keyof typeof dnaName] : null),
          item_category3: item?.configuratorType ? item.configuratorType : item?.configurator_type || null,
          item_category5: materialName || null,
          item_list_name: null, // TODO: BE
          item_variant: item?.shelf_type ? GET_SHELF_TYPE(item.shelf_type, true) : null,
          price: payloadPrice,
          price_discounted: item?.region_price_with_discount_in_euro ? parseFloat(item.region_price_with_discount_in_euro).toFixed(2) : null,
          assembly: item?.shelf_type === 3 ? 'built-in' : 'additional',
          item_id_preset: item?.base_preset || null,
          delivery_time_min: item?.delivery_time_min,
          delivery_time_max: item?.delivery_time_max
        }]
      }
    };
  };

  return {
    viewItemGA4,
    configureItemGA4,
    a2cGa4Event,
    s4lGa4Event,
    cartGenerateLead
  };
};
