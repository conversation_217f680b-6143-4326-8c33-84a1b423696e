import type { Color } from '~/consts/colors';

export default function () {
  const {
    activeCategory,
    comparisonColor1,
    comparisonColor2,
    comparisonColor3,
    availableProductLines,
    comparisonPageCategories,
    miniGridFilters
  } = storeToRefs(useComparisonPageStore());

  const { comparisonDefaultColor1, comparisonDefaultColor2, comparisonDefaultColor3 } = storeToRefs(useComparisonPageStore());
  const comparisonColorWithDefaultValue1 = computed((): Color|null => comparisonColor1.value || comparisonDefaultColor1.value);
  const comparisonColorWithDefaultValue2 = computed((): Color|null => comparisonColor2.value || comparisonDefaultColor2.value);
  const comparisonColorWithDefaultValue3 = computed((): Color|null => comparisonColor3.value || comparisonDefaultColor3.value);

  const comparisonColorDefaultValue1 = computed((): Color|null => comparisonDefaultColor1.value);
  const comparisonColorDefaultValue2 = computed((): Color|null => comparisonDefaultColor2.value);
  const comparisonColorDefaultValue3 = computed((): Color|null => comparisonDefaultColor3.value);
  return {
    comparisonColor1,
    comparisonColor2,
    comparisonColor3,
    comparisonColorWithDefaultValue1,
    comparisonColorWithDefaultValue2,
    comparisonColorWithDefaultValue3,
    availableProductLines,
    activeCategory,
    miniGridFilters,
    comparisonPageCategories,
    comparisonColorDefaultValue1,
    comparisonColorDefaultValue2,
    comparisonColorDefaultValue3
  };
}
