import { regions } from '~/consts/regions';
import { LOCALE_NAMES } from '~/utils/languages';

export const getLanguageFromCountry = (countryName: string): string => {
  if (!countryName || !regions[countryName as keyof typeof regions]) {
    return 'English';
  }

  const region = regions[countryName as keyof typeof regions];
  const primaryLanguage = region.langs[0];

  return LOCALE_NAMES[primaryLanguage] || 'English';
};

export const getLanguageCodeFromCountry = (countryName: string): string => {
  if (!countryName || !regions[countryName as keyof typeof regions]) {
    return 'en';
  }

  const region = regions[countryName as keyof typeof regions];
  return region.langs[0];
};
