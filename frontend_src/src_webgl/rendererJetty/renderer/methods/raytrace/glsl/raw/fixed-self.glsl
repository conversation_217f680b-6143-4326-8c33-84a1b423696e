precision mediump float;

uniform float iGlobalTime;
uniform vec2 iResolution;

float rotation;
   
#pragma glslify: square = require('glsl-square-frame')

vec2 doModel(vec3 p);
#pragma glslify: raytrace = require('glsl-raytrace', map = doModel, steps = 80)
#pragma glslify: normal = require('glsl-sdf-normal', map = doModel)
  
vec2 doModel2(vec3 p);
#pragma glslify: normal2 = require('glsl-sdf-normal', map = doModel2)

#pragma glslify: camera = require('glsl-camera-ray')
#pragma glslify: noise = require('glsl-noise/simplex/4d')
#pragma glslify: box = require('glsl-sdf-box') 

#pragma glslify: blinnPhongSpec = require('glsl-specular-phong') 

#pragma glslify: combine = require('glsl-combine-smooth')
#pragma glslify: opU = require('glsl-sdf-ops/union')
#pragma glslify: opS = require('glsl-sdf-ops/subtraction')

#pragma glslify: combine2 = require('glsl-combine-chamfer')

vec2 doModel(vec3 p) {
  
  vec3 scale = vec3(2.0,2.0,2.0);
  float szafka = box(p,vec3(1.2,1.0,0.3)*scale);
  
  float c = box(p-vec3(-rotation,.65,.0)*scale,vec3(1.15,.3,1.0)*scale);
  float c2 = box(p-vec3(rotation,.0,.0)*scale,vec3(1.15,.3,1.0)*scale);
  float c3 = box(p-vec3(rotation*0.5,-.65,.0)*scale,vec3(1.15,.3,1.0)*scale);

  
  szafka = opS(c, szafka);
  szafka = opS(c2, szafka);
  szafka = opS(c3, szafka);

  
  float podloga = box(p-vec3(0.,-2.1,0.),vec3(10.,.001,10.0));
  float sciana = box(p-vec3(0.,.0,-1.0),vec3(10.1,6.0,.01));
  float scena = combine(szafka, podloga , 0.);
  scena = combine(scena, sciana, 0.);
  return vec2(scena, .0);
}

vec2 doModel2(vec3 p) {
  
  vec3 scale = vec3(2.0,2.0,2.0);
  float szafka2 = box(p,vec3(1.2,1.0,0.3)*scale);
  
  float c = box(p-vec3(.0,.65,.0)*scale,vec3(1.15,.3,1.0)*scale);
  float c2 = box(p-vec3(.0,.0,.0)*scale,vec3(1.15,.3,1.0)*scale);
  float c3 = box(p-vec3(.0,-.65,.0)*scale,vec3(1.15,.3,1.0)*scale);

  szafka2 = opS(c, szafka2);
  szafka2 = opS(c2, szafka2);
  szafka2 = opS(c3, szafka2);
  
  return vec2(szafka2, .0);
  
}

float shadowSoft( vec3 ro, vec3 rd, float mint, float maxt, float k )
{
    float res = 1.5;
    float ph = 0.0;
    float t = 0.1;
    float m = 130.;
    for ( int i = 0; i < 13; ++i )
    {
        float h = doModel(ro + rd*t).x;
        if( h<0.0001 )
            return .0;
        float y = h*h/(2.*ph);
        float d = sqrt(h*h-y*y);
        res = min( res,k*d/max(0.0,t-y) );
        ph = h;
        t += h;
        if( res<0.0001 || t>m ) break;
    }
     return clamp( res, 0., 1.0 );

}



vec3 shade( vec3 pos, vec3 nrm, vec4 light )
{
	vec3 toLight = light.xyz - pos;
	
	float toLightLen = length( toLight );
	toLight = normalize( toLight );

	float comb = 2.0;
	float vis = shadowSoft( pos, toLight, .625, toLightLen, 1. );
	
	if ( vis > 0.0 )
	{
		float diff = 2.0 * max( 0.0, dot( nrm, toLight ) );
		comb += diff *vis;
	}
	
  return vec3(vis);

}



/*
float softshadow(vec3 ro, vec3 rd, float mint, float maxt, float k )
{
    float res = 1.0;
    float ph = 1e20;
    for( float t=mint; t < maxt; )
    {
        float h = doModel(ro + rd*t).x;
        if( h<0.001 )
            return 0.0;
        float y = h*h/(2.0*ph);
        float d = sqrt(h*h-y*y)
        res = min( res, k*d/max(0.0,t-y) );
        ph = h;
        t += h;
    }
    return res;
}*/

void orbitCamera(
  in float camAngle,
  in float camHeight,
  in float camDistance,
  in vec2 screenResolution,
  out vec3 rayOrigin,
  out vec3 rayDirection
) {
  vec2 screenPos = square(screenResolution);
  vec3 rayTarget = vec3(-0.0);

  rayOrigin = vec3(
    camDistance * sin(camAngle),
    camHeight,
    camDistance * cos(camAngle)
  );

  rayDirection = camera(rayOrigin, rayTarget, screenPos, 2.);
}

void main() {
  vec3 color = vec3(0.,0./0.,0.);
  vec3 rd,  ro;
  vec3 ro2, rd2;

  rotation = sin(iGlobalTime*.8);
  float height   = .0;
  float dist     = 6.78;
  
  orbitCamera(-.3, height, dist, iResolution.xy, ro, rd);

  float cameraAngle  = 0.8 * iGlobalTime;
 // vec3  ro    = vec3(0.0);
  vec3  rayTarget    = vec3(0, 0, 0);
  vec2  screenPos    = square(iResolution.xy);

  //rd = camera(rayOrigin, rayTarget, screenPos, 2.33);
  
  
  vec2 t = raytrace(ro, rd);
  
  
  
 // float noiseField = noise(vec4(rd*t.x,ro*t.y)) * 2.0;

  if (t.x > 0.5) {
    vec3 pos = ro + rd * t.x;
    vec3 nor = normal(pos);
    
    vec3 pos2 = ro + rd * t.x;
    vec3 nor2 = normal2(pos2);
    
    float shininess = 3.4;
    vec3 eyePosition = vec3(0.0,10.0,10.0);
    vec3 surfacePosition = pos;
    vec3 surfaceNormal = nor;

    vec3 lightPosition = vec3(0.0,5.0,9.5+rotation);
    vec3 lightPosition2 =vec3(2.0+rotation,2.5,9.0);
    lightPosition *= cos(rotation * rotation);
    vec3 eyeDirection = normalize(eyePosition - surfacePosition);
    vec3 lightDirection = normalize(lightPosition - surfacePosition);

    vec3 shaded = shade(pos, nor, vec4(lightPosition,3.2));
    vec3 shaded2 = shade(pos, nor, vec4(lightPosition2,6.2));

    float power = blinnPhongSpec(lightDirection, rd, nor, shininess);
    float power2 = blinnPhongSpec(lightDirection, rd, nor, shininess);
    
   color = vec3(dot(nor, vec3(.3, .9, .0) ));
   color = vec3(.0);
 //   color += power*0.3;
   color = mix(shaded,shaded2,.5);

  //  color *= nor;
  }
  
 
  gl_FragColor.rgb = color;
  gl_FragColor.a   = 1.0;
}