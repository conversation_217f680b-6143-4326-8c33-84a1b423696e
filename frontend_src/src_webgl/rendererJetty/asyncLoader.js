import zip from 'lodash/zip';
import flattenDeep from 'lodash/flattenDeep';
import * as THREE from 'three';
// eslint-disable-next-line import/extensions
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';

const TYLKO_BLADE_OF_SLAWEK = 'o=}===>';
const url = (a, b) => new URL(b, a).href;

const isOffscreenTool = window.gridData || window.feedData || window.galleryData;


const sideboardPlusElementsT01 = [
    'plinth',
];

const sideboardPlusElementsT02 = [
    'leg',
];

const namesType01 = [
    'hori',
    'vert',
    'support',
    'support-drawer',
    'shadowbox',
    'insert',
    ...(isOffscreenTool ? sideboardPlusElementsT01 : []),
];
const fileNamesType01 = [
    'top_bottom',
    'left_right',
    'support',
    'support_drawer',
    'shadow_box',
    'insert',
    ...(isOffscreenTool ? sideboardPlusElementsT01 : []),
];

const namesType02 = [
    'hori',
    'vert',
    'support',
    'support-drawer',
    'shadowbox',
    'insert',
    ...(isOffscreenTool ? sideboardPlusElementsT02 : []),
];
const fileNamesType02 = [
    'top_bottom',
    'left_right',
    'support',
    'support_drawer',
    'shadow_box',
    'insert',
    ...(isOffscreenTool ? sideboardPlusElementsT02 : []),
];

const namesType01Veneer = [
    'hori',
    'vert',
    'support',
    'support-drawer',
    'shadowbox',
    'fdoors',
    'fdrawer',
    'fdrawer-big',
    'grommet',
    'insert',
    ...(isOffscreenTool ? sideboardPlusElementsT01 : []),
];
const fileNamesType01Veneer = [
    'top_bottom',
    'left_right',
    'support',
    'support_drawer',
    'shadow_box',
    'fdoors',
    'fdrawer',
    'fdrawer_big',
    'grommet',
    'insert',
    ...(isOffscreenTool ? sideboardPlusElementsT01 : []),
];

class AsyncLoader {
    constructor({
        digitalProductVersion = window.cstm.item.digital_product_version,
        root = 'r_static/src_webgl/ivy/models/',
        maxFilesPerCycle = 5,
    } = { /* */}) {
        this.elements = [];
        this.cache = [];

        this.base = url(window.location.origin, root);

        const isSideboard = digitalProductVersion === 2;

        namesType01.push(
            ...(isSideboard ? sideboardPlusElementsT01 : []),
        );
        fileNamesType01.push(
            ...(isSideboard ? sideboardPlusElementsT01 : []),
        );

        namesType02.push(
            ...(isSideboard ? sideboardPlusElementsT02 : []),
        );
        fileNamesType02.push(
            ...(isSideboard ? sideboardPlusElementsT02 : []),
        );

        namesType01Veneer.push(
            ...(isSideboard ? sideboardPlusElementsT01 : []),
        );
        fileNamesType01Veneer.push(
            ...(isSideboard ? sideboardPlusElementsT01 : []),
        );
    }

    loadColorTextures(colors, shelfType = 0, { fileType = 'jpg' } = { /* */}) {
        // Loads all textures for provided color name
        // Loads colors set with priotity determined by horizontal
        // relation to onboardingColor index in array (neighbours first).
        const baseUrl = url(this.base, 'textures/');

        const fix = (name, isName) => name.replace(' ', isName ? '-' : '_');

        let names = [];
        let fileNames = [];

        switch (shelfType) {
        case 0:
            names = namesType01;
            fileNames = fileNamesType01;
            break;
        case 1:
            names = namesType02;
            fileNames = fileNamesType02;
            break;
        case 2:
            names = namesType01Veneer;
            fileNames = fileNamesType01Veneer;
            break;
        default:
            names = namesType01;
            fileNames = fileNamesType01;
            break;
        }

        const loadColor = color => zip(
            names.map(name => `${fix(color, true)}-${name}`),
            fileNames.map(name => `${fix(color)}_${name}.${fileType}`),
        )
            .map(texture => this.loadTexture({
                name: texture[0],
                url: url(baseUrl, texture[1]),
            }));

        const jobs = [].concat(colors).map(loadColor);

        return flattenDeep(jobs);
    }

    addTextureToCache(textureDescription) {
        this.cache[textureDescription.name] = textureDescription.texture;
        return textureDescription.name;
    }

    addModelToCache(model, name = null) {
        this.cache[name || model.name] = model;
        return model.name;
    }

    loadTexture(textureDescription, resolveBase = true) {
        return new Promise(resolve => {
            const finialize = (add, t) => {
                resolve(add ? this.addTextureToCache({
                    name: textureDescription.name,
                    texture: t,
                }) : 'ERROR');
            };

            const texture = new THREE.TextureLoader().load(
                resolveBase ? url(this.base, textureDescription.url) : textureDescription.url,
                t => finialize(true, t),
                undefined,
                t => finialize(false),
            );
        });
    }

    loadCubemap(name, {
        fileType = 'jpg',
        filenames = ['px', 'nx', 'py', 'ny', 'pz', 'nz'],
    } = { /**/}) {
        const baseUrl = url(this.base, 'textures/');

        const urls = filenames.map(face => url(url(baseUrl, `${name}/`), `${face}.${fileType}`));

        return new Promise(resolve => {
            const finialize = add => result => resolve(add ? this.addTextureToCache({
                name,
                texture,
            }) : 'ERROR');

            let texture = new THREE.CubeTextureLoader().load(
                urls,
                finialize(true),
                undefined,
                finialize(false),
            );
        });
    }

    loadPlane(planeDescription, resolveBase = true) {
        return new Promise(resolve => {
            const finialize = add => result => {
                const geo = new THREE.PlaneGeometry(100, 100, 32);
                const mat = new THREE.MeshBasicMaterial({ map: result });
                this.addModelToCache(new THREE.Mesh(geo, mat), planeDescription.name);
                // console.log("SPECIAL", planeDescription.name, this.cache[planeDescription.name]);
                resolve();
            };

            const texture = new THREE.TextureLoader().load(
                resolveBase ? url(this.base, planeDescription.url) : planeDescription.url,
                finialize(true),
                undefined,
                finialize(false),
            );
        });
    }

    loadModels(config) {
        const self = this;

        const manager = new THREE.LoadingManager();
        const objLoader = new OBJLoader(manager);
        const xhrLoader = new THREE.FileLoader(objLoader.manager);

        const createModel = (name, texture_name, model) => {
            model.castShadow = false;
            model.receiveShadow = false;
            model.name = name;
            model.traverse(child => {
                if (child instanceof THREE.Mesh) {
                    if (texture_name) {
                        child.material = new THREE.MeshBasicMaterial({
                            //	map: self.cache["basic_white-hori"].clone(),
                            transparent: true,
                            side: name === 'fdrawer' ? THREE.DoubleSide : THREE.FrontSide,
                        });

                        if (texture_name === 'cast_shadow') {
                            child.material.map = self.cache.cast_shadow;
                        }
                        if (texture_name === 'cast_shadow_wall') {
                            child.material.map = self.cache.cast_shadow;
                        }

                        if (texture_name === 'leg_texture') {
                            child.material.map = self.cache.leg_texture;
                        }
                    }
                }
            });

            return model;
        };

        // TODO CROSS ORIGIN NOT NEEDED FOR NOW
        // loader.setCrossOrigin(objLoader.crossOrigin);

        return new Promise(resolve => {
            xhrLoader.load('/r_static/ivy5.objx', modelsData => {
                const models = modelsData.split(TYLKO_BLADE_OF_SLAWEK);
                models.splice(0, 1);

                models.map(model => {
                    if (!model) return;

                    const filename = model.match(/[\w\d-]+\.obj/)[0];
                    const cfg = config[filename];

                    if (cfg) {
                        if (cfg[0] instanceof Array) {
                            for (let j = 0; j < cfg.length; j++) {
                                const currentModel = createModel(cfg[j][0], cfg[j][1], objLoader.parse(model));
                                this.addModelToCache(currentModel);
                            }
                        } else {
                            const currentModel = createModel(cfg[0], cfg[1], objLoader.parse(model));
                            this.addModelToCache(currentModel);
                        }
                    }
                });

                resolve();
            }, null, null);
        });
    }

    loadImage() {
        return new Promise(resolve => {
            const img = new Image();
            img.addEventListener('load', e => {
                resolve();
            });
        });
    }

    getElements() {
        return this.cache;
    }

    addOnLoadedEvent(mainCallback) {
        mainCallback();
    }
}

// eslint-disable-next-line import/prefer-default-export
export { AsyncLoader };
