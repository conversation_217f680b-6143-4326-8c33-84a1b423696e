{"version": 3, "sources": ["webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_objectToString.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_webgl/ivy/presets/bundle/presets.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_coreJsData.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_isMasked.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_baseSetToString.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_webgl/utils/loader/asyncLoader.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_webgl/ivy/modelConfig.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_webgl/ivy/designerRenderer.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_webgl/ivy/webdesigner.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/flattenDeep.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/three-obj-loader/dist/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_toSource.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/isFunction.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_isFlattenable.js", "webpack:///./node_modules/@babel/runtime/helpers/getPrototypeOf.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_baseRest.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_apply.js", "webpack:///./node_modules/@babel/runtime/helpers/assertThisInitialized.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_setToString.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/isArray.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/isObjectLike.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/isArrayLikeObject.js", "webpack:///./node_modules/@babel/runtime/helpers/setPrototypeOf.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_baseIsNative.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_shortOut.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/isObject.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/core-js/modules/es6.regexp.search.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_baseGetTag.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_Symbol.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_baseTimes.js", "webpack:///./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_overRest.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_getValue.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_freeGlobal.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/isLength.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_getRawTag.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_baseIsArguments.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/constant.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_webgl/ivy/decoder_old.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_webgl/ivy/build3d/buildFunctions.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_webgl/ivy/build3d.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_arrayFilter.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_baseProperty.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_defineProperty.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/identity.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_getNative.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/isArguments.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_arrayPush.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/isArrayLike.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_baseFlatten.js", "webpack:///./node_modules/@babel/runtime/helpers/inherits.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/core-js/modules/es6.regexp.match.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_arrayMap.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/zip.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/unzip.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/lodash/_root.js"], "names": ["objectProto", "Object", "prototype", "nativeObjectToString", "toString", "objectToString", "value", "call", "module", "exports", "_AwaitValue", "this", "wrapped", "_AsyncGenerator", "gen", "front", "back", "send", "key", "arg", "Promise", "resolve", "reject", "request", "next", "resume", "result", "wrappedAwait", "then", "settle", "done", "err", "type", "_invoke", "return", "undefined", "Symbol", "asyncIterator", "throw", "_defineProperty", "obj", "defineProperty", "enumerable", "configurable", "writable", "_objectSpread", "target", "i", "arguments", "length", "source", "ownKeys", "keys", "getOwnPropertySymbols", "concat", "filter", "sym", "getOwnPropertyDescriptor", "for<PERSON>ach", "data", "material", "id", "material_id", "color", "hex_color", "hex_handler_color", "backs_color", "opacity", "reflectivityValue", "reflectivityValueDoors", "shadow", "bakeShadow", "useSoftShadows", "position", "x", "y", "z", "cubemap", "textures", "dynamic", "foo", "bundle", "materialSettings", "shadowSettings", "lightPoint", "liveShadowEnabled", "settings$1", "material_id$1", "color$1", "materialSettings$1", "shadowSettings$1", "settings$2", "material_id$2", "color$2", "materialSettings$2", "shadowSettings$2", "settings$3", "material_id$3", "color$3", "materialSettings$3", "shadowSettings$3", "settings$4", "material_id$4", "color$4", "materialSettings$4", "shadowSettings$4", "settings$5", "material_id$5", "color$5", "materialSettings$5", "shadowSettings$5", "settings$6", "material_id$6", "color$6", "materialSettings$6", "shadowSettings$6", "settings$7", "material_id$7", "color$7", "materialSettings$7", "shadowSettings$7", "settings$8", "material_id$8", "color$8", "materialSettings$8", "shadowSettings$8", "settings$9", "material_id$9", "color$9", "materialSettings$9", "shadowSettings$9", "settings$10", "enabledMaterials", "enabledBasicMaterials", "getMaterialSettings", "materialId", "itemType", "m", "find", "o", "console", "warn", "data$1", "flags", "ao", "shadows", "log", "scenePresets", "getScenePresetsByColor", "_ref", "root", "__webpack_require__", "coreJsData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uid", "exec", "IE_PROTO", "isMasked", "func", "constant", "identity", "baseSetToString", "string", "OBJLoader", "THREE", "TYLKO_BLADE_OF_SLAWEK", "url", "a", "b", "URL", "href", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref$root", "_ref$maxFilesPerCycle", "maxFilesPerCycle", "classCallCheck_default", "elements", "cache", "base", "window", "origin", "colors", "_this", "_ref2", "_ref2$fileType", "fileType", "baseUrl", "fix", "name", "isName", "replace", "names", "fileNames", "loadColor", "zip", "map", "texture", "loadTexture", "jobs", "flattenDeep", "textureDescription", "model", "_this2", "resolveBase", "finialize", "add", "t", "addTextureToCache", "load", "_this3", "_ref3", "_ref3$fileType", "_ref3$filenames", "filenames", "urls", "face", "planeDescription", "_this4", "geo", "mat", "addModelToCache", "config", "_this5", "self", "manager", "ob<PERSON><PERSON><PERSON><PERSON>", "xhrLoader", "createModel", "texture_name", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "traverse", "child", "transparent", "side", "modelsData", "models", "split", "splice", "filename", "match", "cfg", "Array", "j", "currentModel", "parse", "img", "Image", "addEventListener", "e", "mainCallback", "prepare_preloader", "colorName", "preloader_config", "<PERSON><PERSON><PERSON><PERSON>", "loader", "required_build3d", "build3d", "Build3d", "getElements", "setDesignerMode", "init3d", "createFacePlaneForRaycasting", "setScene", "scene", "clearScene", "setBackgroundScene", "getPrice", "json", "getPriceConf", "override_color", "price_data", "factor_hvs_area", "factor_verticals_item", "factor_supports_item", "factor_horizontals_item", "factor_horizontals_row", "factor_backs_item", "factor_backs_area", "factor_doors_item", "factor_drawers_multiplier", "factor_margin_multiplier", "factor_hvs_mass", "factor_doors_mass", "factor_backs_mass", "factor_euro", "factor_material_multiplier", "calculatePrice", "points", "material_override", "width_override", "number_of_rows_override", "prices", "depth", "width", "rows", "horizontals", "verticals", "supports", "marza_x", "waga_kg", "min_", "max_", "Number", "Math", "atan", "toFixed", "min", "max", "get_hvs_area", "area", "abs", "y2", "y1", "x2", "x1", "pow", "total_price", "hvs_area", "backs", "wall_material_price", "reduce", "sum", "doors", "drawers", "d", "height", "drawers_price", "doors_weight", "backs_weight", "weight", "round", "ceil", "setColor", "shelf_type", "_getDefinitionForMate", "getDefinitionForMaterial", "parseInt", "_getDefinitionForMate2", "slicedToArray_default", "all", "loadColorTextures", "setMaterialColor", "skipTracking", "raycastComponentNo", "camera", "compList", "mousePoint", "raycaster", "Raycaster", "setFromCamera", "intersects", "intersectObjects", "object", "no", "displayShelf", "renderer", "timeout", "setShelfType", "z1", "z2", "rebuildWallsFromJson", "render", "getIndicatorBoxesPositions", "mode", "designer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Webdesigner<PERSON><PERSON><PERSON>", "build", "require", "cstm", "item", "shelfType", "initialColorName", "loadCubemap", "loadPlane", "loadModels", "ObjectsConfig", "baseFlatten", "INFINITY", "array", "defaultOnError", "Error", "DefaultLoadingManager", "materials", "regexp", "vertex_pattern", "normal_pattern", "uv_pattern", "face_vertex", "face_vertex_uv", "face_vertex_uv_normal", "face_vertex_normal", "object_pattern", "smoothing_pattern", "material_library_pattern", "material_use_pattern", "constructor", "onLoad", "onProgress", "onError", "scope", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "text", "setMaterials", "_createParserState", "state", "objects", "vertices", "normals", "uvs", "materialLibraries", "startObject", "fromDeclaration", "previousMaterial", "currentMaterial", "_finalize", "geometry", "smooth", "startMaterial", "libraries", "previous", "inherited", "groupCount", "index", "mtllib", "isArray", "groupStart", "groupEnd", "clone", "cloned", "bind", "push", "end", "lastMultiMaterial", "mi", "declared", "finalize", "parseVertexIndex", "len", "parseNormalIndex", "parseUVIndex", "addVertex", "c", "src", "dst", "addVertexLine", "addNormal", "addUV", "addUVLine", "addFace", "ua", "ub", "uc", "ud", "na", "nb", "nc", "nd", "vLen", "ia", "ib", "ic", "uvLen", "nLen", "addLineGeometry", "vi", "l", "uvi", "debug", "time", "indexOf", "lines", "line", "lineFirstChar", "lineSecondChar", "lineLength", "trimLeft", "trim", "char<PERSON>t", "parseFloat", "lineParts", "substring", "lineVertices", "lineUVs", "li", "llen", "parts", "substr", "test", "toLowerCase", "container", "Group", "isLine", "buffergeometry", "BufferGeometry", "addAttribute", "BufferAttribute", "Float32Array", "computeVertexNormals", "createdMaterials", "miLen", "sourceMaterial", "create", "LineBasicMaterial", "materialLine", "copy", "MeshPhongMaterial", "shading", "SmoothShading", "FlatShading", "mesh", "addGroup", "multiMaterial", "MultiMaterial", "<PERSON><PERSON>", "LineSegments", "timeEnd", "funcProto", "Function", "funcToString", "toSource", "baseGetTag", "isObject", "asyncTag", "funcTag", "genTag", "proxyTag", "isFunction", "tag", "isArguments", "spreadableSymbol", "isConcatSpreadable", "isFlattenable", "_getPrototypeOf", "setPrototypeOf", "getPrototypeOf", "__proto__", "overRest", "setToString", "baseRest", "start", "apply", "thisArg", "args", "_assertThisInitialized", "ReferenceError", "shortOut", "isObjectLike", "isArrayLike", "isArrayLikeObject", "_setPrototypeOf", "p", "reRegExpChar", "reIsHostCtor", "hasOwnProperty", "reIsNative", "RegExp", "baseIsNative", "pattern", "HOT_COUNT", "HOT_SPAN", "nativeNow", "Date", "now", "count", "lastCalled", "stamp", "remaining", "defined", "SEARCH", "$search", "search", "O", "fn", "String", "getRawTag", "nullTag", "undefinedTag", "symToStringTag", "toStringTag", "baseTimes", "n", "iteratee", "_typeof", "assertThisInitialized", "_possibleConstructorReturn", "nativeMax", "transform", "otherArgs", "getValue", "global", "freeGlobal", "MAX_SAFE_INTEGER", "<PERSON><PERSON><PERSON><PERSON>", "isOwn", "unmasked", "argsTag", "baseIsArguments", "preparePoints", "dnaTools", "dna<PERSON><PERSON>", "motion", "rowHeights", "rowStyles", "half_material", "support_size", "snapping", "shadow_settings", "gen_row_styles_for_buttons", "styles_slider", "backpanels_rows", "get_elements", "plankScaleMM", "MAX_DOOR_OPEN", "BuildFunctions", "createClass_default", "createSingleVertical", "oldPoints", "newPoints", "dif", "compare_dnas", "newitem", "scaled", "only_moved", "depth_previous", "same", "vertical", "walls", "distance", "bottom", "top", "verticalBox", "boundingBox", "setFromObject", "scale", "setX", "setY", "setZ", "rotation", "PI", "createSingleBack", "backBox", "scaleX", "scaleY", "size", "createSingleSupport", "supportType", "support", "supportBox", "scaleZ", "set", "createHorizontal", "horizontal", "horizontalBox", "createLegs", "leg", "legBox", "legs", "createDrawers", "drawer_description", "selected", "designer_mode", "door_group", "door_handler_width", "door_handler_height", "offset", "innerOffset", "drawer_cutout", "front_handling_size", "bottom_offset", "bottom_thickness", "bottom_depth", "back_height", "sides_length", "sides_height", "doorBox", "door", "drawer_handler", "needsUpdate", "center", "drawer_handler_shadow", "door_handler", "overscale", "magical_materials_for_rows", "sideA", "sideB", "updateMatrix", "front_offset_from_top", "front_height_offset", "front_thickness", "back_width_offset", "back_thickness", "back_height_a", "back_height_b", "back_height_c", "sides_thickness", "sides_height_a", "sides_height_b", "sides_height_c", "row_number", "getRowByY", "row_height", "renderOrder", "row_a", "row_b", "drawers_lists", "door_handler_depth", "handling_overlap", "createVerticalPanels", "wall", "leftRightWalls", "factor", "leftRightWall", "createAdditionalHorizontalPanels", "panel", "panelBox", "additionalHorizontalElements", "createHorizontalPanels", "topBottomWalls", "topBottomWall", "createDoor", "door_type", "door_flip", "handling_size", "handling_flip", "item_to_use", "rotate_sign", "door_lists", "door_groups", "door_handler_shadow", "createButton", "box_description", "raw", "sphere", "visible", "box", "scene3", "facePlane", "createComponentHoverBox", "scene2", "componentHoverBoxes", "createInsertAndPlynth", "value_y", "rowsList", "tmp", "row", "VERTEX_SHADER", "FRAGMENT_SHADER", "Box3", "plankScale", "shadowCastEnabled", "dnaStartWasSent", "sizePlane", "_points", "reflectionCube", "actualMaterial", "actualType", "elements_in", "sendCustomization", "context_in", "isMobile", "possibleConstructorReturn_default", "getPrototypeOf_default", "context", "send_customization", "backpanel_rows", "wallShadowsObjects", "row_c", "handlers", "castShadows", "boxes", "wallCompartmentShadow", "plinth", "depth_changed", "row_styles_presets", "empty_row_styles", "boxGeometry", "BoxGeometry", "boxMaterial", "MeshBasicMaterial", "wireframe", "spotGeometry", "spotMaterial", "ShaderMaterial", "vertexShader", "fragmentShader", "uniforms", "foreground", "bg", "blending_ratio", "blending_color", "Vector3", "Scene", "FrontSide", "envMap", "reflectivity", "Color", "polygonOffset", "polygonOffsetFactor", "polygonOffsetUnits", "depthTest", "depthWrite", "background", "additional_elements", "shadow_left", "shadow_middle", "shadow_right", "shadow_side", "remove", "children", "new_points", "getTopBottomWallPosition", "getLeftRightWallPosition", "getAdditionalHorizontalPanelPosition", "drawWalls", "returnObject", "Boolean", "ivy", "rebuildCounter", "getUrlParameter", "shadow_translator", "components", "_", "typical_elements", "element_type", "getSizeFromPoint", "getPositionFromPoint", "shadow_list", "shadow_type", "createShadows", "door_lists_elements", "flip", "constants", "rowHeight", "Object3D", "matrixWorldNeedsUpdate", "calculatedCapacity", "getTotalCapacity", "capacity", "compartmentCapacity", "row_styles_availability", "styles", "generateCanvas", "getHeight", "skip_animations", "PubSub", "publishSync", "publish", "topLeft", "tempMax", "tempMin", "topBottom", "minHorizontal", "maxHorizontal", "maxHeight", "minHeight", "minX", "maxX", "panelPoints", "_iteratorNormalCompletion", "_didIteratorError", "_iteratorError", "_iterator", "iterator", "_step", "localMinX", "localMaxX", "_ref$skipTracking", "loaclElements", "magicalElements", "_getDefinitionForMate3", "_getDefinitionForMate4", "dispose", "location", "compare_shadows", "isShadowIn", "cstm_i18n", "gridView", "configurator<PERSON><PERSON><PERSON>", "is_mobile_loaded", "is_tablet_loaded", "shadowIn", "shadowBox", "moved", "shelfSize", "delta", "shelfPosition", "toConsumableArray_default", "shdw", "w", "indent", "h", "half", "fixWarp", "left", "shadowCenter", "shadowLeft", "shadowRight", "positionBottom", "positionZ", "old_one", "new_one", "patterns", "initial", "skip_dna", "loadPresetFlag", "initialRun", "tracking", "trackDnaStart", "actual_dna_name", "generateCounter", "shelf_rows", "get_row_coor", "simplified_data", "generate_elements_simplified_data", "openings", "get_shadows_openings", "get_shadows_geometry", "generate_legs", "own_walls", "generateWalls", "Property1", "get_row_styles_list", "get_row_styles", "backpanel_styles", "createCastShadows", "sParam", "sPageURL", "decodeURIComponent", "sURLVariables", "sParameterName", "shelfGeometry", "rowAmount", "dnaID", "capacityTable", "300", "400", "500", "600", "700", "800", "900", "1000", "setTableType02", "0", "default", "subset_len", "param_1", "param_2", "param_3", "p_min", "p_max", "o_min", "o_max", "1", "2", "3", "setTable", "parameters", "allSorted", "el", "sort", "first", "second", "lengthAll", "shelfAmount", "floor", "allSum", "slice", "accumulator", "currentValue", "slicedCount", "sumAverage", "getCapacity", "k", "doorsAndDrawers", "adjustment", "roundTo", "checkIfOpeningsAreEqual", "maxDifferenceBetweenOpenings", "capMin", "capMax", "parameterWidth", "total", "totalMax", "multiplicity", "rounded", "totalMaxFlat", "arrayFilter", "predicate", "resIndex", "baseProperty", "getNative", "propertyIsEnumerable", "arrayPush", "values", "isStrict", "_inherits", "subClass", "superClass", "TypeError", "MATCH", "$match", "arrayMap", "unzip", "group", "freeSelf"], "mappings": "yFACA,IAAAA,EAAAC,OAAAC,UAOA,IAAAC,EAAAH,EAAAI,SASA,SAAAC,EAAAC,GACA,OAAAH,EAAAI,KAAAD,GAGAE,EAAAC,QAAAJ,mRCrBA,SAASK,EAAYJ,GACnBK,KAAKC,QAAUN,EAGjB,SAASO,EAAgBC,GACvB,IAAIC,EAAOC,EAEX,SAASC,EAAKC,EAAKC,GACjB,OAAO,IAAIC,QAAQ,SAAUC,EAASC,GACpC,IAAIC,EAAU,CACZL,IAAKA,EACLC,IAAKA,EACLE,QAASA,EACTC,OAAQA,EACRE,KAAM,MAGR,GAAIR,EAAM,CACRA,EAAOA,EAAKQ,KAAOD,MACd,CACLR,EAAQC,EAAOO,EACfE,EAAOP,EAAKC,MAKlB,SAASM,EAAOP,EAAKC,GACnB,IACE,IAAIO,EAASZ,EAAII,GAAKC,GACtB,IAAIb,EAAQoB,EAAOpB,MACnB,IAAIqB,EAAerB,aAAiBI,EACpCU,QAAQC,QAAQM,EAAerB,EAAMM,QAAUN,GAAOsB,KAAK,SAAUT,GACnE,GAAIQ,EAAc,CAChBF,EAAO,OAAQN,GACf,OAGFU,EAAOH,EAAOI,KAAO,SAAW,SAAUX,IACzC,SAAUY,GACXN,EAAO,QAASM,KAElB,MAAOA,GACPF,EAAO,QAASE,IAIpB,SAASF,EAAOG,EAAM1B,GACpB,OAAQ0B,GACN,IAAK,SACHjB,EAAMM,QAAQ,CACZf,MAAOA,EACPwB,KAAM,OAER,MAEF,IAAK,QACHf,EAAMO,OAAOhB,GACb,MAEF,QACES,EAAMM,QAAQ,CACZf,MAAOA,EACPwB,KAAM,QAER,MAGJf,EAAQA,EAAMS,KAEd,GAAIT,EAAO,CACTU,EAAOV,EAAMG,IAAKH,EAAMI,SACnB,CACLH,EAAO,MAIXL,KAAKsB,QAAUhB,EAEf,UAAWH,EAAIoB,SAAW,WAAY,CACpCvB,KAAKuB,OAASC,WAIlB,UAAWC,SAAW,YAAcA,OAAOC,cAAe,CACxDxB,EAAgBX,UAAUkC,OAAOC,eAAiB,WAChD,OAAO1B,MAIXE,EAAgBX,UAAUsB,KAAO,SAAUL,GACzC,OAAOR,KAAKsB,QAAQ,OAAQd,IAG9BN,EAAgBX,UAAUoC,MAAQ,SAAUnB,GAC1C,OAAOR,KAAKsB,QAAQ,QAASd,IAG/BN,EAAgBX,UAAUgC,OAAS,SAAUf,GAC3C,OAAOR,KAAKsB,QAAQ,SAAUd,IAGhC,SAASoB,EAAgBC,EAAKtB,EAAKZ,GACjC,GAAIY,KAAOsB,EAAK,CACdvC,OAAOwC,eAAeD,EAAKtB,EAAK,CAC9BZ,MAAOA,EACPoC,WAAY,KACZC,aAAc,KACdC,SAAU,WAEP,CACLJ,EAAItB,GAAOZ,EAGb,OAAOkC,EAGT,SAASK,EAAcC,GACrB,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CACzC,IAAIG,EAASF,UAAUD,IAAM,KAAOC,UAAUD,GAAK,GACnD,IAAII,EAAUlD,OAAOmD,KAAKF,GAE1B,UAAWjD,OAAOoD,wBAA0B,WAAY,CACtDF,EAAUA,EAAQG,OAAOrD,OAAOoD,sBAAsBH,GAAQK,OAAO,SAAUC,GAC7E,OAAOvD,OAAOwD,yBAAyBP,EAAQM,GAAKd,cAIxDS,EAAQO,QAAQ,SAAUxC,GACxBqB,EAAgBO,EAAQ5B,EAAKgC,EAAOhC,MAIxC,OAAO4B,EAGT,IAAIa,EAAO,CAAEC,SAAS,CAAEC,GAAG,EACvBC,YAAY,EACZC,MAAM,QACNC,UAAU,QACVC,kBAAkB,QAClBC,YAAY,QACZC,QAAQ,GACRC,kBAAkB,IAClBC,uBAAuB,IACzBC,OAAO,CAAEC,WAAW,MAClBC,eAAe,MACftB,OAAO,CAAEuB,SAAS,CAAE,CAAEC,EAAE,KACpB,CAAEC,EAAE,KACJ,CAAEC,EAAE,QACVC,QAAQ,KACRC,SAAS,MAEX,IAAIC,EAAU,CACZC,IAAK,SAASA,IACZ,MAAO,QAIX,IAAIC,EAASpC,EAAc,GAAIc,EAAKC,SAAUmB,GAG9C,IAAIjB,EAAc,EAClB,IAAIC,EAAQ,QACZ,IAAImB,EAAmB,CACrBpB,YAAaA,EACbC,MAAOA,EACPC,UAAW,SACXC,kBAAmB,SACnBC,YAAa,SACbC,QAAS,IACTC,kBAAmB,IACnBC,uBAAwB,KAE1B,IAAIc,EAAiB,CACnBC,WAAY,CACVV,EAAG,EACHC,EAAG,EACHC,EAAG,GAELS,kBAAmB,OAGrB,IAAIC,EAAazC,EAAc,CAC7BgB,GAAIC,GACHoB,EAAkBC,GAGrB,IAAII,EAAgB,EACpB,IAAIC,EAAU,OACd,IAAIC,EAAqB,CACvB3B,YAAayB,EACbxB,MAAOyB,EACPxB,UAAW,SACXC,kBAAmB,SACnBC,YAAa,QACbC,QAAS,IACTC,kBAAmB,IACnBC,uBAAwB,KAE1B,IAAIqB,EAAmB,CACrBN,WAAY,CACVV,EAAG,EACHC,EAAG,EACHC,EAAG,GAELS,kBAAmB,OAGrB,IAAIM,EAAa9C,EAAc,CAC7BgB,GAAI0B,GACHE,EAAoBC,GAGvB,IAAIE,EAAgB,EACpB,IAAIC,EAAU,MACd,IAAIC,EAAqB,CACvBhC,YAAa8B,EACb7B,MAAO8B,EACP7B,UAAW,QACXC,kBAAmB,MACnBC,YAAa,MACbC,QAAS,IACTC,kBAAmB,IACnBC,uBAAwB,IAE1B,IAAI0B,EAAmB,CACrBX,WAAY,CACVV,EAAG,EACHC,EAAG,EACHC,EAAG,GAELS,kBAAmB,OAGrB,IAAIW,EAAanD,EAAc,CAC7BgB,GAAI+B,GACHE,EAAoBC,GAGvB,IAAIE,EAAgB,EACpB,IAAIC,EAAU,QACd,IAAIC,EAAqB,CACvBrC,YAAamC,EACblC,MAAOmC,EACPlC,UAAW,QACXC,kBAAmB,QACnBC,YAAa,QACbC,QAAS,IACTC,kBAAmB,IACnBC,uBAAwB,KAE1B,IAAI+B,EAAmB,CACrBhB,WAAY,CACVV,EAAG,EACHC,EAAG,EACHC,EAAG,GAELS,kBAAmB,OAGrB,IAAIgB,EAAaxD,EAAc,CAC7BgB,GAAIoC,GACHE,EAAoBC,GAGvB,IAAIE,EAAgB,EACpB,IAAIC,EAAU,SACd,IAAIC,EAAqB,CACvB1C,YAAawC,EACbvC,MAAOwC,EACPvC,UAAW,QACXC,kBAAmB,QACnBC,YAAa,QACbC,QAAS,IACTC,kBAAmB,IACnBC,uBAAwB,KAE1B,IAAIoC,EAAmB,CACrBrB,WAAY,CACVV,EAAG,EACHC,EAAG,EACHC,EAAG,GAELS,kBAAmB,OAGrB,IAAIqB,EAAa7D,EAAc,CAC7BgB,GAAIyC,GACHE,EAAoBC,GAGvB,IAAIE,EAAgB,EACpB,IAAIC,EAAU,cACd,IAAIC,EAAqB,CACvB/C,YAAa6C,EACb5C,MAAO6C,EACP5C,UAAW,SACXC,kBAAmB,SACnBC,YAAa,SACbC,QAAS,EACTC,kBAAmB,EACnBC,uBAAwB,GAE1B,IAAIyC,EAAmB,CACrB1B,WAAY,CACVV,EAAG,EACHC,EAAG,EACHC,EAAG,GAELS,kBAAmB,OAGrB,IAAI0B,EAAalE,EAAc,CAC7BgB,GAAI8C,GACHE,EAAoBC,GAGvB,IAAIE,GAAgB,EACpB,IAAIC,GAAU,QACd,IAAIC,GAAqB,CACvBpD,YAAakD,GACbjD,MAAOkD,GACPjD,UAAW,SACXC,kBAAmB,SACnBC,YAAa,SACbC,QAAS,IACTC,kBAAmB,IACnBC,uBAAwB,KAE1B,IAAI8C,GAAmB,CACrB/B,WAAY,CACVV,EAAG,EACHC,EAAG,EACHC,EAAG,GAELS,kBAAmB,OAGrB,IAAI+B,GAAavE,EAAc,CAC7BgB,GAAImD,IACHE,GAAoBC,IAGvB,IAAIE,GAAgB,EACpB,IAAIC,GAAU,SACd,IAAIC,GAAqB,CACvBzD,YAAauD,GACbtD,MAAOuD,GACPtD,UAAW,QACXC,kBAAmB,QACnBC,YAAa,QACbC,QAAS,GACTC,kBAAmB,IACnBC,uBAAwB,KAE1B,IAAImD,GAAmB,CACrBpC,WAAY,CACVV,EAAG,EACHC,EAAG,EACHC,EAAG,GAELS,kBAAmB,OAGrB,IAAIoC,GAAa5E,EAAc,CAC7BgB,GAAIwD,IACHE,GAAoBC,IAGvB,IAAIE,GAAgB,EACpB,IAAIC,GAAU,OACd,IAAIC,GAAqB,CACvB9D,YAAa4D,GACb3D,MAAO4D,GACP3D,UAAW,SACXC,kBAAmB,SACnBC,YAAa,QACbC,QAAS,IACTC,kBAAmB,IACnBC,uBAAwB,KAE1B,IAAIwD,GAAmB,CACrBzC,WAAY,CACVV,EAAG,EACHC,EAAG,EACHC,EAAG,GAELS,kBAAmB,OAGrB,IAAIyC,GAAajF,EAAc,CAC7BgB,GAAI6D,IACHE,GAAoBC,IAGvB,IAAIE,GAAgB,EACpB,IAAIC,GAAU,SACd,IAAIC,GAAqB,CACvBnE,YAAaiE,GACbhE,MAAOiE,GACPhE,UAAW,SACXC,kBAAmB,SACnBC,YAAa,QACbC,QAAS,IACTC,kBAAmB,IACnBC,uBAAwB,KAE1B,IAAI6D,GAAmB,CACrB9C,WAAY,CACVV,EAAG,EACHC,EAAG,EACHC,EAAG,GAELS,kBAAmB,OAGrB,IAAI8C,GAActF,EAAc,CAC9BgB,GAAIkE,IACHE,GAAoBC,IAEvB,IAAIE,GAAmB,CAAC9C,EAAYL,EAAQoB,EAAYV,EAAYe,EAAYV,GAChF,IAAIqC,GAAwB,CAACtB,EAAYK,GAAYK,GAAYK,GAAYK,IAE7E,IAAIG,GAAsB,SAASA,EAAoBC,EAAYC,GACjE,IAAIC,EAEJ,OAAQD,GACN,KAAK,EACHC,EAAIL,GAAiBM,KAAK,SAAUC,GAClC,OAAOA,EAAE7E,aAAeyE,IAE1B,MAEF,KAAK,EACHE,EAAIJ,GAAsBK,KAAK,SAAUC,GACvC,OAAOA,EAAE7E,aAAeyE,IAE1B,MAEF,QACEE,EAAIL,GAAiBM,KAAK,SAAUC,GAClC,OAAOA,EAAE7E,aAAeyE,IAE1B,MAGJ,GAAIE,GAAKtG,UAAW,CAClByG,QAAQC,KAAK,2CAA4CN,EAAYC,GACrEC,EAAID,GAAY,EAAIH,GAAsBK,KAAK,SAAUC,GACvD,OAAOA,EAAE7E,aAAe,IACrBsE,GAAiBM,KAAK,SAAUC,GACnC,OAAOA,EAAE7E,aAAe,IAI5B,MAAO,CAAC2E,EAAE3E,YAAa2E,EAAE1E,MAAO0E,EAAEtE,QAASsE,EAAEzE,UAAWyE,EAAExE,kBAAmBwE,EAAEvE,YAAauE,EAAErE,kBAAmBqE,EAAEpE,yBAGrH,IAAIyE,GAAS,CAAEC,MAAM,CAAEC,GAAG,OACxBC,QAAQ,MAEVL,QAAQM,IAAIJ,IAEZ,IAAIK,GAAe,SAASC,EAAuBC,GACjD,OAAOP,4BChdT,IAAAQ,EAAWC,EAAQ,QAGnB,IAAAC,EAAAF,EAAA,sBAEA9I,EAAAC,QAAA+I,0BCLA,IAAAA,EAAiBD,EAAQ,QAGzB,IAAAE,EAAA,WACA,IAAAC,EAAA,SAAAC,KAAAH,KAAApG,MAAAoG,EAAApG,KAAAwG,UAAA,IACA,OAAAF,EAAA,iBAAAA,EAAA,GAFA,GAYA,SAAAG,EAAAC,GACA,QAAAL,QAAAK,EAGAtJ,EAAAC,QAAAoJ,0BCnBA,IAAAE,EAAeR,EAAQ,QACvB9G,EAAqB8G,EAAQ,QAC7BS,EAAeT,EAAQ,QAUvB,IAAAU,GAAAxH,EAAAuH,EAAA,SAAAF,EAAAI,GACA,OAAAzH,EAAAqH,EAAA,YACAnH,aAAA,KACAD,WAAA,MACApC,MAAAyJ,EAAAG,GACAtH,SAAA,QAIApC,EAAAC,QAAAwJ,sVCdAE,EAAUC,GAEV,IAAMC,EAAwB,UAC9B,IAAMC,EAAM,SAANA,EAAOC,EAAGC,GAAJ,OAAU,IAAIC,IAAID,EAAGD,GAAGG,UAE9BC,aAEL,SAAAA,IAGkB,IAAAtB,EAAArG,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GAAX,GAAW4H,EAAAvB,EAFjBC,OAEiBsB,SAAA,EAFV,iCAEUA,EAAAC,EAAAxB,EADjByB,mBACiBD,SAAA,EADE,EACFA,EAAAE,IAAApK,KAAAgK,GAEjBhK,KAAKyJ,MAAQA,EAEbzJ,KAAKqK,SAAW,GAChBrK,KAAKsK,MAAQ,GAEbtK,KAAKuK,KAAOZ,EAAIa,OAAOC,OAAQ9B,oDAGd+B,GAA0C,IAAAC,EAAA3K,KAAA,IAAA4K,EAAAvI,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GAAX,GAAWwI,EAAAD,EAAhCE,WAAgCD,SAAA,EAArB,MAAqBA,EAM3D,IAAIE,EAAUpB,EAAI3J,KAAKuK,KAAM,aAE7B,IAAIS,EAAM,SAANA,EAAOC,EAAMC,GAAP,OAAkBD,EAAKE,QAAQ,IAAKD,EAAS,IAAM,MAC7D,IAAIE,EAAQ,CAAC,OAAQ,OAAQ,UAAW,iBAAkB,aAC1D,IAAIC,EAAY,CAAC,aAAc,aAAc,UAAW,iBAAkB,cAE1E,IAAIC,EAAY,SAAZA,EAAalI,GAAD,OACfmI,IACCH,EAAMI,IAAI,SAACP,GAAD,SAAAtI,OAAaqI,EAAI5H,EAAO,MAAxB,KAAAT,OAAiCsI,KAC3CI,EAAUG,IAAI,SAACP,GAAD,SAAAtI,OAAaqI,EAAI5H,GAAjB,KAAAT,OAA2BsI,EAA3B,KAAAtI,OAAmCmI,MAChDU,IAAI,SAACC,GAAD,OAAad,EAAKe,YAAY,CACnCT,KAAMQ,EAAQ,GACd9B,IAAKA,EAAIoB,EAASU,EAAQ,SAI5B,IAAIE,EAAO,GAAGhJ,OAAO+H,GAAQc,IAAIF,GAEjC,OAAOM,IAAYD,+CAGFE,GACjB7L,KAAKsK,MAAMuB,EAAmBZ,MAAQY,EAAmBJ,QACzD,OAAOI,EAAmBZ,+CAGXa,GAAoB,IAAbb,EAAa5I,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GAAN,KAC7BrC,KAAKsK,MAAMW,EAAKA,EAAKa,EAAMb,MAAQa,EACnC,OAAOA,EAAMb,2CAGFY,GAAwC,IAAAE,EAAA/L,KAAA,IAApBgM,EAAoB3J,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GAAN,KAE7C,OAAO,IAAI5B,QAAQ,SAACC,GACnB,IAAIuL,EAAY,SAAZA,EAAaC,EAAKC,GACrBzL,EAAQwL,EAAMH,EAAKK,kBAAkB,CACpCnB,KAAMY,EAAmBZ,KACzBQ,QAASU,IACL,UAGN,IAAIV,GAAU,IAAIhC,oBAAsB4C,KACvCL,EAAcrC,EAAIoC,EAAKxB,KAAMsB,EAAmBlC,KAAOkC,EAAmBlC,IAC1E,SAACwC,GAAD,OAAOF,EAAU,KAAME,IACvB3K,UACA,SAAC2K,GAAD,OAAOF,EAAU,iDAKRhB,GAGE,IAAAqB,EAAAtM,KAAA,IAAAuM,EAAAlK,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GAAV,GAAUmK,EAAAD,EAFZzB,WAEY0B,SAAA,EAFD,MAECA,EAAAC,EAAAF,EADZG,YACYD,SAAA,EADA,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,MAC/BA,EAEb,IAAI1B,EAAUpB,EAAI3J,KAAKuK,KAAM,aAE7B,IAAIoC,EAAOD,EAAUlB,IAAI,SAACoB,GAAD,OAAUjD,EAAIA,EAAIoB,EAAD,GAAApI,OAAasI,EAAb,MAAJ,GAAAtI,OAA8BiK,EAA9B,KAAAjK,OAAsCmI,MAE5E,OAAO,IAAIrK,QAAQ,SAACC,GACnB,IAAIuL,EAAY,SAAZA,EAAaC,GAAD,OAAS,SAACnL,GAAD,OACxBL,EAAQwL,EAAMI,EAAKF,kBAAkB,CACpCnB,KAAMA,EACNQ,YACI,WAEN,IAAIA,GAAU,IAAIhC,wBAA0B4C,KAC3CM,EACAV,EAAU,MACVzK,UACAyK,EAAU,8CAKHY,GAAsC,IAAAC,EAAA9M,KAAA,IAApBgM,EAAoB3J,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GAAN,KAEzC,OAAO,IAAI5B,QAAQ,SAACC,GACnB,IAAIuL,EAAY,SAAZA,EAAaC,GAAD,OAAS,SAACnL,GACzB,IAAIgM,EAAM,IAAItD,mBAAqB,IAAK,IAAK,IAC7C,IAAIuD,EAAM,IAAIvD,uBAAwB,CAAE+B,IAAMzK,IAC9C+L,EAAKG,gBAAgB,IAAIxD,UAAWsD,EAAKC,GAAMH,EAAiB5B,MAEhEvK,MAGD,IAAI+K,GAAU,IAAIhC,oBAAsB4C,KACvCL,EAAcrC,EAAImD,EAAKvC,KAAMsC,EAAiBlD,KAAOkD,EAAiBlD,IACtEsC,EAAU,MACVzK,UACAyK,EAAU,+CAKFiB,GAAQ,IAAAC,EAAAnN,KAGlB,IAAIoN,EAAOpN,KAEX,IAAIqN,EAAU,IAAI5D,oBAClB,IAAI6D,EAAY,IAAI7D,eAAiB4D,GACrC,IAAIE,EAAY,IAAI9D,eAAiB6D,EAAUD,SAE/C,IAAIG,EAAc,SAAdA,EAAevC,EAAMwC,EAAc3B,GAGtCA,EAAM4B,WAAa,MACnB5B,EAAM6B,cAAgB,MACtB7B,EAAMb,KAAOA,EACba,EAAM8B,SAAW,SAAEC,GAClB,GAAKA,aAAiBpE,UAAa,CAElC,GAAGgE,EAAc,CAChBI,EAAM5K,SAAW,IAAIwG,uBAAwB,CAE5CqE,YAAa,KACbC,KAAMtE,eAGP,GAAGgE,GAAc,cAAe,CAC/BI,EAAM5K,SAASuI,IAAM4B,EAAK9C,MAAM,eAGjC,GAAGmD,GAAc,cAAe,CAC/BI,EAAM5K,SAASuI,IAAM4B,EAAK9C,MAAM,oBAQpC,OAAOwB,GAMR,OAAO,IAAIrL,QAAQ,SAACC,GAEnB6M,EAAUlB,KAAM,wBAAyB,SAAC2B,GAEzC,IAAIC,EAASD,EAAWE,MAAMxE,GAC9BuE,EAAOE,OAAO,EAAG,GAEjBF,EAAOzC,IAAI,SAACM,GACX,IAAIA,EAAO,OAEX,IAAIsC,EAAWtC,EAAMuC,MAAM,iBAAiB,GAC5C,IAAIC,EAAMpB,EAAOkB,GAEjB,GAAIE,EAAK,CACR,GAAIA,EAAI,aAAcC,MAAO,CAC5B,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAIhM,OAAQkM,IAAK,CACpC,IAAIC,EAAejB,EAAYc,EAAIE,GAAG,GAAIF,EAAIE,GAAG,GAAIlB,EAAUoB,MAAM5C,IACrEqB,EAAKF,gBAAgBwB,QAEhB,CACN,IAAIA,EAAejB,EAAYc,EAAI,GAAIA,EAAI,GAAIhB,EAAUoB,MAAM5C,IAC/DqB,EAAKF,gBAAgBwB,OAKxB/N,KAEE,KAAM,8CAKV,OAAO,IAAID,QAAQ,SAACC,GACnB,IAAIiO,EAAM,IAAIC,MACdD,EAAIE,iBAAiB,OAAQ,SAACC,GAC7BpO,gDAMF,OAAOV,KAAKsK,iDAGIyE,GAChBA,oBCzNF,IAAIC,EAAoB,SAApBA,EAA8BC,GAC9B,IAAIC,EAAmB,GAEvBA,EAAiB,uBAAyB,CAAC,WAAD,GAAAvM,OAAgBsM,EAAhB,UAC1CC,EAAiB,yBAA2B,CAAC,aAAD,GAAAvM,OAAkBsM,EAAlB,UAC5CC,EAAiB,8BAAgC,CAAC,kBAAD,GAAAvM,OAAuBsM,EAAvB,UACjDC,EAAiB,iBAAmB,CAAC,UAAD,GAAAvM,OAAesM,EAAf,aACpCC,EAAiB,wBAA0B,CAAC,iBAAD,GAAAvM,OAAsBsM,EAAtB,oBAC3CC,EAAiB,oBAAsB,CAAC,SAAD,GAAAvM,OAAcsM,EAAd,eACvCC,EAAiB,yBAA2B,CAAC,cAAD,GAAAvM,OAAmBsM,EAAnB,eAC5CC,EAAiB,0BAA4B,CAAC,eAAD,GAAAvM,OAAoBsM,EAApB,eAC7CC,EAAiB,oBAAsB,CAAC,aAAD,GAAAvM,OAAkBsM,EAAlB,UACvCC,EAAiB,oBAAsB,CAAC,aAAD,GAAAvM,OAAkBsM,EAAlB,UACvCC,EAAiB,cAAgB,CAAC,SAAD,GAAAvM,OAAcsM,EAAd,aAEjCC,EAAiB,2BAA6B,CAAC,qBAAsB,eACrEA,EAAiB,wBAA0B,CAAC,oBAAqB,eACjEA,EAAiB,yBAA2B,CAAC,mBAAoB,eAEjEA,EAAiB,aAAe,CAAC,OAAQ,aAAc,eACvDA,EAAiB,eAAiB,CAAC,eAAgB,cACnDA,EAAiB,WAAa,CAAC,MAAO,eACtCA,EAAiB,kBAAoB,CAAC,aAAc,aAAc,eAClEA,EAAiB,oBAAsB,CAAC,eAAgB,aAAc,eACtEA,EAAiB,qBAAuB,CAAC,gBAAiB,aAAc,eACxEA,EAAiB,yBAA2B,CAAC,oBAAqB,6BAClEA,EAAiB,gCAAkC,CAAC,2BAA4B,6BAEhF,OAAOA,mCC1BX,SAASC,EAAiBC,EAAQC,GAC9B,IAAIC,EAAU,IAAID,EAAiBE,QAAQH,EAAOI,cAAe,KAAM,KAAM,OAAQ,OAErFF,EAAQG,gBAAgB,GACxBH,EAAQI,SACRJ,EAAQK,+BAER,SAASC,EAASC,GACdP,EAAQM,SAASC,GAErB,SAASC,EAAWD,GAChBP,EAAQQ,WAAWD,GAEvB,SAASE,EAAmBF,GACxBP,EAAQS,mBAAmBF,GAE/B,SAASG,EAASC,GAEd,IAAIC,EAAe,SAAfA,IAA6C,IAArBC,EAAqB9N,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GAAN,KACvC,IAAI+N,EAAa,CAEbC,gBAAmB,MAAQ,MAAOC,sBAAyB,MAAOC,qBAAwB,MAC1FC,wBAA2B,GAAMC,uBAA0B,GAAK,IAEhEC,kBAAqB,GAAIC,kBAAqB,GAAK,KAEnDC,kBAAqB,GAAK,IAE1BC,0BAA6B,KAE7BC,yBAA4B,KAC5BC,gBAAmB,KAAMC,kBAAqB,GAAMC,kBAAqB,KAEzEC,YAAe,IACfC,2BAA8B,GAsBlC,OAAOf,GAGX,IAAIgB,EAAiB,SAAjBA,EAA0BC,EAAQC,EAAmBC,EAAgBC,GAErE,IAAIC,EAASvB,IACb,IAAIwB,EAAQ,IAEZ,IAAIzO,EAAWqO,EACf,IAAIK,EAAQJ,GAAkB,KAC9B,IAAIK,EAAOJ,GAA2B,EAEtC,IAAIK,EAAcR,EAAOQ,YACzB,IAAIC,EAAYT,EAAOS,UACvB,IAAIC,EAAWV,EAAOU,SAEtB,IAAIC,EAAU,SAAVA,EAAmBC,GAEiB,IADpClO,EACoC1B,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GADlC,OACkC,IADzB2B,EACyB3B,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GADvB,SACuB,IAApCwH,EAAoCxH,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GAAlC,GAAkC,IAA7ByM,EAA6BzM,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,IAA1B,IAA0B,IAApB6P,EAAoB7P,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GAAf,IAAe,IAAT8P,EAAS9P,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GAAJ,GAEhC,IAAIkI,EAAO6H,QAAQrO,GAAK,KAAOsO,KAAKC,KAAKtO,EAAIiO,EAAUpI,IAAMiF,GAAGyD,QAAQ,IACxE,OAAQ,EAAIF,KAAKG,IAAIH,KAAKI,IAAIlI,EAAM2H,GAAOC,IAE/C,IAAIO,EAAe,SAAfA,EAAwBb,EAAaC,EAAWC,EAAUL,GAE1D,IAAIiB,EAAO,EACX,IAAI,IAAIvQ,EAAE,EAAGA,EAAE0P,EAAUxP,OAAQF,GAAG,EAAG,CACnCuQ,GAAQN,KAAKO,IAAId,EAAU1P,GAAGyQ,GAAKf,EAAU1P,GAAG0Q,IAAMpB,EAE1D,IAAI,IAAItP,EAAE,EAAGA,EAAEyP,EAAYvP,OAAQF,GAAG,EAAG,CACrCuQ,GAAQN,KAAKO,IAAIf,EAAYzP,GAAG2Q,GAAKlB,EAAYzP,GAAG4Q,IAAMtB,EAE9D,IAAI,IAAItP,EAAE,EAAGA,EAAE2P,EAASzP,OAAQF,GAAG,EAAG,CAClCuQ,GAAQN,KAAKO,IAAIb,EAAS3P,GAAGyQ,GAAKd,EAAS3P,GAAG0Q,IAAMT,KAAKO,IAAIb,EAAS3P,GAAG2Q,GAAKhB,EAAS3P,GAAG4Q,IAG9F,OAAOL,EAAON,KAAKY,IAAI,GAAI,IAI/B,IAAIC,EAAc,EAClB,IAAIC,EAAWT,EAAab,EAAaC,EAAWC,EAAUL,GAE9DwB,GAAeC,EAAW1B,EAAOpB,gBAGjC6C,GAAepB,EAAUxP,OAASmP,EAAOnB,sBAEzC4C,GAAenB,EAASzP,OAASmP,EAAOlB,qBAIxC,GAAIoB,EAAQ,KAAK,CACbuB,IAAgBtB,EAAK,GAAKH,EAAOhB,uBACjCyC,GAAerB,EAAYvP,OAASmP,EAAOjB,wBAA0B,MAEpE,CACD0C,GAAerB,EAAYvP,OAASmP,EAAOjB,wBAG/C,GAAIa,EAAO+B,MAAM9Q,OAAS,EAAE,CACxB4Q,GAAe7B,EAAO+B,MAAM9Q,OAASmP,EAAOf,kBAC5C,IAAI2C,EAAuBhC,EAAO+B,MAAM5H,IAAI,SAAA3B,GAAC,OAAEwI,KAAKO,KAAK/I,EAAE,MAAQA,EAAE,QAAUA,EAAE,MAAQA,EAAE,UAASyJ,OAAO,SAACC,EAAK5T,GAAN,OAAc4T,EAAK5T,GAAO,GAAK,IAAO,IAAQ8R,EAAOd,kBAChKuC,GAAeG,EAGnBH,GAAe7B,EAAOmC,MAAMlR,OAASmP,EAAOb,kBAI5CS,EAAOoC,QAAQjI,IAAI,SAAAkI,GACf,IAAI/B,EAAQU,KAAKO,IAAIc,EAAE,MAAQA,EAAE,OACjC,IAAIC,EAAStB,KAAKO,IAAIc,EAAE,MAAQA,EAAE,OAClC,IAAIE,IAAkBjC,EAAQ,IAAM,IAAM,KAAOU,KAAKY,IAAItB,EAAM,GAAK,IAAU,IAAKA,EAAQ,KAAOgC,EAAS,IAAM,EAAKA,EAAS,IAAM,KAAO,MAC7IC,GAAiBnC,EAAOZ,0BACxBqC,GAAeU,IAInBV,GAAezB,EAAOX,yBAGtB,IAAI+C,EAAgBxC,EAAOmC,MAAMhI,IAAI,SAAA3B,GAAC,OAAEwI,KAAKO,KAAK/I,EAAE,MAAQA,EAAE,QAAUA,EAAE,MAAQA,EAAE,UAASyJ,OAAO,SAACC,EAAK5T,GAAN,OAAc4T,EAAK5T,GAAO,GAAK,IAAO,IAAQ8R,EAAOT,kBACzJ,IAAI8C,EAAgBzC,EAAO+B,MAAM5H,IAAI,SAAA3B,GAAC,OAAEwI,KAAKO,KAAK/I,EAAE,MAAQA,EAAE,QAAUA,EAAE,MAAQA,EAAE,UAASyJ,OAAO,SAACC,EAAK5T,GAAN,OAAc4T,EAAK5T,GAAO,GAAK,IAAO,IAAQ8R,EAAOR,kBACzJ,IAAI8C,GAAUtC,EAAOV,gBAAkBoC,EAAWU,EAAeC,GAAcvB,QAAQ,GAEvFW,GAAelB,EAAQ+B,GAEvB,GAAItC,EAAON,4BAA8B,EAAE,MAEpC,CACH+B,GAAgB,EAAIzB,EAAON,2BAG/B+B,GAAezB,EAAOP,YAEtB,OAAOmB,KAAK2B,MAAM3B,KAAK4B,KAAKf,EAAY,QAE5C,OAAO9B,EAAenB,EAAM,GAGhC,SAASiE,EAAS9Q,EAAO+Q,GAAW,IAAAC,EACXC,eAAyBC,SAASlR,GAAQkR,SAASH,IADxCI,EAAAC,IAAAJ,EAAA,GACxBnF,EADwBsF,EAAA,GAGhC,OAAO9T,QAAQgU,IAAIrF,EAAOsF,kBAAkBzF,IAAYhO,KAAK,WACzDqO,EAAQqF,iBAAiB,CAAEvR,QAAO+Q,aAAYS,aAAa,SAInE,SAASC,EAAmBC,EAAQC,EAAUC,GAC1C,IAAIC,EAAY,IAAIxL,MAAMyL,UAC1BD,EAAUE,cAAeH,EAAYF,GACrC,IAAIM,EAAaH,EAAUI,iBAAkBN,GAC7C9M,QAAQM,IAAI,OAAO6M,GACnB,GAAGA,EAAW9S,OAAO,EAAG,CACpB,OAAO8S,EAAW,GAAGE,OAAOC,OACzB,CACH,OAAO,OAIf,SAASC,EAAavF,EAAM7M,EAAOyM,EAAOiF,EAAQW,GAE9C,IAAIC,EAEJ,GAAG,KAAM,EAsDTpG,EAAQM,SAASC,GAEjBP,EAAQqG,aAAavS,EAAM8K,MAAM,KAAK,IAEtC,GAAG+B,EAAK4B,YAAavC,EAAQoC,MAAQW,KAAKO,IAAI3C,EAAK4B,YAAY,GAAG+D,GAAK3F,EAAK4B,YAAY,GAAGgE,IAE3F3B,EAAS9Q,EAAM8K,MAAM,KAAK,GAAI9K,EAAM8K,MAAM,KAAK,IAE/CoB,EAAQwG,qBAAqB7F,EAAMJ,GAEnC4F,EAASM,OAAOlG,EAAOiF,GAG3B,SAASkB,IACL,OAAO1G,EAAQ0G,6BAGnB,SAASvG,EAAgBwG,GACrB3G,EAAQG,gBAAgBwG,GAE5BzL,OAAO0L,uBAAyBlW,KAEhC,MAAO,CACHgW,6BACAlG,WAAYA,EACZ0F,aAAcA,EACd/F,gBAAiBA,EACjBG,SAAUA,EACVsE,SAAUA,EACVnE,mBAAoBA,EACpBC,SAAUA,mCCnQlB,IAAIZ,EAEG,SAAS+G,IAEd/G,EAAS,IAAIpF,EACb,IAAIoM,EAAQC,EAAQ,QAEpB,CAAE,CAAC,cAAe,oBAChB,CAAC,cAAe,WAChB,CAAC,aAAc,kBACf,CAAC,mBAAoB,wBACrB,CAAC,cAAe,mBAChB,CAAC,4BAA6B,yBAC9B,CAAC,WAAY,qBACb,CAAC,YAAa,sBACd,CAAC,YAAa,uBACd7K,IAAI,SAACC,GACL2D,EAAO1D,YAAY,CAAET,KAAMQ,EAAQ,GAAI9B,IAAG,YAAAhH,OAAc8I,EAAQ,KAAQ,QAftC,IAAA/C,EAkBsB8B,OAAO8L,KAAO9L,OAAO8L,KAAKC,KAAO,CAACtT,SAAS,EAAEkR,WAAW,GAAjGvM,EAlBmBc,EAkB9BzF,SAAoCuT,EAlBN9N,EAkBPyL,WAlBO,IAAAC,EAmBPC,eAAyBzM,EAAY4O,GAnB9BjC,EAAAC,IAAAJ,EAAA,GAmB5BqC,EAnB4BlC,EAAA,GAqBpC,OAAO9T,QAAQgU,IAAI7I,IAAY,CAC7BwD,EAAOsH,YAAY,WACnBtH,EAAOuH,UAAU,CACf1L,KAAM,0BACNtB,IAAK,6BAEPlJ,QAAQgU,IAAI7I,IAAY,CAAC,CAAC,cAAe,QAAS,SAAU,OAAQ,SAAS,MAAM,OAAO,QAAS,SAAS,SAAUJ,IAAI,SAAAzH,GAAC,OAAGqL,EAAOsF,kBAAkB3Q,WACrJ9C,KAAK,WACP,OAAOmO,EAAOwH,WAAWC,EAAcJ,IAAmBxV,KAAK,WAC7D,OAAOkO,EAAiBC,EAAQgH,gCCzCtC,IAAAU,EAAkBlO,EAAQ,QAG1B,IAAAmO,EAAA,IAgBA,SAAAnL,EAAAoL,GACA,IAAA1U,EAAA0U,GAAA,OAAAA,EAAA1U,OACA,OAAAA,EAAAwU,EAAAE,EAAAD,GAAA,GAGAlX,EAAAC,QAAA8L,uCCtBA,SAAAqL,EAAA7V,GACA,UAAA8V,MAAA9V,GAGAvB,EAAAC,QAAA,SAAA2J,GAMAA,EAAAD,UAAA,SAAA6D,GAEArN,KAAAqN,YAAA7L,UAAA6L,EAAA5D,EAAA0N,sBAEAnX,KAAAoX,UAAA,KAEApX,KAAAqX,OAAA,CAEAC,eAAA,0EAEAC,eAAA,2EAEAC,WAAA,oDAEAC,YAAA,kDAEAC,eAAA,sFAEAC,sBAAA,0HAEAC,mBAAA,8FAEAC,eAAA,gBAEAC,kBAAA,oBAEAC,yBAAA,WAEAC,qBAAA,aAIAvO,EAAAD,UAAAjK,UAAA,CAEA0Y,YAAAxO,EAAAD,UAEA6C,KAAA,SAAAA,EAAA1C,EAAAuO,EAAAC,EAAAC,GAEA,IAAAC,EAAArY,KACAA,KAAAoY,WAAAnB,EAEA,IAAA7H,EAAA,IAAA3F,EAAA6O,WAAAD,EAAAhL,SACA+B,EAAAmJ,QAAAvY,KAAAwY,MACApJ,EAAA/C,KAAA1C,EAAA,SAAA8O,GAEAP,EAAAG,EAAA3J,MAAA+J,KACON,EAAAC,IAGPG,QAAA,SAAAA,EAAA5Y,GAEAK,KAAAwY,KAAA7Y,GAGA+Y,aAAA,SAAAA,EAAAtB,GAEApX,KAAAoX,aAGAuB,mBAAA,SAAAA,IAEA,IAAAC,EAAA,CACAC,QAAA,GACAvD,OAAA,GAEAwD,SAAA,GACAC,QAAA,GACAC,IAAA,GAEAC,kBAAA,GAEAC,YAAA,SAAAA,EAAAjO,EAAAkO,GAIA,GAAAnZ,KAAAsV,QAAAtV,KAAAsV,OAAA6D,kBAAA,OAEAnZ,KAAAsV,OAAArK,OACAjL,KAAAsV,OAAA6D,oBAAA,MACA,OAGA,IAAAC,EAAApZ,KAAAsV,eAAAtV,KAAAsV,OAAA+D,kBAAA,WAAArZ,KAAAsV,OAAA+D,kBAAA7X,UAEA,GAAAxB,KAAAsV,eAAAtV,KAAAsV,OAAAgE,YAAA,YAEAtZ,KAAAsV,OAAAgE,UAAA,MAGAtZ,KAAAsV,OAAA,CACArK,QAAA,GACAkO,oBAAA,MAEAI,SAAA,CACAT,SAAA,GACAC,QAAA,GACAC,IAAA,IAEA5B,UAAA,GACAoC,OAAA,KAEAC,cAAA,SAAAA,EAAAxO,EAAAyO,GAEA,IAAAC,EAAA3Z,KAAAsZ,UAAA,OAIA,GAAAK,MAAAC,WAAAD,EAAAE,YAAA,IAEA7Z,KAAAoX,UAAAjJ,OAAAwL,EAAAG,MAAA,GAGA,IAAA7W,EAAA,CACA6W,MAAA9Z,KAAAoX,UAAA9U,OACA2I,QAAA,GACA8O,OAAAxL,MAAAyL,QAAAN,MAAApX,OAAA,EAAAoX,IAAApX,OAAA,MACAkX,OAAAG,IAAAnY,UAAAmY,EAAAH,OAAAxZ,KAAAwZ,OACAS,WAAAN,IAAAnY,UAAAmY,EAAAO,SAAA,EACAA,UAAA,EACAL,YAAA,EACAD,UAAA,MAEAO,MAAA,SAAAA,EAAAL,GACA,IAAAM,EAAA,CACAN,iBAAA,SAAAA,EAAA9Z,KAAA8Z,MACA7O,KAAAjL,KAAAiL,KACA8O,OAAA/Z,KAAA+Z,OACAP,OAAAxZ,KAAAwZ,OACAS,WAAA,EACAC,UAAA,EACAL,YAAA,EACAD,UAAA,OAEAQ,EAAAD,MAAAna,KAAAma,MAAAE,KAAAD,GACA,OAAAA,IAIApa,KAAAoX,UAAAkD,KAAArX,GAEA,OAAAA,GAGAoW,gBAAA,SAAAA,IAEA,GAAArZ,KAAAoX,UAAA9U,OAAA,GACA,OAAAtC,KAAAoX,UAAApX,KAAAoX,UAAA9U,OAAA,GAGA,OAAAd,WAGA8X,UAAA,SAAAA,EAAAiB,GAEA,IAAAC,EAAAxa,KAAAqZ,kBACA,GAAAmB,KAAAN,YAAA,GAEAM,EAAAN,SAAAla,KAAAuZ,SAAAT,SAAAxW,OAAA,EACAkY,EAAAX,WAAAW,EAAAN,SAAAM,EAAAP,WACAO,EAAAZ,UAAA,MAIA,GAAAW,GAAAva,KAAAoX,UAAA9U,OAAA,GAEA,QAAAmY,EAAAza,KAAAoX,UAAA9U,OAAA,EAAwDmY,GAAA,EAASA,IAAA,CACjE,GAAAza,KAAAoX,UAAAqD,GAAAZ,YAAA,GACA7Z,KAAAoX,UAAAjJ,OAAAsM,EAAA,KAMA,GAAAF,GAAAva,KAAAoX,UAAA9U,SAAA,GAEAtC,KAAAoX,UAAAkD,KAAA,CACArP,KAAA,GACAuO,OAAAxZ,KAAAwZ,SAIA,OAAAgB,IAUA,GAAApB,KAAAnO,aAAAmO,EAAAe,QAAA,YAEA,IAAAO,EAAAtB,EAAAe,MAAA,GACAO,EAAAd,UAAA,KACA5Z,KAAAsV,OAAA8B,UAAAkD,KAAAI,GAGA1a,KAAA6Y,QAAAyB,KAAAta,KAAAsV,SAGAqF,SAAA,SAAAA,IAEA,GAAA3a,KAAAsV,eAAAtV,KAAAsV,OAAAgE,YAAA,YAEAtZ,KAAAsV,OAAAgE,UAAA,QAIAsB,iBAAA,SAAAA,EAAAjb,EAAAkb,GAEA,IAAAf,EAAAxF,SAAA3U,EAAA,IACA,OAAAma,GAAA,EAAAA,EAAA,EAAAA,EAAAe,EAAA,MAGAC,iBAAA,SAAAA,EAAAnb,EAAAkb,GAEA,IAAAf,EAAAxF,SAAA3U,EAAA,IACA,OAAAma,GAAA,EAAAA,EAAA,EAAAA,EAAAe,EAAA,MAGAE,aAAA,SAAAA,EAAApb,EAAAkb,GAEA,IAAAf,EAAAxF,SAAA3U,EAAA,IACA,OAAAma,GAAA,EAAAA,EAAA,EAAAA,EAAAe,EAAA,MAGAG,UAAA,SAAAA,EAAApR,EAAAC,EAAAoR,GAEA,IAAAC,EAAAlb,KAAA8Y,SACA,IAAAqC,EAAAnb,KAAAsV,OAAAiE,SAAAT,SAEAqC,EAAAb,KAAAY,EAAAtR,EAAA,IACAuR,EAAAb,KAAAY,EAAAtR,EAAA,IACAuR,EAAAb,KAAAY,EAAAtR,EAAA,IACAuR,EAAAb,KAAAY,EAAArR,EAAA,IACAsR,EAAAb,KAAAY,EAAArR,EAAA,IACAsR,EAAAb,KAAAY,EAAArR,EAAA,IACAsR,EAAAb,KAAAY,EAAAD,EAAA,IACAE,EAAAb,KAAAY,EAAAD,EAAA,IACAE,EAAAb,KAAAY,EAAAD,EAAA,KAGAG,cAAA,SAAAA,EAAAxR,GAEA,IAAAsR,EAAAlb,KAAA8Y,SACA,IAAAqC,EAAAnb,KAAAsV,OAAAiE,SAAAT,SAEAqC,EAAAb,KAAAY,EAAAtR,EAAA,IACAuR,EAAAb,KAAAY,EAAAtR,EAAA,IACAuR,EAAAb,KAAAY,EAAAtR,EAAA,KAGAyR,UAAA,SAAAA,EAAAzR,EAAAC,EAAAoR,GAEA,IAAAC,EAAAlb,KAAA+Y,QACA,IAAAoC,EAAAnb,KAAAsV,OAAAiE,SAAAR,QAEAoC,EAAAb,KAAAY,EAAAtR,EAAA,IACAuR,EAAAb,KAAAY,EAAAtR,EAAA,IACAuR,EAAAb,KAAAY,EAAAtR,EAAA,IACAuR,EAAAb,KAAAY,EAAArR,EAAA,IACAsR,EAAAb,KAAAY,EAAArR,EAAA,IACAsR,EAAAb,KAAAY,EAAArR,EAAA,IACAsR,EAAAb,KAAAY,EAAAD,EAAA,IACAE,EAAAb,KAAAY,EAAAD,EAAA,IACAE,EAAAb,KAAAY,EAAAD,EAAA,KAGAK,MAAA,SAAAA,EAAA1R,EAAAC,EAAAoR,GAEA,IAAAC,EAAAlb,KAAAgZ,IACA,IAAAmC,EAAAnb,KAAAsV,OAAAiE,SAAAP,IAEAmC,EAAAb,KAAAY,EAAAtR,EAAA,IACAuR,EAAAb,KAAAY,EAAAtR,EAAA,IACAuR,EAAAb,KAAAY,EAAArR,EAAA,IACAsR,EAAAb,KAAAY,EAAArR,EAAA,IACAsR,EAAAb,KAAAY,EAAAD,EAAA,IACAE,EAAAb,KAAAY,EAAAD,EAAA,KAGAM,UAAA,SAAAA,EAAA3R,GAEA,IAAAsR,EAAAlb,KAAAgZ,IACA,IAAAmC,EAAAnb,KAAAsV,OAAAiE,SAAAP,IAEAmC,EAAAb,KAAAY,EAAAtR,EAAA,IACAuR,EAAAb,KAAAY,EAAAtR,EAAA,KAGA4R,QAAA,SAAAA,EAAA5R,EAAAC,EAAAoR,EAAAvH,EAAA+H,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAEA,IAAAC,EAAAjc,KAAA8Y,SAAAxW,OAEA,IAAA4Z,EAAAlc,KAAA4a,iBAAAhR,EAAAqS,GACA,IAAAE,EAAAnc,KAAA4a,iBAAA/Q,EAAAoS,GACA,IAAAG,EAAApc,KAAA4a,iBAAAK,EAAAgB,GACA,IAAA/Y,EAEA,GAAAwQ,IAAAlS,UAAA,CAEAxB,KAAAgb,UAAAkB,EAAAC,EAAAC,OACW,CAEXlZ,EAAAlD,KAAA4a,iBAAAlH,EAAAuI,GAEAjc,KAAAgb,UAAAkB,EAAAC,EAAAjZ,GACAlD,KAAAgb,UAAAmB,EAAAC,EAAAlZ,GAGA,GAAAuY,IAAAja,UAAA,CAEA,IAAA6a,EAAArc,KAAAgZ,IAAA1W,OAEA4Z,EAAAlc,KAAA+a,aAAAU,EAAAY,GACAF,EAAAnc,KAAA+a,aAAAW,EAAAW,GACAD,EAAApc,KAAA+a,aAAAY,EAAAU,GAEA,GAAA3I,IAAAlS,UAAA,CAEAxB,KAAAsb,MAAAY,EAAAC,EAAAC,OACa,CAEblZ,EAAAlD,KAAA+a,aAAAa,EAAAS,GAEArc,KAAAsb,MAAAY,EAAAC,EAAAjZ,GACAlD,KAAAsb,MAAAa,EAAAC,EAAAlZ,IAIA,GAAA2Y,IAAAra,UAAA,CAGA,IAAA8a,EAAAtc,KAAA+Y,QAAAzW,OACA4Z,EAAAlc,KAAA8a,iBAAAe,EAAAS,GAEAH,EAAAN,IAAAC,EAAAI,EAAAlc,KAAA8a,iBAAAgB,EAAAQ,GACAF,EAAAP,IAAAE,EAAAG,EAAAlc,KAAA8a,iBAAAiB,EAAAO,GAEA,GAAA5I,IAAAlS,UAAA,CAEAxB,KAAAqb,UAAAa,EAAAC,EAAAC,OACa,CAEblZ,EAAAlD,KAAA8a,iBAAAkB,EAAAM,GAEAtc,KAAAqb,UAAAa,EAAAC,EAAAjZ,GACAlD,KAAAqb,UAAAc,EAAAC,EAAAlZ,MAKAqZ,gBAAA,SAAAA,EAAAzD,EAAAE,GAEAhZ,KAAAsV,OAAAiE,SAAAlY,KAAA,OAEA,IAAA4a,EAAAjc,KAAA8Y,SAAAxW,OACA,IAAA+Z,EAAArc,KAAAgZ,IAAA1W,OAEA,QAAAka,EAAA,EAAAC,EAAA3D,EAAAxW,OAA+Cka,EAAAC,EAAQD,IAAA,CAEvDxc,KAAAob,cAAApb,KAAA4a,iBAAA9B,EAAA0D,GAAAP,IAGA,QAAAS,EAAA,EAAAD,EAAAzD,EAAA1W,OAA2Coa,EAAAD,EAASC,IAAA,CAEpD1c,KAAAub,UAAAvb,KAAA+a,aAAA/B,EAAA0D,GAAAL,OAMAzD,EAAAM,YAAA,UAEA,OAAAN,GAGAlK,MAAA,SAAAA,EAAA+J,EAAAkE,GACA,UAAAA,IAAA,aACAA,EAAA,KAGA,GAAAA,EAAA,CACA1U,QAAA2U,KAAA,aAGA,IAAAhE,EAAA5Y,KAAA2Y,qBAEA,GAAAF,EAAAoE,QAAA,cAGApE,IAAAtN,QAAA,cAGA,GAAAsN,EAAAoE,QAAA,cAGApE,IAAAtN,QAAA,YAGA,IAAA2R,EAAArE,EAAAvK,MAAA,MACA,IAAA6O,EAAA,GACAC,EAAA,GACAC,EAAA,GACA,IAAAC,EAAA,EACA,IAAAnc,EAAA,GAGA,IAAAoc,QAAA,GAAAA,WAAA,WAEA,QAAA/a,EAAA,EAAAqa,EAAAK,EAAAxa,OAAuCF,EAAAqa,EAAOra,IAAA,CAE9C2a,EAAAD,EAAA1a,GAEA2a,EAAAI,EAAAJ,EAAAI,WAAAJ,EAAAK,OAEAF,EAAAH,EAAAza,OAEA,GAAA4a,IAAA,WAEAF,EAAAD,EAAAM,OAAA,GAGA,GAAAL,IAAA,aAEA,GAAAA,IAAA,KAEAC,EAAAF,EAAAM,OAAA,GAEA,GAAAJ,IAAA,MAAAlc,EAAAf,KAAAqX,OAAAC,eAAAtO,KAAA+T,MAAA,MAKAnE,EAAAE,SAAAwB,KAAAgD,WAAAvc,EAAA,IAAAuc,WAAAvc,EAAA,IAAAuc,WAAAvc,EAAA,UACW,GAAAkc,IAAA,MAAAlc,EAAAf,KAAAqX,OAAAE,eAAAvO,KAAA+T,MAAA,MAKXnE,EAAAG,QAAAuB,KAAAgD,WAAAvc,EAAA,IAAAuc,WAAAvc,EAAA,IAAAuc,WAAAvc,EAAA,UACW,GAAAkc,IAAA,MAAAlc,EAAAf,KAAAqX,OAAAG,WAAAxO,KAAA+T,MAAA,MAKXnE,EAAAI,IAAAsB,KAAAgD,WAAAvc,EAAA,IAAAuc,WAAAvc,EAAA,SACW,CAEXf,KAAAoY,QAAA,sCAAA2E,EAAA,WAES,GAAAC,IAAA,KAET,IAAAjc,EAAAf,KAAAqX,OAAAM,sBAAA3O,KAAA+T,MAAA,MAMAnE,EAAA4C,QAAAza,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,IAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,IAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,UACW,IAAAA,EAAAf,KAAAqX,OAAAK,eAAA1O,KAAA+T,MAAA,MAMXnE,EAAA4C,QAAAza,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,SACW,IAAAA,EAAAf,KAAAqX,OAAAO,mBAAA5O,KAAA+T,MAAA,MAMXnE,EAAA4C,QAAAza,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAS,wCAAAT,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,SACW,IAAAA,EAAAf,KAAAqX,OAAAI,YAAAzO,KAAA+T,MAAA,MAMXnE,EAAA4C,QAAAza,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,QACW,CAEXf,KAAAoY,QAAA,0BAAA2E,EAAA,WAES,GAAAC,IAAA,KAET,IAAAO,EAAAR,EAAAS,UAAA,GAAAJ,OAAAlP,MAAA,KACA,IAAAuP,EAAA,GACAC,EAAA,GAEA,GAAAX,EAAAF,QAAA,WAEAY,EAAAF,MACW,CAEX,QAAAI,EAAA,EAAAC,EAAAL,EAAAjb,OAAqDqb,EAAAC,EAAWD,IAAA,CAEhE,IAAAE,EAAAN,EAAAI,GAAAzP,MAAA,KAEA,GAAA2P,EAAA,QAAAJ,EAAAnD,KAAAuD,EAAA,IACA,GAAAA,EAAA,QAAAH,EAAApD,KAAAuD,EAAA,KAGAjF,EAAA2D,gBAAAkB,EAAAC,QACS,IAAA3c,EAAAf,KAAAqX,OAAAQ,eAAA7O,KAAA+T,MAAA,MAQT,IAAA9R,GAAA,IAAAlK,EAAA,GAAA+c,OAAA,GAAAV,QAAAU,OAAA,GAEAlF,EAAAM,YAAAjO,QACS,GAAAjL,KAAAqX,OAAAW,qBAAA+F,KAAAhB,GAAA,CAITnE,EAAAtD,OAAAmE,cAAAsD,EAAAS,UAAA,GAAAJ,OAAAxE,EAAAK,wBACS,GAAAjZ,KAAAqX,OAAAU,yBAAAgG,KAAAhB,GAAA,CAITnE,EAAAK,kBAAAqB,KAAAyC,EAAAS,UAAA,GAAAJ,aACS,IAAArc,EAAAf,KAAAqX,OAAAS,kBAAA9O,KAAA+T,MAAA,MAWT,IAAApd,EAAAoB,EAAA,GAAAqc,OAAAY,cACApF,EAAAtD,OAAAkE,OAAA7Z,IAAA,KAAAA,IAAA,KAEA,IAAAsD,EAAA2V,EAAAtD,OAAA+D,kBACA,GAAApW,EAAA,CAEAA,EAAAuW,OAAAZ,EAAAtD,OAAAkE,YAES,CAGT,GAAAuD,IAAA,cAEA/c,KAAAoY,QAAA,qBAAA2E,EAAA,MAIAnE,EAAA+B,WAEA,IAAAsD,EAAA,IAAAxU,EAAAyU,MACAD,EAAAhF,kBAAA,GAAAtW,OAAAiW,EAAAK,mBAEA,QAAA7W,EAAA,EAAAqa,EAAA7D,EAAAC,QAAAvW,OAA+CF,EAAAqa,EAAOra,IAAA,CAEtD,IAAAkT,EAAAsD,EAAAC,QAAAzW,GACA,IAAAmX,EAAAjE,EAAAiE,SACA,IAAAnC,EAAA9B,EAAA8B,UACA,IAAA+G,EAAA5E,EAAAlY,OAAA,OAGA,GAAAkY,EAAAT,SAAAxW,SAAA,WAEA,IAAA8b,EAAA,IAAA3U,EAAA4U,eAEAD,EAAAE,aAAA,eAAA7U,EAAA8U,gBAAA,IAAAC,aAAAjF,EAAAT,UAAA,IAEA,GAAAS,EAAAR,QAAAzW,OAAA,GAEA8b,EAAAE,aAAA,aAAA7U,EAAA8U,gBAAA,IAAAC,aAAAjF,EAAAR,SAAA,QACS,CAETqF,EAAAK,uBAGA,GAAAlF,EAAAP,IAAA1W,OAAA,GAEA8b,EAAAE,aAAA,SAAA7U,EAAA8U,gBAAA,IAAAC,aAAAjF,EAAAP,KAAA,IAKA,IAAA0F,EAAA,GAEA,QAAAjE,EAAA,EAAAkE,EAAAvH,EAAA9U,OAAkDmY,EAAAkE,EAAYlE,IAAA,CAE9D,IAAAmE,EAAAxH,EAAAqD,GACA,IAAAxX,EAAAzB,UAEA,GAAAxB,KAAAoX,YAAA,MAEAnU,EAAAjD,KAAAoX,UAAAyH,OAAAD,EAAA3T,MAGA,GAAAkT,GAAAlb,kBAAAwG,EAAAqV,mBAAA,CAEA,IAAAC,EAAA,IAAAtV,EAAAqV,kBACAC,EAAAC,KAAA/b,GACAA,EAAA8b,GAIA,IAAA9b,EAAA,CAEAA,GAAAkb,EAAA,IAAA1U,EAAAwV,kBAAA,IAAAxV,EAAAqV,kBACA7b,EAAAgI,KAAA2T,EAAA3T,KAGAhI,EAAAic,QAAAN,EAAApF,OAAA/P,EAAA0V,cAAA1V,EAAA2V,YAEAV,EAAApE,KAAArX,GAKA,IAAAoc,EAEA,GAAAX,EAAApc,OAAA,GAEA,QAAAmY,EAAA,EAAAkE,EAAAvH,EAAA9U,OAAoDmY,EAAAkE,EAAYlE,IAAA,CAEhE,IAAAmE,EAAAxH,EAAAqD,GACA2D,EAAAkB,SAAAV,EAAA3E,WAAA2E,EAAA/E,WAAAY,GAGA,IAAA8E,EAAA,IAAA9V,EAAA+V,cAAAd,GACAW,GAAAlB,EAAA,IAAA1U,EAAAgW,KAAArB,EAAAmB,GAAA,IAAA9V,EAAAiW,aAAAtB,EAAAmB,OACS,CAETF,GAAAlB,EAAA,IAAA1U,EAAAgW,KAAArB,EAAAM,EAAA,QAAAjV,EAAAiW,aAAAtB,EAAAM,EAAA,IAGAW,EAAApU,KAAAqK,EAAArK,KAEAgT,EAAA/R,IAAAmT,GAGA,GAAA1C,EAAA,CACA1U,QAAA0X,QAAA,aAGA,OAAA1B,2BCnpBA,IAAA2B,EAAAC,SAAAtgB,UAGA,IAAAugB,EAAAF,EAAAngB,SASA,SAAAsgB,EAAA5W,GACA,GAAAA,GAAA,MACA,IACA,OAAA2W,EAAAlgB,KAAAuJ,GACK,MAAA2F,IACL,IACA,OAAA3F,EAAA,GACK,MAAA2F,KAEL,SAGAjP,EAAAC,QAAAigB,wBCzBA,IAAAC,EAAiBpX,EAAQ,QACzBqX,EAAerX,EAAQ,QAGvB,IAAAsX,EAAA,yBACAC,EAAA,oBACAC,EAAA,6BACAC,EAAA,iBAmBA,SAAAC,EAAA3gB,GACA,IAAAsgB,EAAAtgB,GAAA,CACA,aAIA,IAAA4gB,EAAAP,EAAArgB,GACA,OAAA4gB,GAAAJ,GAAAI,GAAAH,GAAAG,GAAAL,GAAAK,GAAAF,EAGAxgB,EAAAC,QAAAwgB,wBCpCA,IAAA7e,EAAamH,EAAQ,QACrB4X,EAAkB5X,EAAQ,QAC1BoR,EAAcpR,EAAQ,QAGtB,IAAA6X,EAAAhf,IAAAif,mBAAAlf,UASA,SAAAmf,EAAAhhB,GACA,OAAAqa,EAAAra,IAAA6gB,EAAA7gB,OACA8gB,GAAA9gB,KAAA8gB,IAGA5gB,EAAAC,QAAA6gB,wBCnBA,SAAAC,EAAA5Y,GACAnI,EAAAC,QAAA8gB,EAAAthB,OAAAuhB,eAAAvhB,OAAAwhB,eAAA,SAAAF,EAAA5Y,GACA,OAAAA,EAAA+Y,WAAAzhB,OAAAwhB,eAAA9Y,IAEA,OAAA4Y,EAAA5Y,GAGAnI,EAAAC,QAAA8gB,0BCPA,IAAAvX,EAAeT,EAAQ,QACvBoY,EAAepY,EAAQ,QACvBqY,EAAkBrY,EAAQ,QAU1B,SAAAsY,EAAA/X,EAAAgY,GACA,OAAAF,EAAAD,EAAA7X,EAAAgY,EAAA9X,GAAAF,EAAA,IAGAtJ,EAAAC,QAAAohB,wBCNA,SAAAE,EAAAjY,EAAAkY,EAAAC,GACA,OAAAA,EAAAhf,QACA,cAAA6G,EAAAvJ,KAAAyhB,GACA,cAAAlY,EAAAvJ,KAAAyhB,EAAAC,EAAA,IACA,cAAAnY,EAAAvJ,KAAAyhB,EAAAC,EAAA,GAAAA,EAAA,IACA,cAAAnY,EAAAvJ,KAAAyhB,EAAAC,EAAA,GAAAA,EAAA,GAAAA,EAAA,IAEA,OAAAnY,EAAAiY,MAAAC,EAAAC,GAGAzhB,EAAAC,QAAAshB,wBCpBA,SAAAG,EAAAnU,GACA,GAAAA,SAAA,GACA,UAAAoU,eAAA,6DAGA,OAAApU,EAGAvN,EAAAC,QAAAyhB,0BCRA,IAAAjY,EAAsBV,EAAQ,QAC9B6Y,EAAe7Y,EAAQ,QAUvB,IAAAqY,EAAAQ,EAAAnY,GAEAzJ,EAAAC,QAAAmhB,wBCUA,IAAAjH,EAAAzL,MAAAyL,QAEAna,EAAAC,QAAAka,wBCDA,SAAA0H,EAAA/hB,GACA,OAAAA,GAAA,aAAAA,GAAA,SAGAE,EAAAC,QAAA4hB,wBC5BA,IAAAC,EAAkB/Y,EAAQ,QAC1B8Y,EAAmB9Y,EAAQ,QA2B3B,SAAAgZ,EAAAjiB,GACA,OAAA+hB,EAAA/hB,IAAAgiB,EAAAhiB,GAGAE,EAAAC,QAAA8hB,wBChCA,SAAAC,EAAA7Z,EAAA8Z,GACAjiB,EAAAC,QAAA+hB,EAAAviB,OAAAuhB,gBAAA,SAAAgB,EAAA7Z,EAAA8Z,GACA9Z,EAAA+Y,UAAAe,EACA,OAAA9Z,GAGA,OAAA6Z,EAAA7Z,EAAA8Z,GAGAjiB,EAAAC,QAAA+hB,wBCTA,IAAAvB,EAAiB1X,EAAQ,QACzBM,EAAeN,EAAQ,QACvBqX,EAAerX,EAAQ,QACvBmX,EAAenX,EAAQ,QAMvB,IAAAmZ,EAAA,sBAGA,IAAAC,EAAA,8BAGA,IAAApC,EAAAC,SAAAtgB,UACAF,EAAAC,OAAAC,UAGA,IAAAugB,EAAAF,EAAAngB,SAGA,IAAAwiB,EAAA5iB,EAAA4iB,eAGA,IAAAC,EAAAC,OAAA,IACArC,EAAAlgB,KAAAqiB,GAAA9W,QAAA4W,EAAA,QACA5W,QAAA,uEAWA,SAAAiX,EAAAziB,GACA,IAAAsgB,EAAAtgB,IAAAuJ,EAAAvJ,GAAA,CACA,aAEA,IAAA0iB,EAAA/B,EAAA3gB,GAAAuiB,EAAAF,EACA,OAAAK,EAAAtE,KAAAgC,EAAApgB,IAGAE,EAAAC,QAAAsiB,wBC7CA,IAAAE,EAAA,IACAC,EAAA,GAGA,IAAAC,EAAAC,KAAAC,IAWA,SAAAjB,EAAAtY,GACA,IAAAwZ,EAAA,EACAC,EAAA,EAEA,kBACA,IAAAC,EAAAL,IACAM,EAAAP,GAAAM,EAAAD,GAEAA,EAAAC,EACA,GAAAC,EAAA,GACA,KAAAH,GAAAL,EAAA,CACA,OAAAjgB,UAAA,QAEK,CACLsgB,EAAA,EAEA,OAAAxZ,EAAAiY,MAAA5f,UAAAa,YAIAxC,EAAAC,QAAA2hB,wBCXA,SAAAxB,EAAAtgB,GACA,IAAA0B,SAAA1B,EACA,OAAAA,GAAA,OAAA0B,GAAA,UAAAA,GAAA,YAGAxB,EAAAC,QAAAmgB,0BC7BArX,EAAQ,OAARA,CAAuB,oBAAAma,EAAAC,EAAAC,GAEvB,gBAAAC,EAAA7L,GACA,aACA,IAAA8L,EAAAJ,EAAA/iB,MACA,IAAAojB,EAAA/L,GAAA7V,oBAAA6V,EAAA2L,GACA,OAAAI,IAAA5hB,UAAA4hB,EAAAxjB,KAAAyX,EAAA8L,GAAA,IAAAhB,OAAA9K,GAAA2L,GAAAK,OAAAF,KACGF,6BCRH,IAAAxhB,EAAamH,EAAQ,QACrB0a,EAAgB1a,EAAQ,QACxBlJ,EAAqBkJ,EAAQ,QAG7B,IAAA2a,EAAA,gBACAC,EAAA,qBAGA,IAAAC,EAAAhiB,IAAAiiB,YAAAliB,UASA,SAAAwe,EAAArgB,GACA,GAAAA,GAAA,MACA,OAAAA,IAAA6B,UAAAgiB,EAAAD,EAEA,OAAAE,QAAAnkB,OAAAK,GACA2jB,EAAA3jB,GACAD,EAAAC,GAGAE,EAAAC,QAAAkgB,0BC3BA,IAAArX,EAAWC,EAAQ,QAGnB,IAAAnH,EAAAkH,EAAAlH,OAEA5B,EAAAC,QAAA2B,yBCIA,SAAAkiB,EAAAC,EAAAC,GACA,IAAA/J,GAAA,EACA/Y,EAAAwN,MAAAqV,GAEA,QAAA9J,EAAA8J,EAAA,CACA7iB,EAAA+Y,GAAA+J,EAAA/J,GAEA,OAAA/Y,EAGAlB,EAAAC,QAAA6jB,0BCnBA,IAAAG,EAAclb,EAAQ,QAEtB,IAAAmb,EAA4Bnb,EAAQ,QAEpC,SAAAob,EAAA5W,EAAAxN,GACA,GAAAA,IAAAkkB,EAAAlkB,KAAA,iBAAAA,IAAA,aACA,OAAAA,EAGA,OAAAmkB,EAAA3W,GAGAvN,EAAAC,QAAAkkB,0BCZA,IAAA5C,EAAYxY,EAAQ,QAGpB,IAAAqb,EAAA5R,KAAAI,IAWA,SAAAuO,EAAA7X,EAAAgY,EAAA+C,GACA/C,EAAA8C,EAAA9C,IAAA3f,UAAA2H,EAAA7G,OAAA,EAAA6e,EAAA,GACA,kBACA,IAAAG,EAAAjf,UACAyX,GAAA,EACAxX,EAAA2hB,EAAA3C,EAAAhf,OAAA6e,EAAA,GACAnK,EAAAzI,MAAAjM,GAEA,QAAAwX,EAAAxX,EAAA,CACA0U,EAAA8C,GAAAwH,EAAAH,EAAArH,GAEAA,GAAA,EACA,IAAAqK,EAAA5V,MAAA4S,EAAA,GACA,QAAArH,EAAAqH,EAAA,CACAgD,EAAArK,GAAAwH,EAAAxH,GAEAqK,EAAAhD,GAAA+C,EAAAlN,GACA,OAAAoK,EAAAjY,EAAAnJ,KAAAmkB,IAIAtkB,EAAAC,QAAAkhB,wBC3BA,SAAAoD,EAAA9O,EAAA/U,GACA,OAAA+U,GAAA,KAAA9T,UAAA8T,EAAA/U,GAGAV,EAAAC,QAAAskB,2BCZA,SAAAC,GACA,IAAAC,SAAAD,GAAA,UAAAA,KAAA/kB,iBAAA+kB,EAEAxkB,EAAAC,QAAAwkB,+CCFA,IAAAC,EAAA,iBA4BA,SAAAC,EAAA7kB,GACA,cAAAA,GAAA,UACAA,GAAA,GAAAA,EAAA,MAAAA,GAAA4kB,EAGA1kB,EAAAC,QAAA0kB,0BClCA,IAAA/iB,EAAamH,EAAQ,QAGrB,IAAAvJ,EAAAC,OAAAC,UAGA,IAAA0iB,EAAA5iB,EAAA4iB,eAOA,IAAAziB,EAAAH,EAAAI,SAGA,IAAAgkB,EAAAhiB,IAAAiiB,YAAAliB,UASA,SAAA8hB,EAAA3jB,GACA,IAAA8kB,EAAAxC,EAAAriB,KAAAD,EAAA8jB,GACAlD,EAAA5gB,EAAA8jB,GAEA,IACA9jB,EAAA8jB,GAAAjiB,UACA,IAAAkjB,EAAA,KACG,MAAA5V,IAEH,IAAA/N,EAAAvB,EAAAI,KAAAD,GACA,GAAA+kB,EAAA,CACA,GAAAD,EAAA,CACA9kB,EAAA8jB,GAAAlD,MACK,QACL5gB,EAAA8jB,IAGA,OAAA1iB,EAGAlB,EAAAC,QAAAwjB,0BC7CA,IAAAtD,EAAiBpX,EAAQ,QACzB8Y,EAAmB9Y,EAAQ,QAG3B,IAAA+b,EAAA,qBASA,SAAAC,EAAAjlB,GACA,OAAA+hB,EAAA/hB,IAAAqgB,EAAArgB,IAAAglB,EAGA9kB,EAAAC,QAAA8kB,wBCEA,SAAAxb,EAAAzJ,GACA,kBACA,OAAAA,GAIAE,EAAAC,QAAAsJ,yXCzBA,IAAIyb,EAAgB,SAAhBA,EAAyBC,EAAUC,EAASC,EAAQrT,EAAOC,EAAMqT,EAAYC,EAC1DxT,EAAOyT,EAAeC,EAAcC,GAEkB,IADtDC,EACsDjjB,UAAAC,OAAA,IAAAD,UAAA,MAAAb,UAAAa,UAAA,IADtC,CAAC,EAAE,EAAE,EAAE,GAC+B,IAD3BkjB,EAC2BljB,UAAAC,OAAA,IAAAD,UAAA,MAAAb,UAAAa,UAAA,IADA,KACA,IAAtDmjB,EAAsDnjB,UAAAC,OAAA,IAAAD,UAAA,MAAAb,UAAAa,UAAA,KAAvC,EAAuC,IAApCojB,EAAoCpjB,UAAAC,OAAA,IAAAD,UAAA,MAAAb,UAAAa,UAAA,IAApB,KAAoB,IAAd8R,EAAc9R,UAAAC,OAAA,IAAAD,UAAA,MAAAb,UAAAa,UAAA,IAAH,EACtE,IAAIgP,EAASyT,EAASY,aACpBX,EACAC,EACArT,EACAC,EACAqT,EACAC,EACAxT,EACAyT,EACAC,EACAC,EACAC,EACFC,EACAC,EACAC,GAAmB,GACnBtR,GAAc,GAEd,OAAO9C,mBClBX,IAAMsU,EAAe,GACrB,IAAMC,EAAgB,GAEf,IAAMC,EAAb,oBAAAA,IAAAzb,IAAApK,KAAA6lB,GAAAC,IAAAD,EAAA,EAAAtlB,IAAA,uBAAAZ,MAAA,SAAAomB,EACyBjM,EAAOkM,EAAWC,GACnC,IAAIC,EACJ,IAAIrW,EAAQ7P,KAAK6P,MAEjB,UAAWmW,IAAc,YAAa,CAClCE,EAAMlmB,KAAKmmB,aAAaH,EAAWC,OAChC,CACHC,EAAM,CACFE,QAAS,KACTC,OAAQ,KACRC,WAAY,MAGpB,GAAItmB,KAAK0R,OAAS1R,KAAKumB,eAAgB,CACnCL,EAAIG,OAAS,KACbH,EAAIM,KAAO,MAIf,GAAIN,EAAIM,OAAS,KAAM,CACnB,OAAO,KAEX,IAAIC,EAEJ,GAAIP,EAAIE,QAAS,CAEbK,EAAWzmB,KAAKqK,SAAS,YAAY8P,YAClC,CAEHsM,EAAWzmB,KAAK0mB,MAAM5U,UAAUgI,GAGpC,IAAI6M,EAAWtU,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,GAAK,EAGhE,GAAIkiB,EAAIG,OAAQ,CACZ,IAAIS,EAAc9mB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,aAC/Doc,EAASQ,MAAMC,KAAK,GACpBT,EAASQ,MAAME,KAAKnnB,KAAK0R,MAAQ,KACjC+U,EAASQ,MAAMG,KAAKT,EAAW,KAC/BF,EAASY,SAAStjB,GAAKsO,KAAKiV,GAAK,EACjC,GAAIpB,EAAIE,QAAS,CAEbK,EAASQ,MAAMC,KAAK,GACpBT,EAASQ,MAAME,KAAKnnB,KAAK0R,MAAQ,KACjC+U,EAASQ,MAAMG,KAAKT,EAAW,KAGnCF,EAAS3iB,SAASojB,KAAKjB,EAAUW,OAAO7iB,GACxC0iB,EAAS3iB,SAASqjB,KACd9U,KAAKG,IAAIyT,EAAUW,OAAO5iB,EAAGiiB,EAAUY,IAAI7iB,GAAK,KAIxD,GAAIkiB,EAAII,WAAY,CAEhBG,EAAS3iB,SAASojB,KAAKjB,EAAUW,OAAO7iB,GACxC0iB,EAAS3iB,SAASqjB,KACd9U,KAAKG,IAAIyT,EAAUW,OAAO5iB,EAAGiiB,EAAUY,IAAI7iB,GAAK,KAEpDyiB,EAASQ,MAAME,KAAKnnB,KAAK0R,MAAQ,KACjC+U,EAASQ,MAAMG,KAAKT,EAAW,KAGnC,GAAIT,EAAIE,QAAS,CAEbK,EAASxb,KAAO,WAChB4E,EAAM3D,IAAIua,GACVzmB,KAAK0mB,MAAM5U,UAAUwI,KAAKmM,MArEtC,CAAAlmB,IAAA,mBAAAZ,MAAA,SAAA4nB,EAyEqBzN,EAAOkM,EAAWC,GAC/B,IAAIC,EACJ,IAAIrW,EAAQ7P,KAAK6P,MAEjB,UAAWmW,IAAc,YAAa,CAClCE,EAAMlmB,KAAKmmB,aAAaH,EAAWC,OAChC,CACHC,EAAM,CACFE,QAAS,KACTC,OAAQ,KACRC,WAAY,MAGpB,GAAIJ,EAAIM,OAAS,KAAM,CACnB,OAAO,KAGX,IAAInmB,EACJ,GAAI6lB,EAAIE,QAAS,CACb/lB,EAAOL,KAAKqK,SAAS,cAAc8P,YAChC,CACH9Z,EAAOL,KAAK0mB,MAAMtT,MAAM0G,GAE5B,IAAI0N,EAAUxnB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,eAE3D,IAAIod,EAAQC,EACZrnB,EAAKgnB,SAAStjB,GAAKsO,KAAKiV,GAExBI,EAASrV,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,EAAI,IAAMwjB,EAAQG,OAAO5jB,EAC9E0jB,EAASpV,KAAKO,IAAIqT,EAAUW,OAAO7iB,EAAIkiB,EAAUY,IAAI9iB,EAAI,IAAMyjB,EAAQG,OAAO5jB,EAE9E1D,EAAKyD,SAASojB,MAAMjB,EAAUW,OAAO7iB,EAAIkiB,EAAUY,IAAI9iB,GAAK,GAC5D1D,EAAKyD,SAASqjB,KAAKlB,EAAUW,OAAO5iB,EAAI,GACxC3D,EAAKyD,SAASsjB,KAAKnB,EAAUW,OAAO3iB,GACpC5D,EAAK4mB,MAAME,KAAKO,GAChBrnB,EAAK4mB,MAAMC,KAAKO,GA6BhB,GAAIvB,EAAIE,QAAS,CACb/lB,EAAK4K,KAAO,SACZ4E,EAAM3D,IAAI7L,GACV,GAAIL,KAAK0mB,MAAMtT,QAAU5R,UAAW,CAChCxB,KAAK0mB,MAAMtT,MAAQ,GAEvBpT,KAAK0mB,MAAMtT,MAAMkH,KAAKja,MA/IlC,CAAAE,IAAA,sBAAAZ,MAAA,SAAAioB,EAmJwB9N,EAAOkM,EAAWC,EAAW4B,GAE7C,IAAI3B,EACJ,IAAIrW,EAAQ7P,KAAK6P,MACjB,UAAWmW,IAAc,YAAa,CAClCE,EAAMlmB,KAAKmmB,aAAaH,EAAWC,OAChC,CACHC,EAAM,CACFE,QAAS,KACTC,OAAQ,KACRC,WAAY,MAGpB,GAAItmB,KAAK0R,OAAS1R,KAAKumB,eAAgB,CACnCL,EAAIG,OAAS,KACbH,EAAIM,KAAO,MAEf,GAAIN,EAAIM,OAAS,KAAM,CACnB,OAAO,KAGX,IAAIsB,EACJ,GAAI5B,EAAIE,QAAS,CACb0B,EAAU9nB,KAAKqK,SAAS,WAAW8P,YAChC,CACH2N,EAAU9nB,KAAK0mB,MAAM,YAAY5M,GAErC,IAAIiO,EAAa/nB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,eAE9D,IAAIod,EAAQO,EACZ,GAAIH,GAAe,OAAQ,CACvBC,EAAQT,SAAStjB,GAAKsO,KAAKiV,GAAK,MAC7B,CACHQ,EAAQT,SAAStjB,EAAIsO,KAAKiV,GAAK,EAEnC,GAAIpB,EAAIE,QAAS,EAGjB4B,EACI3V,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,GAAK+jB,EAAWJ,OAAO5jB,EAEvE0jB,EACIpV,KAAKO,IAAIqT,EAAUW,OAAO7iB,EAAIkiB,EAAUY,IAAI9iB,GAAKgkB,EAAWJ,OAAO5jB,EAEvE,GAAImiB,EAAIG,OAAQ,CACZyB,EAAQb,MAAMG,KAAKY,GACnBF,EAAQb,MAAMC,KAAKO,GACnB,GAAIvB,EAAIE,QAAS,CACb0B,EAAQb,MAAME,KAAK,GAEvBY,EAAa/nB,KAAK+mB,YAAYC,cAAcc,GAC5CA,EAAQhkB,SAASmkB,IACb5V,KAAKG,IAAIyT,EAAUW,OAAO7iB,EAAGkiB,EAAUY,IAAI9iB,GAAKgkB,EAAWJ,OAAO5jB,EAAI,EACtEsO,KAAKG,IAAIyT,EAAUW,OAAO5iB,EAAGiiB,EAAUY,IAAI7iB,GAC3CqO,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,GAAK,EACjD,GAIR,GAAIkiB,EAAII,WAAY,CAChByB,EAAa/nB,KAAK+mB,YAAYC,cAAcc,GAC5CA,EAAQhkB,SAASmkB,IACb5V,KAAKG,IAAIyT,EAAUW,OAAO7iB,EAAGkiB,EAAUY,IAAI9iB,GAAKgkB,EAAWJ,OAAO5jB,EAAI,EACtEsO,KAAKG,IAAIyT,EAAUW,OAAO5iB,EAAGiiB,EAAUY,IAAI7iB,GAC3CqO,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,GAAK,EACjD,GAIR,GAAIkiB,EAAIE,QAAS,CACb0B,EAAQ7c,KAAO,WACf4E,EAAM3D,IAAI4b,GACV,GAAI9nB,KAAK0mB,MAAM,cAAgBllB,UAAW,CACtCxB,KAAK0mB,MAAM,YAAc,GAE7B1mB,KAAK0mB,MAAM,YAAYpM,KAAKwN,MA9NxC,CAAAvnB,IAAA,mBAAAZ,MAAA,SAAAuoB,EAkOqBpO,EAAOkM,EAAWC,GAC/B,IAAIC,EACJ,IAAIrW,EAAQ7P,KAAK6P,MACjB,UAAWmW,IAAc,YAAa,CAClCE,EAAMlmB,KAAKmmB,aAAaH,EAAWC,OAChC,CACHC,EAAM,CACFE,QAAS,KACTC,OAAQ,KACRC,WAAY,MAGpB,GAAItmB,KAAK0R,OAAS1R,KAAKumB,eAAgB,CACnCL,EAAIG,OAAS,KACbH,EAAIM,KAAO,MAEf,GAAIN,EAAIM,OAAS,KAAM,CACnB,OAAO,KAEX,IAAI2B,EACJ,GAAIjC,EAAIE,QAAS,CACb+B,EAAanoB,KAAKqK,SAAS,cAAc8P,YACtC,CACHgO,EAAanoB,KAAK0mB,MAAM7U,YAAYiI,GAGxC,IAAI6M,EAAWtU,KAAKO,IAAIqT,EAAUW,OAAO7iB,EAAIkiB,EAAUY,IAAI9iB,GAC3D,GAAImiB,EAAIG,OAAQ,CACZ,IAAI+B,EAAgBpoB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,eACjE,IAAI4c,EAAQN,EAAWyB,EAAcT,OAAO5jB,EAC5CokB,EAAWlB,MAAMC,KAAKD,GACtBkB,EAAWd,SAAStjB,GAAKsO,KAAKiV,GAAK,EAEnCa,EAAWlB,MAAMG,KAAK,GACtBe,EAAWlB,MAAME,KAAKnnB,KAAK0R,MAAQ0W,EAAcT,OAAO3jB,GACxDmkB,EAAWrkB,SAASqjB,KAAKlB,EAAUW,OAAO5iB,GAC1CmkB,EAAWrkB,SAASojB,KAChB7U,KAAKG,IAAIyT,EAAUW,OAAO7iB,EAAGkiB,EAAUY,IAAI9iB,GAAK4iB,EAAW,GAInE,GAAIT,EAAII,WAAY,CAChB6B,EAAWrkB,SAASqjB,KAAKlB,EAAUW,OAAO5iB,GAC1CmkB,EAAWrkB,SAASojB,KAChB7U,KAAKG,IAAIyT,EAAUW,OAAO7iB,EAAGkiB,EAAUY,IAAI9iB,GAAK4iB,EAAW,GAInE,GAAIT,EAAIE,QAAS,CACb+B,EAAWld,KAAO,aAClBjL,KAAK6P,MAAM3D,IAAIic,GACfnoB,KAAK0mB,MAAM7U,YAAYyI,KAAK6N,MArRxC,CAAA5nB,IAAA,aAAAZ,MAAA,SAAA0oB,EAyRevkB,GAEP,IAAIwkB,EAAMtoB,KAAKqK,SAAS,OAAO8P,QAC/B,IAAIoO,EAASvoB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,QAC1Die,EAAIxkB,SAASojB,KAAKpjB,EAASkP,IAC3BsV,EAAIxkB,SAASsjB,KAAKtjB,EAAS8R,IAC3B0S,EAAIxkB,SAASqjB,MAAMrjB,EAASgP,GAAGhP,EAAS+O,IAAI,EAAIR,KAAKO,IAAI9O,EAASgP,GAAKhP,EAAS+O,IAAM,GACtFyV,EAAIrB,MAAMG,KAAK/U,KAAKO,IAAI9O,EAASgP,GAAKhP,EAAS+O,IAAM0V,EAAOZ,OAAO1jB,GAEnEqkB,EAAIjB,SAAStjB,EAAIukB,EAAIjB,SAAStjB,EAAI,GAAKsO,KAAKiV,GAAK,IAGjDgB,EAAIrd,KAAO,MACXjL,KAAK6P,MAAM3D,IAAIoc,GACftoB,KAAK0mB,MAAM8B,KAAKlO,KAAKgO,KAvS7B,CAAA/nB,IAAA,gBAAAZ,MAAA,SAAA8oB,EA4SkB3kB,EAAU6jB,EAAM1C,EAAY9Q,EAAYuU,GAClD,IAAIC,IAAaD,EAAmBC,SACpC,GAAI3oB,KAAK4oB,cAAgB,EAAG,CACxB,IAAIC,EAAa,IAAIpf,WACrB,IAAIoG,EAAQ7P,KAAK6P,MACjB,IAAIiZ,EAAqBJ,EAAmBI,mBAC5C,IAAIC,EAAsBL,EAAmBK,oBAE7CF,EAAW5d,KAAO,eAClB,IAAI+d,EAASN,EAAmBO,YAChC,IAAIC,EAAgBR,EAAmBQ,cACvC,IAAIC,EAAsBT,EAAmBS,oBAK7C,IAAIC,EAAgBV,EAAmBU,cACvC,IAAIC,EAAmBX,EAAmBW,iBAC1C,IAAIC,EAAeZ,EAAmBY,aAItC,IAAIC,EAAcb,EAAmBa,YAErC,IAAIC,EAAed,EAAmBc,aAEtC,IAAIC,EAAef,EAAmBe,aAEtC,IAAIC,EAGJb,EAAW/kB,SAASojB,KAAKpjB,EAASC,GAClC8kB,EAAW/kB,SAASqjB,KAAKrjB,EAASE,GAClC6kB,EAAW/kB,SAASsjB,KAAKtjB,EAASG,GAAK0kB,GAAY3oB,KAAK4oB,eAAiB,EAAI,IAAK,IAIlF,GAAIF,EAAmBrnB,MAAQ,EAAG,CAE9B,IAAIsoB,EAAO3pB,KAAKqK,SAAS,QAAQ8P,QACjC,IAAI/M,EAAOpN,KACX2pB,EAAK/b,SAAS,SAAUC,GACpB,GAAIA,aAAiBpE,UAAY,KAIrCkgB,EAAK1e,KAAO,gBAGZ0e,EAAK7lB,SAASojB,KAAK,GACnByC,EAAK7lB,SAASqjB,KACVrjB,EAASE,EAAI6kB,EAAW/kB,SAASE,GAErC2lB,EAAK7lB,SAASsjB,KAAK,GAEnBsC,EAAU1pB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,iBAEvDsf,EAAK1C,MAAMC,MAAMS,EAAK5jB,EAAIilB,GAAUU,EAAQ/B,OAAO5jB,GACnD4lB,EAAK1C,MAAME,MAAMQ,EAAK3jB,EAAIglB,GAAUU,EAAQ/B,OAAO3jB,GACnD2lB,EAAK1C,MAAMG,KAAKO,EAAK1jB,EAAIylB,EAAQ/B,OAAO1jB,GAExC,GAAIjE,KAAK4oB,eAAiB,EAAG,CACzBC,EAAW3c,IAAIyd,GAEnB,IAAIC,EAAiB5pB,KAAKqK,SAAS,qBAAqB8P,QACxDyP,EAAe3e,KAAO,UAEtB2e,EAAevC,SAAStjB,GAAKsO,KAAKiV,GAAK,EACvCsC,EAAe9lB,SAASojB,KAAKpjB,EAASC,EAAI4jB,EAAK5jB,EAAI,EAAI8kB,EAAW/kB,SAASC,EAAI+kB,EAAqB,EAAIE,GACxGY,EAAe9lB,SAASqjB,KAAKQ,EAAK3jB,EAAI,EAAI+kB,EAAsB,GAChEa,EAAe9lB,SAASsjB,KAAKO,EAAK1jB,EAAI0hB,EAAe,EAAI,GAEzD+D,EAAU1pB,KAAK+mB,YAAYC,cAAc4C,GACzCA,EAAe3C,MAAME,KAAK,GAC1ByC,EAAe3C,MAAMC,KAAK,GAC1B0C,EAAe3C,MAAMG,KAAK,GAE1BwC,EAAehc,SAAS,SAAUC,GAC9B,GACIA,aAAiBpE,kBACVoE,EAAM5K,WAAa,YAC5B,CACE4K,EAAM5K,SAAS4mB,YAAc,KAC7Bhc,EAAM0L,SAASuQ,YAGvBjB,EAAW3c,IAAI0d,GAEf,IAAIG,EAAwB/pB,KAAKqK,SAAS,4BAA4B8P,QACtE4P,EAAsB9e,KAAO,iBAE7B8e,EAAsB1C,SAAStjB,GAAKsO,KAAKiV,GAAK,EAC9CyC,EAAsBjmB,SAASojB,KAAKpjB,EAASC,EAAI4jB,EAAK5jB,EAAI,EAAI8kB,EAAW/kB,SAASC,GAClFgmB,EAAsBjmB,SAASqjB,KAAKQ,EAAK3jB,EAAI,EAAI+kB,EAAsB,EAAI,GAC3EgB,EAAsBjmB,SAASsjB,KAAKO,EAAK1jB,EAAI0hB,EAAe,GAE5D+D,EAAU1pB,KAAK+mB,YAAYC,cAAc+C,GACzCA,EAAsB9C,MAAME,KAAK,GACjC4C,EAAsB9C,MAAMC,KAAK,GACjC6C,EAAsB9C,MAAMG,KAAK,GAEjCyB,EAAW3c,IAAI6d,OAEZ,CAEH,IAAIJ,EAAO3pB,KAAKqK,SAAS,gBAAgB8P,QACzC,IAAI/M,EAAOpN,KACX2pB,EAAK/b,SAAS,SAAUC,GACpB,GAAIA,aAAiBpE,UAAY,KAIrCkgB,EAAK1e,KAAO,gBAGZ0e,EAAK7lB,SAASojB,KAAK,GACnByC,EAAK7lB,SAASqjB,KACVrjB,EAASE,EAAImlB,EAAsB,EAAIN,EAAW/kB,SAASE,GAE/D2lB,EAAK7lB,SAASsjB,KAAK,GAEnBsC,EAAU1pB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,iBAEvDsf,EAAK1C,MAAMC,MAAMS,EAAK5jB,EAAIilB,EAAS,GAAKU,EAAQ/B,OAAO5jB,GACvD4lB,EAAK1C,MAAME,MAAMQ,EAAK3jB,EAAImlB,EAAsBH,GAAUU,EAAQ/B,OAAO3jB,GACzE2lB,EAAK1C,MAAMG,KAAKO,EAAK1jB,EAAIylB,EAAQ/B,OAAO1jB,GACxC,GAAIjE,KAAK4oB,eAAiB,EAAG,CACzBC,EAAW3c,IAAIyd,GAGnB,IAAIK,EAAehqB,KAAKqK,SAAS,iBAAiB8P,QAClD6P,EAAa/e,KAAO,cAIpB+e,EAAa3C,SAASrjB,EAAIgmB,EAAa3C,SAASrjB,EAAI,GAAKqO,KAAKiV,GAAK,IAGnE0C,EAAalmB,SAASqjB,KAClBrjB,EAASE,GACR2jB,EAAK3jB,EAAI,EAAImlB,GACdH,EACAH,EAAW/kB,SAASE,EAAI,GAG5BgmB,EAAalmB,SAASojB,KAAK,GAC3B8C,EAAalmB,SAASsjB,KAAK,GAE3BsC,EAAU1pB,KAAK+mB,YAAYC,cAAcgD,GACzC,IAAIC,EAAY,EAChBD,EAAa/C,MAAME,KACf,IAEJ6C,EAAa/C,MAAMG,KAAKO,EAAK5jB,EAAIkmB,EAAYP,EAAQ/B,OAAO5jB,GAC5DimB,EAAa/C,MAAMC,KAAKS,EAAK1jB,EAAIgmB,EAAYP,EAAQ/B,OAAO1jB,GAE5D+lB,EAAapc,SAAS,SAAUC,GAC5B,GAAIA,aAAiBpE,UAAY,CAC7BoE,EAAM5K,SAAWmK,EAAK8c,2BAA2B,YAAY,GAC7Drc,EAAM5K,SAAS4mB,YAAc,KAC7Bhc,EAAM0L,SAASuQ,YAGvBjB,EAAW3c,IAAI8d,GAKnB,IAAIpD,EAAS5mB,KAAKqK,SAAS,kBAAkB8P,QAE7CyM,EAAO3b,KAAO,iBAEd2b,EAAO9iB,SAASojB,KAAK,GACrBN,EAAO9iB,SAASqjB,KACZrjB,EAASE,EAAI2jB,EAAK3jB,EAAI,EAAIklB,EAAgBL,EAAW/kB,SAASE,GAElE4iB,EAAO9iB,SAASsjB,MAAMkC,EAAe,GACrC1C,EAAOS,SAAStjB,EAAIsO,KAAKiV,GAAK,IAE9BoC,EAAU1pB,KAAK+mB,YAAYC,cAAcJ,GACzCA,EAAOK,MAAMC,MAAMS,EAAK5jB,EAAIqlB,GAAiBM,EAAQ/B,OAAO5jB,GAC5D6iB,EAAOK,MAAME,KAAKkC,EAAmBK,EAAQ/B,OAAO3jB,GACpD4iB,EAAOK,MAAMG,KAAKkC,EAAeI,EAAQ/B,OAAO1jB,GAEhD4kB,EAAW3c,IAAI0a,GAIf,IAAIvmB,EAAOL,KAAKqK,SAAS,kBAAkB8P,QAE3C9Z,EAAK4K,KAAO,eAGZ5K,EAAKgnB,SAAStjB,GAAK,GAAKsO,KAAKiV,GAAK,IAElCjnB,EAAKyD,SAASojB,KAAK,GAEnB7mB,EAAKyD,SAASqjB,KACVrjB,EAASE,EACT2jB,EAAK3jB,EAAI,EACTklB,EACAL,EAAW/kB,SAASE,EACpBulB,EAAc,GAElBlpB,EAAKyD,SAASsjB,MAAMkC,GAEpBI,EAAU1pB,KAAK+mB,YAAYC,cAAc3mB,GACzCA,EAAK4mB,MAAMC,MACNS,EAAK5jB,EAAIqlB,EAAgBF,EAAgBF,EAAS,GAAKU,EAAQ/B,OAAO5jB,GAE3E1D,EAAK4mB,MAAMG,KACPmC,EACAG,EAAQ/B,OAAO3jB,GAInB6kB,EAAW3c,IAAI7L,GAIf,IAAI8pB,EAAQnqB,KAAKqK,SAAS,kBAAkB8P,QAE5CgQ,EAAMlf,KAAO,gBAGbkf,EAAM9C,SAASpjB,GAAK,GAAKoO,KAAKiV,GAAK,IACnC6C,EAAM9C,SAAStjB,EAAIsO,KAAKiV,GACxB6C,EAAMrmB,SAASojB,OAAOS,EAAK5jB,EAAI,GAAKilB,EAAS,GAC7CmB,EAAMrmB,SAASqjB,KACXrjB,EAASE,EACT2jB,EAAK3jB,EAAI,EACTklB,EACAL,EAAW/kB,SAASE,EACpBylB,EACA,GAEJU,EAAMrmB,SAASsjB,MAAMkC,EAAe,GAEpCI,EAAU1pB,KAAK+mB,YAAYC,cAAcmD,GACzCA,EAAMlD,MAAMC,KACRuC,EACAC,EAAQ/B,OAAO3jB,GAEnBmmB,EAAMlD,MAAMG,KAAKoC,EAAeE,EAAQ/B,OAAO1jB,GAE/C4kB,EAAW3c,IAAIie,GAIf,IAAIC,EAAQpqB,KAAKqK,SAAS,kBAAkB8P,QAE5CiQ,EAAMnf,KAAO,gBAEbmf,EAAM/C,SAASpjB,EAAI,GAAKoO,KAAKiV,GAAK,IAClC8C,EAAM/C,SAAStjB,EAAIsO,KAAKiV,GACxB8C,EAAMtmB,SAASojB,KAAKS,EAAK5jB,EAAI,EAAIilB,EAAS,GAC1CoB,EAAMtmB,SAASqjB,KACXrjB,EAASE,EACT2jB,EAAK3jB,EAAI,EACTklB,EACAL,EAAW/kB,SAASE,EACpBylB,EACA,GAEJW,EAAMtmB,SAASsjB,MAAMkC,EAAe,GAEpCI,EAAU1pB,KAAK+mB,YAAYC,cAAcoD,GAGzCA,EAAMnD,MAAMC,KACRuC,EACAC,EAAQ/B,OAAO3jB,GAEnBomB,EAAMnD,MAAMG,KAAKoC,EAAeE,EAAQ/B,OAAO1jB,GAE/C4kB,EAAW3c,IAAIke,GAEfpqB,KAAK0mB,MAAMjT,QAAQ6G,KAAKuO,GACxBA,EAAW/kB,SAASqjB,KAAKrjB,EAASE,EAAI2jB,EAAK3jB,EAAI,GAI/ChE,KAAK6P,MAAM3D,IAAI2c,GACfhZ,EAAM3D,IAAI2c,GACVhZ,EAAMwa,oBACH,GAAIlW,IAAe,EAAG,CACzB,IAAI0U,EAAa,IAAIpf,WACrB,IAAIoG,EAAQ7P,KAAK6P,MACjBgZ,EAAW5d,KAAO,eAClB,IAAI+d,EAAS,EACb,IAAIE,EAAgB,GACpB,IAAIC,EAAsB,GAC1B,IAAImB,EAAwB,EAC5B,IAAIC,EAAsB,GAC1B,IAAIC,EAAkB,GAEtB,IAAIpB,EAAgB,GACpB,IAAIC,EAAmB,GACvB,IAAIC,EAAetpB,KAAK0R,OAAS,IAAM,IAAM,IAE7C,IAAI+Y,EAAoB,GACxB,IAAIC,EAAiB,GACrB,IAAIC,EAAgB,IACpB,IAAIC,EAAgB,IACpB,IAAIC,EAAgB,IAAM,GAE1B,IAAIrB,EAAexpB,KAAK0R,OAAS,IAAM,IAAM,IAC7C,IAAIoZ,EAAkB,GACtB,IAAIC,GAAiB,IACrB,IAAIC,GAAiB,IACrB,IAAIC,GAAiB,IAAM,GAG3BpC,EAAW/kB,SAASojB,KAAKpjB,EAASC,GAClC8kB,EAAW/kB,SAASqjB,KAAKrjB,EAASE,GAClC6kB,EAAW/kB,SAASsjB,KAAKtjB,EAASG,EAAI,GAEtC,IAAIinB,GAAaC,EAAUrnB,EAASE,EAAGihB,GAEvC,IAAImG,GAAanG,EAAWiG,IAG5B,IAAIvB,GAAO3pB,KAAKqK,SAAS,gBAAgB8P,QACzC,IAAI/M,GAAOpN,KACX2pB,GAAK/b,SAAS,SAAUC,GACpB,GAAIA,aAAiBpE,UAAY,KAKrCkgB,GAAK1e,KAAO,gBAGZ0e,GAAK7lB,SAASojB,KAAK,GACnByC,GAAK7lB,SAASqjB,KACVrjB,EAASE,EAAImlB,EAAsB,EAAIN,EAAW/kB,SAASE,GAE/D2lB,GAAK7lB,SAASsjB,KAAK,GAEnB,IAAIsC,GAAU1pB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,iBAE3Dsf,GAAK1C,MAAMC,MAAMS,EAAK5jB,EAAIilB,EAAS,GAAKU,GAAQ/B,OAAO5jB,GACvD4lB,GAAK1C,MAAME,MAAMQ,EAAK3jB,EAAImlB,EAAsBH,GAAUU,GAAQ/B,OAAO3jB,GACzE2lB,GAAK1C,MAAMG,KAAKO,EAAK1jB,EAAIylB,GAAQ/B,OAAO1jB,GAExC4kB,EAAW3c,IAAIyd,IAGf,IAAIK,GAAehqB,KAAKqK,SAAS,iBAAiB8P,QAClD6P,GAAa/e,KAAO,cAEpB+e,GAAa3C,SAASpjB,EAAI+lB,GAAa3C,SAASpjB,EAAI,GAAKoO,KAAKiV,GAAK,IAEnE0C,GAAa3C,SAASrjB,EAAIgmB,GAAa3C,SAASrjB,EAAI,GAAKqO,KAAKiV,GAAK,IAGnE0C,GAAalmB,SAASqjB,KAClBrjB,EAASE,GACR2jB,EAAK3jB,EAAI,EAAImlB,GACdH,EACAH,EAAW/kB,SAASE,EAAI,GAG5BgmB,GAAalmB,SAASojB,KAAK,GAC3B8C,GAAalmB,SAASsjB,KAAK,GAE3BsC,GAAU1pB,KAAK+mB,YAAYC,cAAcgD,IACzC,IAAIC,GAAY,EAChBD,GAAa/C,MAAME,KACf,IAEJ6C,GAAa/C,MAAMG,KAAKO,EAAK5jB,EAAIkmB,GAAYP,GAAQ/B,OAAO5jB,GAC5DimB,GAAa/C,MAAMC,KAAKS,EAAK1jB,EAAIgmB,GAAYP,GAAQ/B,OAAO1jB,GAE5D+lB,GAAapc,SAAS,SAAUC,GAC5B,GAAIA,aAAiBpE,UAAY,CAC7BoE,EAAM5K,SACFmK,GAAK8c,2BAA2B,YAAYgB,IAChDrd,EAAM5K,SAAS4mB,YAAc,KAC7Bhc,EAAM0L,SAASuQ,YAGvBjB,EAAW3c,IAAI8d,IAIf,IAAIpD,GAAS5mB,KAAKqK,SAAS,kBAAkB8P,QAE7CyM,GAAO3b,KAAO,iBAEd2b,GAAO9iB,SAASojB,KAAK,GACrBN,GAAO9iB,SAASqjB,KACZrjB,EAASE,EAAI2jB,EAAK3jB,EAAI,EAAIklB,EAAgBL,EAAW/kB,SAASE,GAElE4iB,GAAO9iB,SAASsjB,MAAMkC,EAAe,GACrC1C,GAAOS,SAAStjB,EAAIsO,KAAKiV,GAAK,IAE9BoC,GAAU1pB,KAAK+mB,YAAYC,cAAcJ,IACzCA,GAAOK,MAAMC,MAAMS,EAAK5jB,EAAIqlB,GAAiBM,GAAQ/B,OAAO5jB,GAC5D6iB,GAAOK,MAAME,KAAKkC,EAAmBK,GAAQ/B,OAAO3jB,GACpD4iB,GAAOK,MAAMG,KAAKkC,EAAeI,GAAQ/B,OAAO1jB,GAEhD4kB,EAAW3c,IAAI0a,IAIf,IAAIvmB,GAAOL,KAAKqK,SAAS,kBAAkB8P,QAE3C9Z,GAAK4K,KAAO,eACZ5K,GAAKgrB,YAAc,GAEnBhrB,GAAKgnB,SAAStjB,GAAK,GAAKsO,KAAKiV,GAAK,IAElCjnB,GAAKyD,SAASojB,KAAK,GAEnB7mB,GAAKyD,SAASqjB,KACVrjB,EAASE,EACT2jB,EAAK3jB,EAAI,EACTklB,EACAL,EAAW/kB,SAASE,GACnBonB,IAAcprB,KAAKsrB,MACdP,GACAK,IAAcprB,KAAKurB,MAAQX,EAAgBC,GACjD,GAEJxqB,GAAKyD,SAASsjB,MAAMkC,GAEpBI,GAAU1pB,KAAK+mB,YAAYC,cAAc3mB,IACzCA,GAAK4mB,MAAMC,MACNS,EAAK5jB,EAAIqlB,EAAgBF,GAAiBQ,GAAQ/B,OAAO5jB,GAE9D1D,GAAK4mB,MAAMG,MACNgE,IAAcprB,KAAKsrB,MACdX,EACAS,IAAcprB,KAAKurB,MAAQX,EAAgBC,GACjDnB,GAAQ/B,OAAO3jB,GAInB6kB,EAAW3c,IAAI7L,IAIf,IAAI8pB,GAAQnqB,KAAKqK,SAAS,kBAAkB8P,QAE5CgQ,GAAMlf,KAAO,gBAGbkf,GAAM9C,SAASpjB,GAAK,GAAKoO,KAAKiV,GAAK,IACnC6C,GAAM9C,SAAStjB,EAAIsO,KAAKiV,GACxB6C,GAAMrmB,SAASojB,OAAOS,EAAK5jB,EAAI,GAAKilB,EAAS,GAC7CmB,GAAMrmB,SAASqjB,KACXrjB,EAASE,EACT2jB,EAAK3jB,EAAI,EACTklB,EACAL,EAAW/kB,SAASE,GACnBonB,IAAcprB,KAAKsrB,MACdP,GACAK,IAAcprB,KAAKurB,MAAQP,GAAiBC,IAClD,GAEJd,GAAMrmB,SAASsjB,MAAMkC,EAAe,GAEpCI,GAAU1pB,KAAK+mB,YAAYC,cAAcmD,IACzCA,GAAMlD,MAAMC,MACPkE,IAAcprB,KAAKsrB,MACdP,GACAK,IAAcprB,KAAKurB,MAAQP,GAAiBC,IAClDvB,GAAQ/B,OAAO3jB,GAEnBmmB,GAAMlD,MAAMG,KAAKoC,EAAeE,GAAQ/B,OAAO1jB,GAE/C4kB,EAAW3c,IAAIie,IAIf,IAAIC,GAAQpqB,KAAKqK,SAAS,kBAAkB8P,QAE5CiQ,GAAMnf,KAAO,gBAEbmf,GAAM/C,SAASpjB,EAAI,GAAKoO,KAAKiV,GAAK,IAClC8C,GAAM/C,SAAStjB,EAAIsO,KAAKiV,GACxB8C,GAAMtmB,SAASojB,KAAKS,EAAK5jB,EAAI,EAAIilB,EAAS,GAC1CoB,GAAMtmB,SAASqjB,KACXrjB,EAASE,EACT2jB,EAAK3jB,EAAI,EACTklB,EACAL,EAAW/kB,SAASE,GACnBonB,IAAcprB,KAAKsrB,MACdP,GACAK,IAAcprB,KAAKurB,MAAQP,GAAiBC,IAClD,GAEJb,GAAMtmB,SAASsjB,MAAMkC,EAAe,GAEpCI,GAAU1pB,KAAK+mB,YAAYC,cAAcoD,IAGzCA,GAAMnD,MAAMC,MACPkE,IAAcprB,KAAKsrB,MACdP,GACAK,IAAcprB,KAAKurB,MAAQP,GAAiBC,IAClDvB,GAAQ/B,OAAO3jB,GAEnBomB,GAAMnD,MAAMG,KAAKoC,EAAeE,GAAQ/B,OAAO1jB,GAE/C4kB,EAAW3c,IAAIke,IAEfpqB,KAAK0mB,MAAMjT,QAAQ6G,KAAKuO,GACxBA,EAAW/kB,SAASqjB,KAAKrjB,EAASE,EAAI2jB,EAAK3jB,EAAI,GAG/ChE,KAAKwrB,cAAcN,IAAY5Q,KAAKuO,GACpC7oB,KAAK6P,MAAM3D,IAAI2c,GACfhZ,EAAM3D,IAAI2c,OAEP,CACH,IAAIA,GAAa,IAAIpf,WACrB,IAAIoG,GAAQ7P,KAAK6P,MACjB,IAAIiZ,GAAqB,IACzB,IAAIC,GAAsB,GAC1B,IAAI0C,GAAqB,GACzB,IAAIC,GAAmB,GAEvB7C,GAAW5d,KAAO,eAClB,IAAI+d,GAAS,EACb,IAAIE,GAAgB,GACpB,IAAIC,GAAsB,GAC1B,IAAImB,GAAwB,EAC5B,IAAIC,GAAsB,GAC1B,IAAIC,GAAkB,GAEtB,IAAIpB,GAAgB,GACpB,IAAIC,GAAmB,GACvB,IAAIC,GAAetpB,KAAK0R,OAAS,IAAM,IAAM,IAE7C,IAAI+Y,GAAoB,GACxB,IAAIC,GAAiB,GACrB,IAAIC,GAAgB,IACpB,IAAIC,GAAgB,IACpB,IAAIC,GAAgB,IAAM,GAE1B,IAAIrB,GAAexpB,KAAK0R,OAAS,IAAM,IAAM,IAC7C,IAAIoZ,GAAkB,GACtB,IAAIC,GAAiB,IACrB,IAAIC,GAAiB,IACrB,IAAIC,GAAiB,IAAM,GAG3BpC,GAAW/kB,SAASojB,KAAKpjB,EAASC,GAClC8kB,GAAW/kB,SAASqjB,KAAKrjB,EAASE,GAClC6kB,GAAW/kB,SAASsjB,KAAKtjB,EAASG,EAAI,GAEtC,IAAIinB,GAAaC,EAAUrnB,EAASE,EAAGihB,GAEvC,IAAImG,GAAanG,EAAWiG,IAG5B,IAAIvB,GAAO3pB,KAAKqK,SAAS,QAAQ8P,QACjC,IAAI/M,GAAOpN,KACX2pB,GAAK/b,SAAS,SAAUC,GACpB,GAAIA,aAAiBpE,UAAY,KAKrCkgB,GAAK1e,KAAO,gBAGZ0e,GAAK7lB,SAASojB,KAAK,GACnByC,GAAK7lB,SAASqjB,KACVrjB,EAASE,EAAI6kB,GAAW/kB,SAASE,GAErC2lB,GAAK7lB,SAASsjB,KAAK,GAEnB,IAAIsC,GAAU1pB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,iBAE3Dsf,GAAK1C,MAAMC,MAAMS,EAAK5jB,EAAIilB,IAAUU,GAAQ/B,OAAO5jB,GACnD4lB,GAAK1C,MAAME,MAAMQ,EAAK3jB,EAAIglB,IAAUU,GAAQ/B,OAAO3jB,GACnD2lB,GAAK1C,MAAMG,KAAKO,EAAK1jB,EAAIylB,GAAQ/B,OAAO1jB,GAExC4kB,GAAW3c,IAAIyd,IAIf,IAAIC,GAAiB5pB,KAAKqK,SAAS,qBAAqB8P,QACxDyP,GAAe3e,KAAO,UAEtB2e,GAAevC,SAAStjB,GAAKsO,KAAKiV,GAAK,EACvCsC,GAAe9lB,SAASojB,KAAKpjB,EAASC,EAAI4jB,EAAK5jB,EAAI,EAAI8kB,GAAW/kB,SAASC,EAAI+kB,GAAqB,EAAIE,IACxGY,GAAe9lB,SAASqjB,KAAKQ,EAAK3jB,EAAI,EAAI+kB,GAAsB,GAChEa,GAAe9lB,SAASsjB,KAAKO,EAAK1jB,EAAI0hB,EAAe,EAAI,GAEzD+D,GAAU1pB,KAAK+mB,YAAYC,cAAc4C,IACzCA,GAAe3C,MAAME,KAAK,GAC1ByC,GAAe3C,MAAMC,KAAK,GAC1B0C,GAAe3C,MAAMG,KAAK,GAE1BwC,GAAehc,SAAS,SAAUC,GAC9B,GACIA,aAAiBpE,kBACVoE,EAAM5K,WAAa,YAC5B,CACE4K,EAAM5K,SAAS4mB,YAAc,KAC7Bhc,EAAM0L,SAASuQ,YAGvBjB,GAAW3c,IAAI0d,IAEf,IAAIG,GAAwB/pB,KAAKqK,SAAS,4BAA4B8P,QACtE4P,GAAsB9e,KAAO,iBAE7B8e,GAAsB1C,SAAStjB,GAAKsO,KAAKiV,GAAK,EAC9CyC,GAAsBjmB,SAASojB,KAAKpjB,EAASC,EAAI4jB,EAAK5jB,EAAI,EAAI8kB,GAAW/kB,SAASC,EAAI+kB,GAAqB,EAAIE,GAAS,GACxHe,GAAsBjmB,SAASqjB,KAAKQ,EAAK3jB,EAAI,EAAI+kB,GAAsB,GACvEgB,GAAsBjmB,SAASsjB,KAAKO,EAAK1jB,EAAI0hB,GAE7C+D,GAAU1pB,KAAK+mB,YAAYC,cAAc+C,IACzCA,GAAsB9C,MAAME,KAAK,GACjC4C,GAAsB9C,MAAMC,KAAK,GACjC6C,GAAsB9C,MAAMG,KAAK,GAEjCyB,GAAW3c,IAAI6d,IAIf,IAAInD,GAAS5mB,KAAKqK,SAAS,kBAAkB8P,QAE7CyM,GAAO3b,KAAO,iBAEd2b,GAAO9iB,SAASojB,KAAK,GACrBN,GAAO9iB,SAASqjB,KACZrjB,EAASE,EAAI2jB,EAAK3jB,EAAI,EAAIklB,GAAgBL,GAAW/kB,SAASE,GAElE4iB,GAAO9iB,SAASsjB,MAAMkC,GAAe,GACrC1C,GAAOS,SAAStjB,EAAIsO,KAAKiV,GAAK,IAE9BoC,GAAU1pB,KAAK+mB,YAAYC,cAAcJ,IACzCA,GAAOK,MAAMC,MAAMS,EAAK5jB,EAAIqlB,IAAiBM,GAAQ/B,OAAO5jB,GAC5D6iB,GAAOK,MAAME,KAAKkC,GAAmBK,GAAQ/B,OAAO3jB,GACpD4iB,GAAOK,MAAMG,KAAKkC,GAAeI,GAAQ/B,OAAO1jB,GAEhD4kB,GAAW3c,IAAI0a,IAIf,IAAIvmB,GAAOL,KAAKqK,SAAS,kBAAkB8P,QAE3C9Z,GAAK4K,KAAO,eAGZ5K,GAAKgnB,SAAStjB,GAAK,GAAKsO,KAAKiV,GAAK,IAElCjnB,GAAKyD,SAASojB,KAAK,GAEnB7mB,GAAKyD,SAASqjB,KACVrjB,EAASE,EACT2jB,EAAK3jB,EAAI,EACTklB,GACAL,GAAW/kB,SAASE,GACnBonB,IAAcprB,KAAKsrB,MACdP,GACAK,IAAcprB,KAAKurB,MAAQX,GAAgBC,IACjD,GAEJxqB,GAAKyD,SAASsjB,MAAMkC,IAEpBI,GAAU1pB,KAAK+mB,YAAYC,cAAc3mB,IACzCA,GAAK4mB,MAAMC,MACNS,EAAK5jB,EAAIqlB,GAAgBF,GAAgBF,GAAS,GAAKU,GAAQ/B,OAAO5jB,GAE3E1D,GAAK4mB,MAAMG,MACNgE,IAAcprB,KAAKsrB,MACdX,GACAS,IAAcprB,KAAKurB,MAAQX,GAAgBC,IACjDnB,GAAQ/B,OAAO3jB,GAInB6kB,GAAW3c,IAAI7L,IAIf,IAAI8pB,GAAQnqB,KAAKqK,SAAS,kBAAkB8P,QAE5CgQ,GAAMlf,KAAO,gBAGbkf,GAAM9C,SAASpjB,GAAK,GAAKoO,KAAKiV,GAAK,IACnC6C,GAAM9C,SAAStjB,EAAIsO,KAAKiV,GACxB6C,GAAMrmB,SAASojB,OAAOS,EAAK5jB,EAAI,GAAKilB,GAAS,GAC7CmB,GAAMrmB,SAASqjB,KACXrjB,EAASE,EACT2jB,EAAK3jB,EAAI,EACTklB,GACAL,GAAW/kB,SAASE,GACnBonB,IAAcprB,KAAKsrB,MACdP,GACAK,IAAcprB,KAAKurB,MAAQP,GAAiBC,IAClD,GAEJd,GAAMrmB,SAASsjB,MAAMkC,GAAe,GAEpCI,GAAU1pB,KAAK+mB,YAAYC,cAAcmD,IACzCA,GAAMlD,MAAMC,MACPkE,IAAcprB,KAAKsrB,MACdP,GACAK,IAAcprB,KAAKurB,MAAQP,GAAiBC,IAClDvB,GAAQ/B,OAAO3jB,GAEnBmmB,GAAMlD,MAAMG,KAAKoC,GAAeE,GAAQ/B,OAAO1jB,GAE/C4kB,GAAW3c,IAAIie,IAIf,IAAIC,GAAQpqB,KAAKqK,SAAS,kBAAkB8P,QAE5CiQ,GAAMnf,KAAO,gBAEbmf,GAAM/C,SAASpjB,EAAI,GAAKoO,KAAKiV,GAAK,IAClC8C,GAAM/C,SAAStjB,EAAIsO,KAAKiV,GACxB8C,GAAMtmB,SAASojB,KAAKS,EAAK5jB,EAAI,EAAIilB,GAAS,GAC1CoB,GAAMtmB,SAASqjB,KACXrjB,EAASE,EACT2jB,EAAK3jB,EAAI,EACTklB,GACAL,GAAW/kB,SAASE,GACnBonB,IAAcprB,KAAKsrB,MACdP,GACAK,IAAcprB,KAAKurB,MAAQP,GAAiBC,IAClD,GAEJb,GAAMtmB,SAASsjB,MAAMkC,GAAe,GAEpCI,GAAU1pB,KAAK+mB,YAAYC,cAAcoD,IAGzCA,GAAMnD,MAAMC,MACPkE,IAAcprB,KAAKsrB,MACdP,GACAK,IAAcprB,KAAKurB,MAAQP,GAAiBC,IAClDvB,GAAQ/B,OAAO3jB,GAEnBomB,GAAMnD,MAAMG,KAAKoC,GAAeE,GAAQ/B,OAAO1jB,GAE/C4kB,GAAW3c,IAAIke,IAEfpqB,KAAK0mB,MAAMjT,QAAQ6G,KAAKuO,IACxBA,GAAW/kB,SAASqjB,KAAKrjB,EAASE,EAAI2jB,EAAK3jB,EAAI,GAG/ChE,KAAKwrB,cAAcN,IAAY5Q,KAAKuO,IACpC7oB,KAAK6P,MAAM3D,IAAI2c,IACfhZ,GAAM3D,IAAI2c,IACVhZ,GAAMwa,kBA/hClB,CAAA9pB,IAAA,uBAAAZ,MAAA,SAAAgsB,EAmiCyB7R,EAAOkM,EAAWC,EAAW5D,GAC9C,IAAI6D,EACJ,IAAIrW,EAAQ7P,KAAK6P,MAEjB,UAAWmW,IAAc,YAAa,CAClCE,EAAMlmB,KAAKmmB,aAAaH,EAAWC,OAChC,CACHC,EAAM,CACFE,QAAS,KACTC,OAAQ,KACRC,WAAY,MAGpB,GAAItmB,KAAK0R,OAAS1R,KAAKumB,eAAgB,CACnCL,EAAIG,OAAS,KACbH,EAAIM,KAAO,MAEf,GAAIN,EAAIM,OAAS,KAAM,CACnB,OAAO,KAGX,IAAIoF,EAEJ,GAAI1F,EAAIE,QAAS,CACbwF,EAAO5rB,KAAKqK,SAAS,cAAc8P,YAChC,CACHyR,EAAO5rB,KAAK0mB,MAAMmF,eAAe/R,GAGrC,IAAI6M,EAAWtU,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,GAC3D,IAAIijB,GAASN,EAAW,GAAK,IAE7B,IAAImF,EAAS,EAEb,GAAI7F,EAAUW,OAAO7iB,EAAI,EAAG,CAExB+nB,GAAU,QACP,CAEHA,EAAS,IAGb,GAAIzJ,GAAW,EAAG,CACdyJ,GAAUA,EAGd,GAAI5F,EAAIG,OAAQ,CAEZ,IAAI0F,EAAgB/rB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,eACjEuhB,EAAKvE,SAAStjB,GAAKsO,KAAKiV,GAAK,EAC7BsE,EAAK3E,MAAME,KAAKnnB,KAAK0R,MAAQ,KAC7Bka,EAAK3E,MAAMG,KAAKH,GAGhB2E,EAAK9nB,SAASqjB,KAAKlB,EAAUW,OAAO5iB,EAAI,KACxC4nB,EAAK9nB,SAASojB,KAAKjB,EAAUW,OAAO7iB,EAAI+nB,GAG5C,GAAI5F,EAAII,WAAY,CAChBsF,EAAK9nB,SAASqjB,KAAKlB,EAAUW,OAAO5iB,EAAI,KACxC4nB,EAAK9nB,SAASojB,KAAKjB,EAAUW,OAAO7iB,EAAI+nB,GACxCF,EAAK3E,MAAME,KAAKnnB,KAAK0R,MAAQ,KAC7Bka,EAAK3E,MAAMG,KAAKH,GAEpB,GAAIf,EAAIE,QAAS,CACbwF,EAAK3gB,KAAO,kBACZjL,KAAK6P,MAAM3D,IAAI0f,GACf5rB,KAAK0mB,MAAMmF,eAAevR,KAAKsR,MAtmC3C,CAAArrB,IAAA,mCAAAZ,MAAA,SAAAqsB,EA4mCqC/F,GAC7B,IAAIC,EACJ,IAAIrW,EAAQ7P,KAAK6P,MAGjB,IAAIoc,EAAQjsB,KAAKqK,SAAS,mBAAmB8P,QAC7C8R,EAAM5E,SAAStjB,GAAKsO,KAAKiV,GAAK,EAE9B2E,EAAMnoB,SAASqjB,KAAKlB,EAAUjiB,GAC9BioB,EAAMnoB,SAASojB,KAAKjB,EAAUliB,GAG9B,IAAImoB,EAAWlsB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,oBAE5D4hB,EAAMhF,MAAME,KAAKnnB,KAAK0R,MAAQwa,EAASvE,OAAO3jB,GAG9CioB,EAAMhhB,KAAO,4BACbjL,KAAK6P,MAAM3D,IAAI+f,GACfjsB,KAAK0mB,MAAMyF,6BAA6B7R,KAAK2R,KA/nCrD,CAAA1rB,IAAA,yBAAAZ,MAAA,SAAAysB,EAkoC2BtS,EAAOkM,EAAWC,GACrC,IAAIC,EACJ,IAAIrW,EAAQ7P,KAAK6P,MACjB,UAAWmW,IAAc,YAAa,CAClCE,EAAMlmB,KAAKmmB,aAAaH,EAAWC,OAChC,CACHC,EAAM,CACFE,QAAS,KACTC,OAAQ,KACRC,WAAY,MAGpB,GAAItmB,KAAK0R,OAAS1R,KAAKumB,eAAgB,CACnCL,EAAIG,OAAS,KACbH,EAAIM,KAAO,MAEf,GAAIN,EAAIM,OAAS,KAAM,CACnB,OAAO,KAGX,IAAIoF,EAEJ,IAAIjF,EAAWtU,KAAKO,IAAIqT,EAAUW,OAAO7iB,EAAIkiB,EAAUY,IAAI9iB,GAE3D,GAAImiB,EAAIE,QAAS,CACbwF,EAAO5rB,KAAKqK,SAAS,cAAc8P,YAChC,CACHyR,EAAO5rB,KAAK0mB,MAAM2F,eAAevS,GAIrC,GAAIoM,EAAIG,OAAQ,CACZ,IAAIiG,EAAgBtsB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,eACjE,IAAI4c,EAAQN,EAAW2F,EAAc3E,OAAO5jB,EAC5C6nB,EAAK3E,MAAMC,KAAKD,GAChB2E,EAAKvE,SAAStjB,GAAKsO,KAAKiV,GAAK,EAE7B,GAAIpB,EAAIE,QAAS,CACbwF,EAAK3E,MAAMG,KAAK,GAEpBwE,EAAK3E,MAAME,KAAKnnB,KAAK0R,MAAQ4a,EAAc3E,OAAO3jB,GAElD4nB,EAAK9nB,SAASE,EAAI8V,EAASmM,EAAUW,OAAO5iB,EAAI,IAAQiiB,EAAUW,OAAO5iB,EAAK,IAC9E4nB,EAAK9nB,SAASojB,KACV7U,KAAKG,IAAIyT,EAAUW,OAAO7iB,EAAGkiB,EAAUY,IAAI9iB,GAAK4iB,EAAW,GAInE,GAAIT,EAAII,WAAY,CAEhBsF,EAAK9nB,SAASE,EAAI8V,EAASmM,EAAUW,OAAO5iB,EAAI,IAAQiiB,EAAUW,OAAO5iB,EAAK,IAC9E4nB,EAAK9nB,SAASojB,KACV7U,KAAKG,IAAIyT,EAAUW,OAAO7iB,EAAGkiB,EAAUY,IAAI9iB,GAAK4iB,EAAW,GAInE,GAAIT,EAAIE,QAAS,CACbwF,EAAK3gB,KAAO,OACZjL,KAAK6P,MAAM3D,IAAI0f,GACf5rB,KAAK0mB,MAAM2F,eAAe/R,KAAKsR,MA7rC3C,CAAArrB,IAAA,aAAAZ,MAAA,SAAA4sB,EAisCazoB,EAAU6jB,EAAM6E,EAAWC,EAAWxH,EAAY9Q,EAAYwU,EAAUM,GAKjF,GAAI9U,IAAe,EAAG,CACpB,IAAI0U,EAAa,IAAIpf,WACrBof,EAAW5d,KAAO,aAElB,IAAIygB,EAAmB,GAEvB,IAAIgB,EAAgBF,IAAc,EAAI,GAAK,GAC3C,IAAIG,EAAgBF,IAAc,EAAI,GAAK,EAC3C,IAAIzD,EAASC,EACb,IAAI2D,EACAJ,IAAc,EAAIxsB,KAAKqK,SAAS,cAAgBrK,KAAKqK,SAAS,gBAElEwe,EAAW/kB,SAASojB,KAAKpjB,EAASC,EAAI4oB,GAAiBhF,EAAK5jB,EAAI,IAChE8kB,EAAW/kB,SAASqjB,KAAKrjB,EAASE,EAAI2jB,EAAK3jB,EAAI,GAC/C6kB,EAAW/kB,SAASsjB,KAAKtjB,EAASG,GAElC,IAAI0lB,EAAO3pB,KAAKqK,SAAS,QAAQ8P,QACjC,IAAI+Q,EAAaC,EAAUrnB,EAASE,EAAGihB,GACvC,IAAI7X,EAAOpN,KACX2pB,EAAK1e,KAAO,QAEZ0e,EAAK7lB,SAASojB,KACVpjB,EAASC,EAAI4oB,EAAgBD,EAAgB,EAAI7D,EAAW/kB,SAASC,GAEzE4lB,EAAK7lB,SAASqjB,KAAK,GACnBwC,EAAK7lB,SAASsjB,KAAK,GAEnB,IAAIsC,EAAU1pB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,SAE3Dsf,EAAK1C,MAAME,MAAMQ,EAAK3jB,EAAIglB,GAAUU,EAAQ/B,OAAO3jB,GACnD2lB,EAAK1C,MAAMC,MAAMS,EAAK5jB,EAAI2oB,EAAgB1D,GAAUU,EAAQ/B,OAAO5jB,GACnE4lB,EAAK1C,MAAMG,KAAKO,EAAK1jB,EAAIylB,EAAQ/B,OAAO1jB,GAExC,GAAIjE,KAAK4oB,eAAiB,EAAG,CAC3BC,EAAW3c,IAAIyd,GAEjB3pB,KAAK0mB,MAAMlT,MAAM8G,KAAKqP,GAEtB,IAAIK,EAAe4C,EAAYzS,QAC/B6P,EAAa/e,KAAO,cAEpB+e,EAAalmB,SAASojB,KAClBpjB,EAASC,EACT4oB,GAAiBhF,EAAK5jB,EAAI,EAAI2oB,GAC9BC,EAAgB3D,EAChBH,EAAW/kB,SAASC,GAExB,GAAI4oB,IAAkB,EAAG,CACrB3C,EAAalmB,SAASC,GAAKilB,EAAS,EAGxCgB,EAAalmB,SAASqjB,KAAK,GAC3B6C,EAAalmB,SAASsjB,KAAK,GAC3BsC,EAAU1pB,KAAK+mB,YAAYC,cAAc4F,GACzC5C,EAAa/C,MAAME,MAAMQ,EAAK3jB,EAAIglB,GAAUU,EAAQ/B,OAAO3jB,GAC3DgmB,EAAa/C,MAAMC,MACdwF,EAAgBhB,GAAoBhC,EAAQ/B,OAAO5jB,GAExDimB,EAAa/C,MAAMG,KAAK,GAExB4C,EAAapc,SAAS,SAAUC,GAC5B,GACIA,aAAiBpE,kBACVoE,EAAM5K,WAAa,YAC5B,CACE4K,EAAM5K,SACFmK,EAAK8c,2BAA2B,YAAYgB,GAChDrd,EAAM5K,SAAS4mB,YAAc,KAC7B,GAAI8C,EAAgB,EAAG,CACnB9e,EAAMwZ,SAASrjB,EAAI6J,EAAMwZ,SAASrjB,EAAI,IAAMqO,KAAKiV,GAAK,IAE1DzZ,EAAM0L,SAASuQ,YAGrB,GAAI9pB,KAAK4oB,eAAiB,EAAG,CACxBC,EAAW3c,IAAI8d,GAEtBhqB,KAAK0mB,MAAMlT,MAAM8G,KAAK0P,GAGtBnB,EAAWgE,YAAcF,EAGzB,GAAIhE,GAAY3oB,KAAK4oB,eAAiB,EAAG,CACrCC,EAAWxB,SAASrjB,GAAK4hB,EAAgBiD,EAAWgE,YAAcxa,KAAKiV,GAAK,IAEhFtnB,KAAK8sB,WAAW5B,GAAY5Q,KAAKuO,GACjC7oB,KAAK0mB,MAAMqG,YAAYzS,KAAKuO,GAC5B7oB,KAAK6P,MAAM3D,IAAI2c,OACV,CAEL,IAAIA,EAAa,IAAIpf,WACrBof,EAAW5d,KAAO,aAClB,IAAI6d,EAAqB,IACzB,IAAIC,EAAsB,GAC1B,IAAI0C,EAAqB,GACzB,IAAIC,EAAmB,GAEvB,IAAIgB,EAAgB,EACpB,IAAIC,EAAgBF,IAAc,EAAI,GAAK,EAC3C,IAAIzD,EAASC,EAGbJ,EAAW/kB,SAASojB,KAAKpjB,EAASC,EAAI4oB,GAAiBhF,EAAK5jB,EAAI,IAChE8kB,EAAW/kB,SAASqjB,KAAKrjB,EAASE,EAAI2jB,EAAK3jB,EAAI,GAC/C6kB,EAAW/kB,SAASsjB,KAAKtjB,EAASG,GAElC,IAAI0lB,EAAO3pB,KAAKqK,SAAS,QAAQ8P,QACjC,IAAI+Q,EAAaC,EAAUrnB,EAASE,EAAGihB,GACvC,IAAI7X,EAAOpN,KACX2pB,EAAK1e,KAAO,QAEZ0e,EAAK7lB,SAASojB,KACVpjB,EAASC,EAAI4oB,EAAgB9D,EAAW/kB,SAASC,GAErD4lB,EAAK7lB,SAASqjB,KAAK,GACnBwC,EAAK7lB,SAASsjB,KAAK,GAEnB,IAAIsC,EAAU1pB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,SAE3Dsf,EAAK1C,MAAMC,MAAMS,EAAK5jB,EAAIilB,GAAUU,EAAQ/B,OAAO5jB,GACnD4lB,EAAK1C,MAAME,MAAMQ,EAAK3jB,EAAIglB,GAAUU,EAAQ/B,OAAO3jB,GACnD2lB,EAAK1C,MAAMG,KAAKO,EAAK1jB,EAAIylB,EAAQ/B,OAAO1jB,GAExC,GAAIjE,KAAK4oB,eAAiB,EAAG,CAC3BC,EAAW3c,IAAIyd,GAEjB3pB,KAAK0mB,MAAMlT,MAAM8G,KAAKqP,GACtB,GAAGgD,KAAmB,EAAG,CACrB,IAAI3C,EAAehqB,KAAKqK,SAAS,qBAAqB8P,QACtD6P,EAAa/e,KAAO,UAEpB+e,EAAa3C,SAAStjB,GAAKsO,KAAKiV,GAAK,EACrC0C,EAAalmB,SAASojB,KAAKpjB,EAASC,EAAI4jB,EAAK5jB,EAAI,EAAI8kB,EAAW/kB,SAASC,EAAI+kB,EAAqB,EAAIE,GACtGgB,EAAalmB,SAASqjB,KAAKQ,EAAK3jB,EAAI,EAAI+kB,EAAsB,GAC9DiB,EAAalmB,SAASsjB,KAAKO,EAAK1jB,EAAI0hB,EAAe,EAAI,GAEvD+D,EAAU1pB,KAAK+mB,YAAYC,cAAcgD,GACzCA,EAAa/C,MAAME,KAAK,GACxB6C,EAAa/C,MAAMC,KAAK,GACxB8C,EAAa/C,MAAMG,KAAK,GAExB4C,EAAapc,SAAS,SAAUC,GAC5B,GACIA,aAAiBpE,kBACVoE,EAAM5K,WAAa,YAC5B,CACE4K,EAAM5K,SAAS4mB,YAAc,KAC7Bhc,EAAM0L,SAASuQ,YAGvB,GAAI9pB,KAAK4oB,eAAiB,EAAG,CAC7BC,EAAW3c,IAAI8d,GAEfhqB,KAAK0mB,MAAMlT,MAAM8G,KAAK0P,GAEtB,IAAIgD,EAAsBhtB,KAAKqK,SAAS,4BAA4B8P,QACpE6S,EAAoB/hB,KAAO,iBAE3B+hB,EAAoB3F,SAAStjB,GAAKsO,KAAKiV,GAAK,EAC5C0F,EAAoBlpB,SAASojB,KAAKpjB,EAASC,EAAI4jB,EAAK5jB,EAAI,EAAI8kB,EAAW/kB,SAASC,EAAI+kB,EAAqB,EAAIE,EAAS,GACtHgE,EAAoBlpB,SAASqjB,KAAKQ,EAAK3jB,EAAI,EAAI+kB,EAAsB,GACrEiE,EAAoBlpB,SAASsjB,KAAKO,EAAK1jB,EAAI,EAAI,GAE/CylB,EAAU1pB,KAAK+mB,YAAYC,cAAcgG,GACzCA,EAAoB/F,MAAME,KAAK,GAC/B6F,EAAoB/F,MAAMC,KAAK,GAC/B8F,EAAoB/F,MAAMG,KAAK,GAE/B4F,EAAoBpf,SAAS,SAAUC,GACnC,GACIA,aAAiBpE,kBACVoE,EAAM5K,WAAa,YAC5B,CACE4K,EAAM5K,SAAS4mB,YAAc,KAC7Bhc,EAAM0L,SAASuQ,YAGvBjB,EAAW3c,IAAI8gB,GACfhtB,KAAK0mB,MAAMlT,MAAM8G,KAAK0S,GAI1BnE,EAAWgE,YAAcF,EAE1B,GAAIhE,GAAY3oB,KAAK4oB,eAAiB,EAAG,CACpCC,EAAWxB,SAASrjB,GAAK4hB,EAAgBiD,EAAWgE,YAAcxa,KAAKiV,GAAK,IAGhFtnB,KAAK8sB,WAAW5B,GAAY5Q,KAAKuO,GACjC7oB,KAAK0mB,MAAMqG,YAAYzS,KAAKuO,GAC5B7oB,KAAK6P,MAAM3D,IAAI2c,MAr4CrB,CAAAtoB,IAAA,eAAAZ,MAAA,SAAAstB,EAw4CiBhH,EAAWiH,GACpB,IAAI3T,EAAW,IAAI9P,iBAAmB,GAAI,GAAI,GAC9C,IAAIxG,EAAW,IAAIwG,uBAAyB,CAACrG,MAAQ6iB,EAAUkH,KAAOlH,EAAUkH,IAAIxE,SAAW,SAAW,WAC1G,IAAIyE,EAAS,IAAI3jB,UAAY8P,EAAUtW,GAEvCmqB,EAAOtpB,SAASmkB,KACXhC,EAAUW,OAAO7iB,EAAIkiB,EAAUY,IAAI9iB,GAAG,GACtCkiB,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,GAAG,GACtCiiB,EAAUW,OAAO3iB,EAAIgiB,EAAUY,IAAI5iB,GAAG,GAE3CmpB,EAAOniB,KAAO,UAEdmiB,EAAOC,QAAU,MAEjBrtB,KAAK6P,MAAM3D,IAAIkhB,GACf,GAAIptB,KAAK0mB,MAAM,WAAallB,UAAW,CACnCxB,KAAK0mB,MAAM,SAAW,GAE1B1mB,KAAK0mB,MAAM,SAASpM,KAAK8S,KA15CjC,CAAA7sB,IAAA,+BAAAZ,MAAA,SAAAgQ,IA+5CQ,IAAI4J,EAAW,IAAI9P,iBAAmB,IAAM,IAAM,GAClD,IAAIxG,EAAW,IAAIwG,uBACnBxG,EAASG,MAAQ,IAAIqG,WAAY,UACjCxG,EAASO,QAAU,EACnBP,EAAS6K,YAAc,KACvB,IAAIwf,EAAM,IAAI7jB,UAAY8P,EAAUtW,GAEpC,IAAIjD,KAAKutB,OAAQ,CACbvtB,KAAKutB,OAAS,IAAI9jB,WAEtBzJ,KAAKwtB,UAAYF,EACjBttB,KAAKutB,OAAOrhB,IAAIohB,KA16CxB,CAAA/sB,IAAA,0BAAAZ,MAAA,SAAA8tB,EA66C4Bpc,GAEpB,IAAIM,EAAQN,EAAO2B,GAAK3B,EAAO0B,GAAK1B,EAAO0B,GAAK1B,EAAO2B,GAAK3B,EAAO2B,GAAI3B,EAAO0B,GAC9E,IAAIY,EAAStC,EAAOwB,GAAK,EACzB,IAAInB,EAAQL,EAAOuE,GAAK,GAExB,IAAI2D,EAAW,IAAI9P,iBAAmBkI,EAAOgC,EAAQjC,GACrD,IAAIzO,EAAW,IAAIwG,uBACnBxG,EAASG,MAAQ,IAAIqG,WAAY,UACjCxG,EAASO,QAAU,EACnBP,EAAS6K,YAAc,KAEvB,IAAIwf,EAAM,IAAI7jB,UAAY8P,EAAUtW,GAIpCqqB,EAAIxpB,SAASmkB,IACV5W,EAAO0B,GAAGpB,EAAM,EAAG,EAAGN,EAAOuE,GAAG,GAGnC,IAAI5V,KAAK0tB,OAAQ,CACb1tB,KAAK0tB,OAAS,IAAIjkB,WAEtBzJ,KAAK0tB,OAAOxhB,IAAIohB,GAEhBA,EAAI/X,GAAKvV,KAAK2tB,oBAAoBrrB,OAClCtC,KAAK2tB,oBAAoBrT,KAAKgT,KAv8CtC,CAAA/sB,IAAA,wBAAAZ,MAAA,SAAAiuB,EA28C0B3H,EAAWiH,GAC7B,IAAIpF,EAAU9nB,KAAKqK,SAAS,UAAU8P,QAEtC,IAAI4N,EAAa/nB,KAAK+mB,YAAYC,cAAchnB,KAAKqK,SAAS,WAE9D,IAAIod,EAAQO,EAAQN,EACpBI,EAAQT,SAAStjB,GAAKsO,KAAKiV,GAAK,EAShCU,EACI3V,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,GAAK+jB,EAAWJ,OAAO5jB,EAEvE0jB,EACIpV,KAAKO,IAAIqT,EAAUW,OAAO7iB,EAAIkiB,EAAUY,IAAI9iB,GAAKgkB,EAAWJ,OAAO5jB,EAEvE2jB,EACIrV,KAAKO,IAAIqT,EAAUW,OAAO3iB,EAAIgiB,EAAUY,IAAI5iB,GAAK8jB,EAAWJ,OAAO5jB,EAEvE+jB,EAAQb,MAAMG,KAAKY,GACnBF,EAAQb,MAAMC,KAAKO,GACnBK,EAAQb,MAAME,KAAKO,GAEnBK,EAAa/nB,KAAK+mB,YAAYC,cAAcc,GAE5CA,EAAQhkB,SAASmkB,IACb5V,KAAKG,IAAIyT,EAAUW,OAAO7iB,EAAGkiB,EAAUY,IAAI9iB,GAAKgkB,EAAWJ,OAAO5jB,EAAI,EACtEsO,KAAKG,IAAIyT,EAAUW,OAAO5iB,EAAGiiB,EAAUY,IAAI7iB,GAAKqO,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,GAAK,EACjGqO,KAAKO,IAAIqT,EAAUW,OAAO3iB,EAAIgiB,EAAUY,IAAI5iB,GAAK,GAErD6jB,EAAQ7c,KAAO,QACfjL,KAAK6P,MAAM3D,IAAI4b,GACf,GAAI9nB,KAAK0mB,MAAM,WAAallB,UAAW,CACnCxB,KAAK0mB,MAAM,SAAW,GAE1B1mB,KAAK0mB,MAAM,SAASpM,KAAKwN,OAn/CjC,OAAAjC,EAAA,GAu/CA,SAASsF,EAAU0C,EAASC,GACxB,IAAIC,EAAM,EACV,IAAIC,EAAM,EACV,QAASD,EAAMF,GAAWE,EAAMD,EAASE,GAAOH,GAAU,CACtDE,GAAOD,EAASE,GAChBA,IAEA,GAAIA,GAAO,GAAI,CACX,OAAQ,GAGhB,OAAOA,yCClgDXvkB,MAAQA,OAAS,GAGjB,IAAIwkB,EAAa,oOAUjB,IAAMC,EAAe,g4CAuCrB,IAAI7jB,EAAW,KACf,IAAI0c,EAAc,IAAItd,MAAM0kB,KAC5B,IAAIC,EAAa,IACjB,IAAIzI,EAAe,GACnB,IAAI0I,EAAoB,KACxB,IAAIC,EAAkB,MAEtB,IAAIC,EAAY,GAEhB,IAAIC,EACJ,IAAIC,EAEJ,IAAIrrB,EACJ,IAAII,EACJ,IAAIH,EACJ,IAAIC,EACJ,IAAIC,EACJ,IAAIE,EACJ,IAAIC,EACJ,IAAIP,EAEJ,IAAIurB,EAAiB,EACrB,IAAIC,EAAa,EAEjB,GAAInkB,OAAO8L,MAAQ9L,OAAO8L,KAAKC,KAAM,CACnCmY,EAAiBlkB,OAAO8L,KAAKC,KAAKtT,SAEpC,GAAIuH,OAAO8L,MAAQ9L,OAAO8L,KAAKC,KAAM,CACnCoY,EAAankB,OAAO8L,KAAKC,KAAKpC,iBAY1BE,eAA0Bqa,EAAgBC,mBAR9CxrB,QACAC,QACAI,QACAH,QACAC,QACAC,QACAE,QACAC,YAII6L,wBAEJ,SAAAA,EACEqf,EACA9J,EACA+J,EACAC,EACAC,GACA,IAAApkB,EAAAP,IAAApK,KAAAuP,GACA5E,EAAAqkB,IAAAhvB,KAAAivB,IAAA1f,GAAA3P,KAAAI,OACA2K,EAAKukB,QAAUJ,EACfnkB,EAAKie,cAAgB,EAErBje,EAAKwkB,mBAAqBN,EAC1BlkB,EAAKN,SAAWukB,EAChBvkB,EAAWM,EAAKN,SAEhBM,EAAKma,SAAWA,EAChBna,EAAKoc,YAAcA,EACnBpc,EAAKkF,MAAQ,KACblF,EAAKokB,SAAWA,EAChBpkB,EAAKykB,eAAiB,KAEtBzkB,EAAK0kB,mBAAqB,GAE1B1kB,EAAK2gB,MAAQ,IACb3gB,EAAK4gB,MAAQ,IACb5gB,EAAK2kB,MAAQ,IACb3kB,EAAKwJ,WAAawa,EAElBhkB,EAAKgjB,oBAAsB,GAE3BhjB,EAAKuf,2BAA6B,CAChC1W,MAAS,GACT+b,SAAY,GACZjnB,QAAW,GACX8K,MAAS,IAEXzI,EAAK+b,MAAQ,CACX5U,UAAW,GACXD,YAAa,GACbE,SAAU,GACVzJ,QAAS,CAAC,GAAI,GAAI,GAAI,GAAI,IAC1BknB,YAAa,GACbhH,KAAM,GACNhV,MAAO,GACPuZ,YAAa,GACb3Z,MAAO,GACPqc,MAAO,GACPhc,QAAS,GACT4Y,eAAgB,GAChBR,eAAgB,GAChBM,6BAA8B,GAC9BuD,sBAAuB,GACvBC,OAAQ,IAGVhlB,EAAK0G,OAAS,GAEd1G,EAAK4kB,SAAW,GAEhB5kB,EAAK+G,MAAQ,IACb/G,EAAK4b,eAAiB,IACtB5b,EAAKilB,cAAgB,MACrBjlB,EAAKgH,MAAQ,KAEbhH,EAAKklB,oBAAsB,EAC3BllB,EAAKmlB,iBAAmB,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAIvD,IAAIC,EAAc,IAAItmB,MAAMumB,YAAY,EAAG,EAAG,GAC9C,IAAIC,EAAc,IAAIxmB,MAAMymB,kBAAkB,CAC5C9sB,MAAO,SACP+sB,UAAW,QAEb9lB,EAAS,SAAW,IAAIZ,MAAMgW,KAAKsQ,EAAaE,GAEhD5lB,EAAS,SAASghB,YAAc,GAGhC0E,EAAc,IAAItmB,MAAMumB,YAAY,EAAG,EAAG,GAC1CC,EAAc,IAAIxmB,MAAMymB,kBAAkB,CACxC9sB,MAAO,QACP+sB,UAAW,QAEb9lB,EAAS,eAAiB,IAAIZ,MAAMgW,KAAKsQ,EAAaE,GAGtD,IAAIG,EAAe,IAAI3mB,MAAMumB,YAAY,EAAG,EAAG,GAC/C,IAAIK,EAAe,IAAI5mB,MAAMymB,kBAAkB,CAC7C9sB,MAAO,QACP+sB,UAAW,QAEb9lB,EAAS,WAAa,IAAIZ,MAAMgW,KAAK2Q,EAAcC,GAtFnD,OAAA1lB,0CAiGA,IAAIN,EAAWrK,KAAKqK,SACpBG,OAAOH,SAAWA,EAChB,IAAK,IAAIjI,EAAI,EAAGA,EAAI,GAAIA,IAAK,CAC3B,IAAIa,EAAW,IAAIwG,MAAM6mB,eAAe,CACtCC,aAActC,EACduC,eAAgBtC,EAChBuC,SAAU,CACRC,WAAY,CACVrvB,KAAM,IACN1B,MAAO0K,EAAS,eAElBsmB,GAAI,CACFtvB,KAAM,IACN1B,MAAO0K,EAAS,gBAElBumB,eAAgB,CACdvvB,KAAM,IACN1B,MAAO,GAETkxB,eAAgB,CACdxvB,KAAM,IACN1B,MAAO,IAAI8J,MAAMqnB,QAAQ,EAAK,EAAK,OAIzC9wB,KAAKkqB,2BAA2B,SAAS5P,KAAKrX,GAE9CA,EAAW,IAAIwG,MAAM6mB,eAAe,CAClCC,aAActC,EACduC,eAAgBtC,EAChBuC,SAAU,CACRC,WAAY,CACVrvB,KAAM,IACN1B,MAAO0K,EAAS,eAElBsmB,GAAI,CACFtvB,KAAM,IACN1B,MAAO0K,EAAS,gBAElBumB,eAAgB,CACdvvB,KAAM,IACN1B,MAAO,GAETkxB,eAAgB,CACdxvB,KAAM,IACN1B,MAAO,IAAI8J,MAAMqnB,QAAQ,EAAK,EAAK,OAIzC9wB,KAAKkqB,2BAA2B,YAAY5P,KAAKrX,GAEnD,GAAIjD,KAAK4oB,eAAiB,EAAG,CAC3B5oB,KAAK6P,MAAQ,IAAIpG,MAAMsnB,MAG3BtC,EAAiBpkB,EAAS,WAC1B,IAAI/B,EAAU,CAAC,SAAU,cAAe,gBACxC,IAAK,IAAIlG,EAAI,EAAGA,EAAIkG,EAAQhG,OAAQF,IAAK,CACvCiI,EAAS/B,EAAQlG,IAAIwL,SAAS,SAASC,GACrC,GAAIA,aAAiBpE,MAAMgW,KAAM,CAE/B5R,EAAM5K,SAAS6K,YAAc,KAC7BD,EAAM5K,SAASO,QAAU,EACzBqK,EAAM5K,SAAS8K,KAAOtE,MAAMunB,UAC5BnjB,EAAM5K,SAASguB,OAAUxC,EAEzB5gB,EAAM5K,SAASiuB,aAAeztB,KAGlC4G,EAAS/B,EAAQlG,IAAIipB,YAAc,EAGrC/iB,EAAU,CACR,oBACA,mBACA,sBAEF,IAAK,IAAIlG,EAAI,EAAGA,EAAIkG,EAAQhG,OAAQF,IAAK,CACvCiI,EAAS/B,EAAQlG,IAAIwL,SAAS,SAASC,GACrC,GAAIA,aAAiBpE,MAAMgW,KAAM,CAC/B5R,EAAM5K,SAAS6K,YAAc,KAC7BD,EAAM5K,SAASO,QAAU,MAI7B6G,EAAS/B,EAAQlG,IAAIipB,YAAc,GAErChhB,EAAS,QAAQuD,SAAS,SAASC,GACjC,GAAIA,aAAiBpE,MAAMgW,KAAM,CAC7B,GAAGjV,OAAO8L,MAAQ9L,OAAO8L,KAAKC,MAAS/L,OAAO8L,KAAKC,KAAKpC,aAAe,GAAKG,SAAS9J,OAAO8L,KAAKC,KAAKtT,YAAc,EAAI,CACpH4K,EAAM5K,SAASuI,IAAMnB,EAAS,oBAElCwD,EAAMwZ,SAAStjB,EAAIsO,KAAKiV,GAAK,EAC7BzZ,EAAMwZ,SAASpjB,GAAKoO,KAAKiV,GAAK,EAC9BzZ,EAAM0L,SAASuQ,SACfjc,EAAM5K,SAASguB,OAASxC,EACxB5gB,EAAM5K,SAASiuB,aAAextB,EAC9BmK,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM9tB,MAI7CgH,EAAS,QAAQghB,YAAc,GAE/BhhB,EAAS,gBAAgBuD,SAAS,SAASC,GACzC,GAAIA,aAAiBpE,MAAMgW,KAAM,CAC/B,GAAGjV,OAAO8L,MAAQ9L,OAAO8L,KAAKC,MAAS/L,OAAO8L,KAAKC,KAAKpC,aAAe,GAAKG,SAAS9J,OAAO8L,KAAKC,KAAKtT,YAAc,EAAI,CACtH4K,EAAM5K,SAASuI,IAAMnB,EAAS,oBAEhCwD,EAAMwZ,SAAStjB,EAAIsO,KAAKiV,GAAK,EAC7BzZ,EAAMwZ,SAASpjB,GAAKoO,KAAKiV,GAAK,EAC9BzZ,EAAM0L,SAASuQ,SACfjc,EAAM5K,SAASguB,OAAUxC,EACzB5gB,EAAM5K,SAASiuB,aAAextB,EAC9BmK,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM9tB,MAG3CgH,EAAS,gBAAgBghB,YAAc,GAEvChhB,EAAS,YAAYuD,SAAS,SAASC,GACrC,GAAIA,aAAiBpE,MAAMgW,KAAM,CAC/B5R,EAAM5K,SAASguB,OAAUxC,EAEzB5gB,EAAM5K,SAASiuB,aAAeztB,KAKlC4G,EAAS,WAAWuD,SAAS,SAASC,GACpC,GAAIA,aAAiBpE,MAAMgW,KAAM,CAC/B5R,EAAM5K,SAASguB,OAAUxC,EAEzB5gB,EAAM5K,SAASiuB,aAAeztB,KAKlC4G,EAAS,UAAUuD,SAAS,SAASC,GACnC,GAAIA,aAAiBpE,MAAMgW,KAAM,CAC/B5R,EAAM5K,SAASguB,OAAUxC,EAEzB5gB,EAAM5K,SAASiuB,aAAeztB,KAIlC4G,EAAS,WAAWghB,YAAc,GAElChhB,EAAS,kBAAkBuD,SAAS,SAASC,GAC3C,GAAIA,aAAiBpE,MAAMgW,KAAM,CAC/B5R,EAAM5K,SAASguB,OAAUxC,EAEzB5gB,EAAM5K,SAASiuB,aAAeztB,KAIlC4G,EAAS,kBAAkBghB,YAAc,GAEzChhB,EAAS,YAAYghB,YAAc,EACnChhB,EAAS,cAAcuD,SAAS,SAASC,GACvC,GAAIA,aAAiBpE,MAAMgW,KAAM,CAG/B5R,EAAM5K,SAASmuB,cAAgB,KAC/BvjB,EAAM5K,SAASouB,oBAAsB,EACrCxjB,EAAM5K,SAASquB,oBAAsB,EACrCzjB,EAAM5K,SAASguB,OAAUxC,EAEzB5gB,EAAM5K,SAASiuB,aAAeztB,KAGlC4G,EAAS,cAAcghB,YAAc,GAErChhB,EAAS,cAAcuD,SAAS,SAASC,GACvC,GAAIA,aAAiBpE,MAAMgW,KAAM,CAC/B5R,EAAMwZ,SAAStjB,GAAKsO,KAAKiV,GAAK,EAC9BzZ,EAAM5K,SAASguB,OAAUxC,EAEzB5gB,EAAM5K,SAASiuB,aAAeztB,KAIlC4G,EAAS,SAASuD,SAAU,SAAWC,GACnC,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM5tB,GACvCsK,EAAM5K,SAASiuB,aAAeztB,KAGtC4G,EAAS,SAASghB,YAAc,GAEhChhB,EAAS,cAAcghB,YAAc,GAErChhB,EAAS,gBAAgBuD,SAAS,SAASC,GACzC,GAAIA,aAAiBpE,MAAMgW,KAAM,CAC/B5R,EAAMwZ,SAAStjB,GAAKsO,KAAKiV,GAAK,EAC5BzZ,EAAM5K,SAASguB,OAAUxC,EAEzB5gB,EAAM5K,SAASiuB,aAAeztB,KAGpC4G,EAAS,gBAAgBghB,YAAc,GAEvChhB,EAAS,qBAAqBuD,SAAS,SAASC,GAC9C,GAAIA,aAAiBpE,MAAMgW,KAAM,CAE/B5R,EAAM5K,SAASuI,IAAMnB,EAAS,gCAMlCA,EAAS,qBAAqBghB,YAAc,KAE5ChhB,EAAS,4BAA4BuD,SAAS,SAASC,GACrD,GAAIA,aAAiBpE,MAAMgW,KAAM,CAC/B5R,EAAM5K,SAASuI,IAAMnB,EAAS,6BAC9BwD,EAAM5K,SAAS6K,YAAc,KAC7BD,EAAM5K,SAASsuB,UAAa,KAC5B1jB,EAAM5K,SAASuuB,WAAa,MAC5B3jB,EAAM5K,SAASmuB,cAAgB,KAC/BvjB,EAAM5K,SAASouB,oBAAsB,EACrCxjB,EAAM5K,SAASquB,oBAAsB,KAGzCjnB,EAAS,4BAA4BghB,YAAc,IAEnDhhB,EAAS,cAAcuD,SAAS,SAASC,GACvC,GAAGA,aAAiBpE,MAAMgW,KAAK,CAI7B5R,EAAM5K,SAASguB,OAAUxC,EAIzB5gB,EAAM5K,SAASiuB,aAAeztB,KAIlC4G,EAAS,cAAcuD,SAAS,SAASC,GACvC,GAAGA,aAAiBpE,MAAMgW,KAAK,CAC7B5R,EAAM5K,SAASmuB,cAAgB,KAC/BvjB,EAAM5K,SAASouB,oBAAsB,EACrCxjB,EAAM5K,SAASquB,oBAAsB,EACrCzjB,EAAM5K,SAASguB,OAAUxC,EACzB5gB,EAAM5K,SAASiuB,aAAeztB,0CAMlC,OAAOzD,KAAK6P,yCAELA,GACP7P,KAAK6P,MAAQA,EACb7P,KAAK6P,MAAM4hB,WAAa,IAAIhoB,MAAM0nB,MAAM,UACxCnxB,KAAK6P,MAAMrM,QAAU,gDAEJqM,GACjB7P,KAAK6P,MAAQA,EACb7P,KAAK6P,MAAM4hB,WAAa,IAAIhoB,MAAM0nB,MAAM,UACxCnxB,KAAK6P,MAAMrM,QAAU,0CAGrBxD,KAAKqR,OAAS,CACVmC,MAAO,GACP3B,YAAa,GACb2W,KAAM,GACN1W,UAAW,GACXC,SAAU,GACV2f,oBAAqB,CACnBC,YAAa,GACbC,cAAe,GACfC,aAAc,GACdC,YAAa,KAGnB9xB,KAAK6P,MAAMkiB,OAAO3Q,MAAMphB,KAAK6P,MAAO7P,KAAK6P,MAAMmiB,UAC/ChyB,KAAK6P,MAAM4hB,WAAa,IAAIhoB,MAAM0nB,MAAM,UACxCnxB,KAAK6P,MAAMrM,QAAU,kDAGFyuB,EAAYpiB,GAG9BoiB,EAAW5F,eAAiBrsB,KAAKkyB,yBAAyBD,GAC1DA,EAAWpG,eAAiB7rB,KAAKmyB,yBAAyBF,GAC1DA,EAAW9F,6BAA+BnsB,KAAKoyB,qCAAqCH,GAKnFjyB,KAAKqyB,UAAUJ,EAAY,GAAIpiB,EAAO,MACtC2e,EAAUyD,uDAGaK,GACzB,IAAI7C,EAAQzvB,KAAK0mB,MAAM,SAAS9jB,OAAO,SAAA0qB,GAAG,OAAIA,EAAIriB,MAAQ,YAAWrI,OAAO2vB,SAC5E,OAAO9C,EAAMjkB,IAAI,SAAA8hB,GAAG,OAAIgF,EAAehF,EAAMA,EAAIxpB,+CAGzCuN,EAAQmhB,EAAK3iB,EAAOwV,GAAU,IAAAtZ,EAAA/L,KACtC,UAAWqR,EAAOoe,OAAS,YAAa,CACtCpe,EAAOoe,MAAQ,GAGjB,IAAIgD,EAAiBC,GAAgB,YAAc,EACnD,IAAIC,EAAoB,CACtBf,cAAe,EACfD,YAAa,EACbE,aAAc,EACdC,YAAa,GAIf,IAAK,IAAI1vB,EAAI,EAAGA,EAAIpC,KAAK0mB,MAAM+I,MAAMntB,OAAQF,IAAK,CAChDyN,EAAMkiB,OAAO/xB,KAAK0mB,MAAM+I,MAAMrtB,IAGhCpC,KAAKgW,2BAA2B,MAAMxK,IAAI,SAAA8hB,GACxCzd,EAAMkiB,OAAOzE,KAIf,GAAGjc,EAAOuhB,WAAY,CACpB5yB,KAAK2tB,oBAAoBniB,IAAI,SAAC8hB,GAAD,OAASzd,EAAMkiB,OAAOzE,KACnDttB,KAAK2tB,oBAAsB,GAC3Btc,EAAOuhB,WAAWpnB,IAAIxL,KAAKytB,wBAAwBpT,KAAKra,OAG1D6yB,EAAEd,OAAO/xB,KAAK0mB,MAAM,SAAU,SAACpR,GAAD,OAAYA,EAAOrK,MAAQ,YAGzD,IAAK,IAAIuD,EAAI,EAAGA,EAAIikB,EAAgBjkB,IAAK,CACvC,IAAIskB,EAAmB,CAAC,YAAa,cAAe,WAAY,QAAS,iBACvE,iBAAkB,QAAS,UAAW,WACxCA,EAAiB/vB,QAAQ,SAAAgwB,GACvB,GAAIhnB,EAAKsF,OAAO0hB,KAAkBvxB,UAAW,CAC3CuK,EAAKsF,OAAO0hB,GAAgB,GAE9B,GAAIhnB,EAAKsF,OAAO0hB,IAAiBhnB,EAAKsF,OAAO0hB,GAAczwB,OAAS,GAAK+O,EAAO0hB,GAAczwB,OAASyJ,EAAKsF,OAAO0hB,GAAczwB,OAAQ,CAEvI,IAAI4jB,EAAMna,EAAKsF,OAAO0hB,GAAczwB,OAAS+O,EAAO0hB,GAAczwB,OAElE,GAAIyJ,EAAK2a,MAAMqM,GAAe,CAC5B,IAAM,IAAIvkB,EAAIzC,EAAK2a,MAAMqM,GAAczwB,OAAS4jB,EAAK1X,EAAIzC,EAAK2a,MAAMqM,GAAczwB,OAASkM,IAAK,CAC9FqB,EAAMkiB,OAAOhmB,EAAK2a,MAAMqM,GAAcvkB,IAExCzC,EAAK2a,MAAMqM,GAAczwB,OAASyJ,EAAK2a,MAAMqM,GAAczwB,OAAS4jB,EAAM,EAAI,EAAIna,EAAK2a,MAAMqM,GAAczwB,OAAS4jB,GAKxH,IAAID,EAAY,GAChB,IAAID,EAAY,GAEhB,IAAK,IAAI5jB,EAAI,EAAGA,EAAIiP,EAAO0hB,GAAczwB,OAAQF,IAAK,CAEpD,UACS2J,EAAKsF,OAAO0hB,GAAc3wB,KAAO,YACxC,CACA6jB,EAAU3L,KAAK,CACbsM,OAAQ,IAAInd,MAAMqnB,QAChBzf,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,OAE1BykB,IAAK,IAAIpd,MAAMqnB,QACbzf,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,OAE1B+qB,IAAK9b,EAAO0hB,GAAc3wB,KAE5B4jB,EAAU1L,KAAK,CACbsM,OAAQ,IAAInd,MAAMqnB,QAChB/kB,EAAKsF,OAAO0hB,GAAc3wB,GAAG,MAC7B2J,EAAKsF,OAAO0hB,GAAc3wB,GAAG,MAC7B2J,EAAKsF,OAAO0hB,GAAc3wB,GAAG,OAE/BykB,IAAK,IAAIpd,MAAMqnB,QACb/kB,EAAKsF,OAAO0hB,GAAc3wB,GAAG,MAC7B2J,EAAKsF,OAAO0hB,GAAc3wB,GAAG,MAC7B2J,EAAKsF,OAAO0hB,GAAc3wB,GAAG,aAG5B,CAEL6jB,EAAU3L,KAAK,CACbsM,OAAQ,IAAInd,MAAMqnB,QAChBzf,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,OAE1BykB,IAAK,IAAIpd,MAAMqnB,QACbzf,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,OAE1B+qB,IAAK9b,EAAO0hB,GAAc3wB,MAQhC,IAAK,IAAIA,EAAI,EAAGA,EAAI6jB,EAAU3jB,OAAQF,IAAK,CACzC,GAAI2wB,IAAiB,YAAa,CAChChnB,EAAKga,qBAAqB3jB,EAAG4jB,EAAU5jB,GAAI6jB,EAAU7jB,SAChD,GAAI2wB,IAAiB,cAAe,CACzChnB,EAAKmc,iBAAiB9lB,EAAG4jB,EAAU5jB,GAAI6jB,EAAU7jB,SAC5C,GAAI2wB,IAAiB,WAAY,CACtC,IAAIlL,OAAW,EACf,GAAIxW,EAAO0hB,GAAc3wB,GAAG2Q,GAAK1B,EAAO0hB,GAAc3wB,GAAG4Q,GAAM,EAAG,CAChE6U,EAAcxV,KAAKO,IAAIvB,EAAO0hB,GAAc3wB,GAAG2Q,IAAMV,KAAKO,IAAIvB,EAAO0hB,GAAc3wB,GAAG4Q,IAAM,OAAS,YAChG,CACL6U,EAAcxV,KAAKO,IAAIvB,EAAO0hB,GAAc3wB,GAAG2Q,IAAMV,KAAKO,IAAIvB,EAAO0hB,GAAc3wB,GAAG4Q,IAAM,OAAS,QAEvGjH,EAAK6b,oBAAoBxlB,EAAG4jB,EAAU5jB,GAAI6jB,EAAU7jB,GAAIylB,QACnD,GAAIkL,IAAiB,QAAS,CACnChnB,EAAKwb,iBAAiBnlB,EAAG4jB,EAAU5jB,GAAI6jB,EAAU7jB,SAC5C,GAAI2wB,IAAiB,YAAa,CACvC9qB,QAAQM,IAAI,WAAWwqB,EAAc9M,EAAU7jB,SAC1C,GAAI2wB,IAAiB,iBAAkB,CAC5ChnB,EAAKqgB,uBAAuBhqB,EAAG4jB,EAAU5jB,GAAI6jB,EAAU7jB,SAClD,GAAI2wB,IAAiB,iBAAkB,CAC5ChnB,EAAK4f,qBAAqBvpB,EAAG4jB,EAAU5jB,GAAI6jB,EAAU7jB,GAAIowB,EAAInQ,cACxD,GAAI0Q,IAAiB,UAAW,CACjChnB,EAAK6hB,sBAAsB3H,EAAU7jB,GAAI6jB,QACxC,GAAI8M,IAAiB,UAAW,CACjChnB,EAAKkhB,aAAahH,EAAU7jB,GAAI6jB,QAC/B,GAAI8M,IAAiB,QAAS,CAC/BhnB,EAAK6hB,sBAAsB3H,EAAU7jB,GAAI6jB,OAWnD,IAAI3d,EAAU+I,EAAOqgB,oBACrB,IAAIsB,EAAmB,SAAnBA,EAA4Bzc,GAC9B,OAAO,IAAI9M,MAAMqnB,QACfze,KAAKO,IAAI2D,EAAKvD,GAAKuD,EAAKxD,IACxBV,KAAKO,IAAI2D,EAAKzD,GAAKyD,EAAK1D,IACxBR,KAAKO,IAAI2D,EAAKX,GAAKW,EAAKV,MAG5B,IAAIod,EAAuB,SAAvBA,EAAgC1c,GAClC,OAAO,IAAI9M,MAAMqnB,SACdva,EAAKvD,GAAKuD,EAAKxD,IAAM,EACtBwD,EAAKzD,GAAK,KACTyD,EAAKX,GAAKW,EAAKV,IAAM,IAG1B7V,KAAK0mB,MAAMpe,QAAQvF,QAAQ,SAAAmwB,GACzB,IAAK,IAAI9wB,EAAI,EAAGA,EAAI8wB,EAAY5wB,OAAQF,IAAK,CAC3CyN,EAAMkiB,OAAOmB,EAAY9wB,IAE3B8wB,EAAY5wB,OAAS,IAEvB,IAAK,IAAI6wB,KAAe7qB,EAAS,CAC/B,GAAI6qB,IAAgB,SAAU,CAC5B,SAEF,IAAID,EAAc5qB,EAAQ6qB,GAE1B,IAAIlN,EAAY,GAChB,IAAK,IAAI7jB,EAAI,EAAGA,EAAI8wB,EAAY5wB,OAAQF,IAAK,CAC3C6jB,EAAU3L,KAAK,CACbqN,KAAMqL,EAAiBE,EAAY9wB,IACnC0B,SAAUmvB,EAAqBC,EAAY9wB,MAG/C,IAAK,IAAIA,EAAI,EAAGA,EAAI6jB,EAAU3jB,OAAQF,IAAK,CACzCpC,KAAKozB,cACHhxB,EACAZ,UACAykB,EAAU7jB,GACVuwB,EAAkBQ,KAKxB,IAAK,IAAI/wB,EAAI,EAAGA,EAAIpC,KAAK0mB,MAAMyF,6BAA6B7pB,OAAQF,IAAK,CACvEyN,EAAMkiB,OAAO/xB,KAAK0mB,MAAMyF,6BAA6B/pB,IAEvDpC,KAAK0mB,MAAMyF,6BAA6B7pB,OAAS,EAEjD,IAAK,IAAIF,EAAI,EAAGA,EAAIiP,EAAO8a,6BAA6B7pB,OAAQF,IAAK,CACnEpC,KAAKgsB,iCAAiC3a,EAAO8a,6BAA6B/pB,IAI5E,IAAK,IAAIA,EAAI,EAAGA,EAAIpC,KAAK0mB,MAAM8B,KAAKlmB,OAAQF,IAAK,CAC/CyN,EAAMkiB,OAAO/xB,KAAK0mB,MAAM8B,KAAKpmB,IAE/BpC,KAAK0mB,MAAM8B,KAAKlmB,OAAS,EAEzB,IAAK,IAAIF,EAAI,EAAGA,EAAIiP,EAAOmX,KAAKlmB,OAAQF,IAAK,CAC3CpC,KAAKqoB,WAAWhX,EAAOmX,KAAKpmB,IAI9B,IAAK,IAAIA,EAAI,EAAGA,EAAIpC,KAAK0mB,MAAMiJ,OAAOrtB,OAAQF,IAAK,CACjDyN,EAAMkiB,OAAO/xB,KAAK0mB,MAAMiJ,OAAOvtB,IAEjCpC,KAAK0mB,MAAMiJ,OAAOrtB,OAAS,EAC3B,IAAK,IAAIF,EAAI,EAAGA,EAAIiP,EAAOse,OAAOrtB,OAAQF,IAAK,CAC3CpC,KAAK4tB,sBAAsB,CACnBhH,OAAQ,CAAC7iB,EAAGsN,EAAOse,OAAOvtB,GAAG4Q,GAAIhP,EAAGqN,EAAOse,OAAOvtB,GAAG0Q,GAAI7O,EAAGoN,EAAOse,OAAOvtB,GAAGwT,IAC7EiR,IAAK,CAAC9iB,EAAGsN,EAAOse,OAAOvtB,GAAG2Q,GAAI/O,EAAGqN,EAAOse,OAAOvtB,GAAGyQ,GAAI5O,EAAGoN,EAAOse,OAAOvtB,GAAGyT,MAKtF7V,KAAK8sB,WAAa,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAC/D9sB,KAAKqzB,oBAAsB,CACzB,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,IAGFrzB,KAAKwrB,cAAgB,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAElE,IAAK,IAAIppB,EAAI,EAAGA,EAAIpC,KAAK0mB,MAAMlT,MAAMlR,OAAQF,IAAK,CAChDyN,EAAMkiB,OAAO/xB,KAAK0mB,MAAMlT,MAAMpR,IAGhC,IAAK,IAAIA,EAAI,EAAGA,EAAIpC,KAAK0mB,MAAMqG,YAAYzqB,OAAQF,IAAK,CACtDyN,EAAMkiB,OAAO/xB,KAAK0mB,MAAMqG,YAAY3qB,IAEtCpC,KAAK0mB,MAAMqG,YAAYzqB,OAAS,EAChCtC,KAAK0mB,MAAMlT,MAAMlR,OAAS,EAE1B,IAAK,IAAIF,EAAI,EAAGA,EAAIiP,EAAOmC,MAAMlR,OAAQF,IAAK,CAC5CpC,KAAKusB,WACH0G,EAAqB5hB,EAAOmC,MAAMpR,IAClC4wB,EAAiB3hB,EAAOmC,MAAMpR,IAC9BiP,EAAOmC,MAAMpR,GAAGf,KAChBgQ,EAAOmC,MAAMpR,GAAGkxB,KAChB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAChD,IACGjiB,EAAOmC,MAAMpR,GAAGumB,SAClBtX,EAAOmC,MAAMpR,GAAG6mB,aAKtB,IAAK,IAAI7mB,EAAI,EAAGA,EAAIpC,KAAK0mB,MAAMjT,QAAQnR,OAAQF,IAAK,CAClDyN,EAAMkiB,OAAO/xB,KAAK0mB,MAAMjT,QAAQrR,IAElCpC,KAAK0mB,MAAMjT,QAAQnR,OAAS,EAE5B,IAAK,IAAIF,EAAI,EAAGA,EAAIiP,EAAOoC,QAAQnR,OAAQF,IAAK,CAC9CpC,KAAKyoB,cACHwK,EAAqB5hB,EAAOoC,QAAQrR,IACpC4wB,EAAiB3hB,EAAOoC,QAAQrR,IAChC,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KACjDpC,KAAKmU,WACL9C,EAAOoC,QAAQrR,IAKnB,IAAK,IAAIA,EAAI,EAAGA,EAAIpC,KAAKuvB,SAASjtB,OAAQF,IAAK,CAC7CyN,EAAMkiB,OAAO/xB,KAAKuvB,SAASntB,IAE7B,IAAK,IAAIA,EAAI,EAAGA,EAAIowB,EAAI5gB,KAAMxP,IAAK,CACjC,IAAIuR,EAAS,EACb,IAAK,IAAInF,EAAI,EAAGA,EAAIpM,EAAGoM,IAAK,CAC1BmF,GAAU6e,EAAIe,UAAUC,UAAUhlB,GAEpCmF,GAAU6e,EAAIe,UAAUC,UAAUpxB,GAAK,EACvC,IAAIP,EAAM,IAAI4H,MAAMgqB,SAOpBzzB,KAAKuvB,SAASjV,KAAKzY,GACnB7B,KAAKuvB,SAASntB,GAAG0B,SAASojB,KAAKsL,EAAI7gB,MAAQ,EAAI,IAC/C3R,KAAKuvB,SAASntB,GAAG0B,SAASqjB,KAAKxT,GAC/B3T,KAAKuvB,SAASntB,GAAG0B,SAASsjB,KAAK,KAC/BpnB,KAAKuvB,SAASntB,GAAGsxB,uBAAyB,KAC1C7jB,EAAM3D,IAAIlM,KAAKuvB,SAASntB,KAGhC,IAAIuxB,EAAqBC,GACnBviB,EACAmhB,EAAI5gB,KACJ4gB,EAAInQ,QACJmQ,EAAI7gB,OAGN3R,KAAK6zB,SAAWF,EAAmB,GACnC3zB,KAAK8zB,oBAAsB,CAACH,EAAmB,GAAIA,EAAmB,IAEtE3zB,KAAKqR,OAASA,EACdrR,KAAK+zB,wBAA0B1iB,EAAOqgB,oBAAoBsC,OAC1Dh0B,KAAKumB,eAAiBvmB,KAAK0R,MAM3B,UAAWuiB,gBAAkB,YAAa,CACxCA,eACEzB,EAAI7gB,MACJ6gB,EAAI0B,YACJl0B,KAAKqR,OAAOQ,YACZ7R,KAAKqR,OAAOS,UACZ9R,KAAKqR,OAAOU,UAIhB,IAAK/R,KAAKm0B,gBAAgB,CAExB,IAAK,IAAI/xB,EAAI,EAAGA,EAAIpC,KAAKkqB,2BAA2B,SAAS5nB,OAAQF,IAAK,CACxEpC,KAAKkqB,2BAA2B,SAC9B9nB,GACAquB,SAASG,eAAejxB,MAAQ,EAClCK,KAAKkqB,2BAA2B,YAC9B9nB,GACAquB,SAASG,eAAejxB,MAAQ,GAItC,UAAWy0B,QAAU,YAAa,CAC9BA,OAAOC,YAAY,eAAgBhjB,GACnC,GAAIgU,EAAU,CAEZ+O,OAAOE,QAAQ,sBAAuBjjB,OACjC,CACL+iB,OAAOC,YAAY,eAAgBhjB,8CAKjB,IAAbmF,EAAanU,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GAAH,EACrB,GAAImU,GAAa,EAAG,CAClBxW,KAAKsrB,MAAQ,IACbtrB,KAAKurB,MAAQ,IACbvrB,KAAKsvB,MAAQ,IACbtvB,KAAKmU,WAAa,OACb,GAAIqC,GAAa,EAAG,CACzBxW,KAAKsrB,MAAQ,IACbtrB,KAAKurB,MAAQ,IACbvrB,KAAKsvB,MAAQ,IACbtvB,KAAKmU,WAAa,6CAIN8B,GACdjW,KAAK4oB,cAAgB3S,uCAGZ6O,GACP9kB,KAAK8kB,SAAWA,iDAGChL,EAAOkM,EAAWC,GACrC,IAAIC,EACJ,IAAIrW,EAAQ7P,KAAK6P,MAEjB,UAAWmW,IAAc,YAAa,CACpCE,EAAMlmB,KAAKmmB,aAAaH,EAAWC,OAC9B,CACLC,EAAM,CACJE,QAAS,KACTC,OAAQ,KACRC,WAAY,MAGhB,GAAItmB,KAAK0R,OAAS1R,KAAKumB,eAAgB,CACrCL,EAAIG,OAAS,KACbH,EAAIM,KAAO,MAIb,GAAIN,EAAIM,OAAS,KAAM,CACrB,OAAO,KAET,IAAIC,EAEJ,GAAIP,EAAIE,QAAS,CAEfK,EAAWpc,EAAS,YAAY8P,YAC3B,CAELsM,EAAWzmB,KAAK0mB,MAAM5U,UAAUgI,GAGlC,IAAI6M,EAAWtU,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,GAAK,EAGhE,GAAIkiB,EAAIG,OAAQ,CACd,IAAIS,EAAcC,EAAYC,cAAc3c,EAAS,aACrDoc,EAASQ,MAAMC,KAAK,GACpBT,EAASQ,MAAME,KAAKnnB,KAAK0R,MAAQ,KAChC+U,EAASQ,MAAMG,KAAKT,EAAW,KAChCF,EAASY,SAAStjB,GAAKsO,KAAKiV,GAAK,EACjC,GAAIpB,EAAIE,QAAS,CAEfK,EAASQ,MAAMC,KAAK,GACpBT,EAASQ,MAAME,KAAKnnB,KAAK0R,MAAQ,KACjC+U,EAASQ,MAAMG,KAAKT,EAAW,KAGjCF,EAAS3iB,SAASojB,KAAKjB,EAAUW,OAAO7iB,GACxC0iB,EAAS3iB,SAASqjB,KAChB9U,KAAKG,IAAIyT,EAAUW,OAAO5iB,EAAGiiB,EAAUY,IAAI7iB,GAAG,KAIlD,GAAIkiB,EAAII,WAAY,CAElBG,EAAS3iB,SAASojB,KAAKjB,EAAUW,OAAO7iB,GACxC0iB,EAAS3iB,SAASqjB,KAChB9U,KAAKG,IAAIyT,EAAUW,OAAO5iB,EAAGiiB,EAAUY,IAAI7iB,GAAG,KAGhDyiB,EAASQ,MAAME,KAAKnnB,KAAK0R,MAAQ,KACjC+U,EAASQ,MAAMG,KAAKT,EAAW,KAGjC,GAAIT,EAAIE,QAAS,CAEfK,EAASxb,KAAO,WAChB4E,EAAM3D,IAAIua,GACVzmB,KAAK0mB,MAAM5U,UAAUwI,KAAKmM,+CAIb3M,EAAOkM,EAAWC,GACjC,IAAIC,EACJ,IAAIrW,EAAQ7P,KAAK6P,MAEjB,UAAWmW,IAAc,YAAa,CACpCE,EAAMlmB,KAAKmmB,aAAaH,EAAWC,OAC9B,CACLC,EAAM,CACJE,QAAS,KACTC,OAAQ,KACRC,WAAY,MAGhB,GAAIJ,EAAIM,OAAS,KAAM,CACrB,OAAO,KAGT,IAAInmB,EACJ,GAAI6lB,EAAIE,QAAS,CACf/lB,EAAOgK,EAAS,cAAc8P,YACzB,CACL9Z,EAAOL,KAAK0mB,MAAMtT,MAAM0G,GAE1B,IAAI0N,EAAUT,EAAYC,cAAc3c,EAAS,eAEjD,IAAIod,EAAQC,EACZrnB,EAAKgnB,SAAStjB,GAAKsO,KAAKiV,GAExBI,EAASrV,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,EAAI,IAAMwjB,EAAQG,OAAO5jB,EAC9E0jB,EAASpV,KAAKO,IAAIqT,EAAUW,OAAO7iB,EAAIkiB,EAAUY,IAAI9iB,EAAI,IAAMyjB,EAAQG,OAAO5jB,EAE9E1D,EAAKyD,SAASojB,MAAMjB,EAAUW,OAAO7iB,EAAIkiB,EAAUY,IAAI9iB,GAAG,GAC1D1D,EAAKyD,SAASqjB,KAAKlB,EAAUW,OAAO5iB,EAAI,GACxC3D,EAAKyD,SAASsjB,KAAK,IACnB/mB,EAAK4mB,MAAME,KAAKO,GAChBrnB,EAAK4mB,MAAMC,KAAKO,GAEhB,GAAIvB,EAAIE,QAAS,CACf/lB,EAAK4K,KAAO,SACZ4E,EAAM3D,IAAI7L,GACV,GAAIL,KAAK0mB,MAAMtT,QAAU5R,UAAW,CAClCxB,KAAK0mB,MAAMtT,MAAO,GAEpBpT,KAAK0mB,MAAMtT,MAAMkH,KAAKja,kDAINyZ,EAAOkM,EAAWC,EAAW4B,GAE/C,IAAI3B,EACJ,IAAIrW,EAAQ7P,KAAK6P,MACjB,UAAWmW,IAAc,YAAa,CACpCE,EAAMlmB,KAAKmmB,aAAaH,EAAWC,OAC9B,CACLC,EAAM,CACJE,QAAS,KACTC,OAAQ,KACRC,WAAY,MAGhB,GAAItmB,KAAK0R,OAAS1R,KAAKumB,eAAgB,CACrCL,EAAIG,OAAS,KACbH,EAAIM,KAAO,MAEb,GAAIN,EAAIM,OAAS,KAAM,CACrB,OAAO,KAGT,IAAIsB,EACJ,GAAI5B,EAAIE,QAAS,CACf0B,EAAUzd,EAAS,WAAW8P,YACzB,CACL2N,EAAU9nB,KAAK0mB,MAAM,YAAY5M,GAEnC,IAAIiO,EAAahB,EAAYC,cAAc3c,EAAS,eAEpD,IAAIod,EAAQO,EACZ,GAAGH,GAAe,OAAQ,CACtBC,EAAQT,SAAStjB,GAAKsO,KAAKiV,GAAK,MAC7B,CACLQ,EAAQT,SAAStjB,EAAIsO,KAAKiV,GAAK,EAEjC,GAAIpB,EAAIE,QAAS,EAGjB4B,EACE3V,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,GAAK+jB,EAAWJ,OAAO5jB,EAErE0jB,EACEpV,KAAKO,IAAIqT,EAAUW,OAAO7iB,EAAIkiB,EAAUY,IAAI9iB,GAAKgkB,EAAWJ,OAAO5jB,EAErE,GAAImiB,EAAIG,OAAQ,CACdyB,EAAQb,MAAMG,KAAKY,GACnBF,EAAQb,MAAMC,KAAKO,GACnB,GAAIvB,EAAIE,QAAS,CACf0B,EAAQb,MAAME,KAAK,GAErBY,EAAahB,EAAYC,cAAcc,GACvCA,EAAQhkB,SAASmkB,IACf5V,KAAKG,IAAIyT,EAAUW,OAAO7iB,EAAGkiB,EAAUY,IAAI9iB,GAAKgkB,EAAWJ,OAAO5jB,EAAI,EACtEsO,KAAKG,IAAIyT,EAAUW,OAAO5iB,EAAGiiB,EAAUY,IAAI7iB,GACzCqO,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,GAAG,EACjD,GAIJ,GAAIkiB,EAAII,WAAY,CAClByB,EAAahB,EAAYC,cAAcc,GACvCA,EAAQhkB,SAASmkB,IACf5V,KAAKG,IAAIyT,EAAUW,OAAO7iB,EAAGkiB,EAAUY,IAAI9iB,GAAKgkB,EAAWJ,OAAO5jB,EAAI,EACtEsO,KAAKG,IAAIyT,EAAUW,OAAO5iB,EAAGiiB,EAAUY,IAAI7iB,GACzCqO,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,GAAG,EACjD,GAIJ,GAAIkiB,EAAIE,QAAS,CACf0B,EAAQ7c,KAAO,WACf4E,EAAM3D,IAAI4b,GACV,GAAI9nB,KAAK0mB,MAAM,cAAgBllB,UAAW,CACxCxB,KAAK0mB,MAAM,YAAc,GAE3B1mB,KAAK0mB,MAAM,YAAYpM,KAAKwN,+CAIfhO,EAAOkM,EAAWC,GACjC,IAAIC,EACJ,IAAIrW,EAAQ7P,KAAK6P,MACjB,UAAWmW,IAAc,YAAa,CACpCE,EAAMlmB,KAAKmmB,aAAaH,EAAWC,OAC9B,CACLC,EAAM,CACJE,QAAS,KACTC,OAAQ,KACRC,WAAY,MAGhB,GAAItmB,KAAK0R,OAAS1R,KAAKumB,eAAgB,CACrCL,EAAIG,OAAS,KACbH,EAAIM,KAAO,MAEb,GAAIN,EAAIM,OAAS,KAAM,CACrB,OAAO,KAET,IAAI2B,EACJ,GAAIjC,EAAIE,QAAS,CACf+B,EAAa9d,EAAS,cAAc8P,YAC/B,CACLgO,EAAanoB,KAAK0mB,MAAM7U,YAAYiI,GAGtC,IAAI6M,EAAWtU,KAAKO,IAAIqT,EAAUW,OAAO7iB,EAAIkiB,EAAUY,IAAI9iB,GAC3D,GAAImiB,EAAIG,OAAQ,CACd,IAAI+B,EAAgBrB,EAAYC,cAAc3c,EAAS,eACvD,IAAI4c,EAAQN,EAAWyB,EAAcT,OAAO5jB,EAC5CokB,EAAWlB,MAAMC,KAAKD,GACtBkB,EAAWd,SAAStjB,GAAKsO,KAAKiV,GAAK,EAEnCa,EAAWlB,MAAMG,KAAK,GACtBe,EAAWlB,MAAME,KAAKnnB,KAAK0R,MAAQ0W,EAAcT,OAAO3jB,GACxDmkB,EAAWrkB,SAASqjB,KAAKlB,EAAUW,OAAO5iB,GAC1CmkB,EAAWrkB,SAASojB,KAClB7U,KAAKG,IAAIyT,EAAUW,OAAO7iB,EAAGkiB,EAAUY,IAAI9iB,GAAK4iB,EAAW,GAI/D,GAAIT,EAAII,WAAY,CAClB6B,EAAWrkB,SAASqjB,KAAKlB,EAAUW,OAAO5iB,GAC1CmkB,EAAWrkB,SAASojB,KAClB7U,KAAKG,IAAIyT,EAAUW,OAAO7iB,EAAGkiB,EAAUY,IAAI9iB,GAAK4iB,EAAW,GAI/D,GAAIT,EAAIE,QAAS,CACf+B,EAAWld,KAAO,aAClBjL,KAAK6P,MAAM3D,IAAIic,GACfnoB,KAAK0mB,MAAM7U,YAAYyI,KAAK6N,uDAQP9W,GAEvB,IAAIkjB,EAAU,GACd,IAAIC,EAAU,EACd,IAAIC,EAAU,EACdpjB,EAAOS,UAAUtG,IAAI,SAAC+K,GACpB,GAAIlE,KAAKO,IAAI2D,EAAKvD,IAAMwhB,GAAYniB,KAAKO,IAAI2D,EAAKxD,IAAMyhB,EAAS,CAC/DA,EAAWniB,KAAKO,IAAI2D,EAAKvD,IAAMX,KAAKO,IAAI2D,EAAKxD,IAAMwD,EAAKvD,GAAKuD,EAAKxD,GAEpE,GAAIwD,EAAKvD,GAAKyhB,GAAYle,EAAKxD,GAAK0hB,EAAS,CAC3CA,EAAWle,EAAKvD,GAAKuD,EAAKxD,GAAKwD,EAAKxD,GAAKwD,EAAKvD,MAIlD3B,EAAOS,UAAUtG,IAAI,SAAC+K,GACpB,GAAGA,EAAKvD,KAAOwhB,GAAWje,EAAKvD,KAAOyhB,EAAS,CAC7CF,EAAQja,KAAK/D,MAIjB,OAAOge,qDAGgBljB,GACvB,IAAIqjB,EAAY,GAChB,IAAIC,EAAgBtjB,EAAOQ,YAAY,GAAG,MAC1C,IAAI+iB,EAAgBvjB,EAAOQ,YAAYR,EAAOQ,YAAYvP,OAAO,GAAG,MACpE,IAAIuyB,EAAYhC,EAAEpgB,IAAIpB,EAAOQ,YAAYrG,IAAI,SAAAzH,GAAC,OAAEA,EAAE,SAClD,IAAI+wB,EAAYjC,EAAErgB,IAAInB,EAAOQ,YAAYrG,IAAI,SAAAzH,GAAC,OAAEA,EAAE,SAClD,IAAK,IAAI3B,EAAE,EAAGA,EAAGiP,EAAOQ,YAAYvP,OAAQF,IAAI,CAC9C,GAAIuyB,EAAgB9B,EAAErgB,IAAI,CAACnB,EAAOQ,YAAYzP,GAAG,MAAMiP,EAAOQ,YAAYzP,GAAG,QAAS,CACpFuyB,EAAgB9B,EAAErgB,IAAI,CAACnB,EAAOQ,YAAYzP,GAAG,MAAMiP,EAAOQ,YAAYzP,GAAG,QAE3E,GAAIwyB,EAAgB/B,EAAEpgB,IAAI,CAACpB,EAAOQ,YAAYzP,GAAG,MAAMiP,EAAOQ,YAAYzP,GAAG,QAAS,CACpFwyB,EAAgB/B,EAAEpgB,IAAI,CAACpB,EAAOQ,YAAYzP,GAAG,MAAMiP,EAAOQ,YAAYzP,GAAG,SAG7EsyB,EAAUpa,KAAK,CAACtH,GAAM2hB,EAAe5hB,GAAM6hB,EAAe9hB,GAAMgiB,EAAWjiB,GAAMiiB,IACjFJ,EAAUpa,KAAK,CAACtH,GAAM2hB,EAAe5hB,GAAM6hB,EAAe9hB,GAAM+hB,EAAWhiB,GAAMgiB,IAEjF,OAAOH,iEAG4BrjB,GACnC,IAAI0jB,EAAOlC,EAAErgB,IAAInB,EAAOQ,YAAYrG,IAAI,SAAAzH,GAAC,OAAE8uB,EAAErgB,IAAI,CAACzO,EAAE,MAAMA,EAAE,WAC5D,IAAIixB,EAAOnC,EAAEpgB,IAAIpB,EAAOQ,YAAYrG,IAAI,SAAAzH,GAAC,OAAE8uB,EAAEpgB,IAAI,CAAC1O,EAAE,MAAMA,EAAE,WAC5D,IAAIkxB,EAAc,GAHwB,IAAAC,EAAA,SAAAC,EAAA,UAAAC,EAAA5zB,UAAA,IAK1C,QAAA6zB,EAAchkB,EAAOQ,YAArBpQ,OAAA6zB,YAAAC,IAAAL,GAAAK,EAAAF,EAAAx0B,QAAAM,MAAA+zB,EAAA,KAAiC,KAAxB9yB,EAAwBmzB,EAAA51B,MAC/B,IAAI61B,EAAY3C,EAAErgB,IAAI,CAACpQ,EAAE,MAAMA,EAAE,QACjC,IAAIqzB,EAAY5C,EAAEpgB,IAAI,CAACrQ,EAAE,MAAMA,EAAE,QACjC,GAAIozB,EAAYT,EAAK,CACnBE,EAAY3a,KAAK,CAACvW,EAAKyxB,EAAY,EAAGxxB,EAAK5B,EAAE,QAE/C,GAAIqzB,EAAYT,EAAK,CACnBC,EAAY3a,KAAK,CAACvW,EAAK0xB,EAAY,EAAGzxB,EAAK5B,EAAE,UAZP,MAAAhB,GAAA+zB,EAAA,KAAAC,EAAAh0B,EAAA,gBAAA8zB,GAAAG,EAAA9zB,QAAA,MAAA8zB,EAAA9zB,UAAA,WAAA4zB,EAAA,OAAAC,IAe1C,OAAOH,iDAGYnb,EAAOkM,EAAWC,EAAW5D,GAChD,IAAI6D,EACJ,IAAIrW,EAAQ7P,KAAK6P,MAEjB,UAAWmW,IAAc,YAAa,CACpCE,EAAMlmB,KAAKmmB,aAAaH,EAAWC,OAC9B,CACLC,EAAM,CACJE,QAAS,KACTC,OAAQ,KACRC,WAAY,MAGhB,GAAItmB,KAAK0R,OAAS1R,KAAKumB,eAAgB,CACrCL,EAAIG,OAAS,KACbH,EAAIM,KAAO,MAEb,GAAIN,EAAIM,OAAS,KAAM,CACrB,OAAO,KAGT,IAAIoF,EAEJ,GAAI1F,EAAIE,QAAS,CACfwF,EAAOvhB,EAAS,cAAc8P,YACzB,CACLyR,EAAO5rB,KAAK0mB,MAAMmF,eAAe/R,GAGnC,IAAI6M,EAAWtU,KAAKO,IAAIqT,EAAUW,OAAO5iB,EAAIiiB,EAAUY,IAAI7iB,GAC3D,IAAIijB,GAASN,EAAW,GAAI,IAE5B,IAAImF,EAAS,EAEb,GAAG7F,EAAUW,OAAO7iB,EAAI,EAAG,CAEvB+nB,GAAW,QACR,CAELA,EAAS,IAGX,GAAGzJ,GAAW,EAAE,CACdyJ,GAAWA,EAGb,GAAI5F,EAAIG,OAAQ,CAEd,IAAI0F,EAAgBhF,EAAYC,cAAc3c,EAAS,eACvDuhB,EAAKvE,SAAStjB,GAAMsO,KAAKiV,GAAG,EAC5BsE,EAAK3E,MAAME,KAAKnnB,KAAK0R,MAAQ,KAC3Bka,EAAK3E,MAAMG,KAAKH,GAGlB2E,EAAK9nB,SAASqjB,KAAKlB,EAAUW,OAAO5iB,EAAI,KACxC4nB,EAAK9nB,SAASojB,KAAKjB,EAAUW,OAAO7iB,EAAI+nB,GAG1C,GAAI5F,EAAII,WAAY,CAClBsF,EAAK9nB,SAASqjB,KAAKlB,EAAUW,OAAO5iB,EAAI,KACxC4nB,EAAK9nB,SAASojB,KAAKjB,EAAUW,OAAO7iB,EAAI+nB,GACxCF,EAAK3E,MAAME,KAAKnnB,KAAK0R,MAAQ,KAC7Bka,EAAK3E,MAAMG,KAAKH,GAElB,GAAIf,EAAIE,QAAS,CACfwF,EAAK3gB,KAAO,kBACZjL,KAAK6P,MAAM3D,IAAI0f,GACf5rB,KAAK0mB,MAAMmF,eAAevR,KAAKsR,+DAMF3F,GAC/B,IAAIC,EACJ,IAAIrW,EAAQ7P,KAAK6P,MAGjB,IAAIoc,EAAQ5hB,EAAS,mBAAmB8P,QACxC8R,EAAM5E,SAAStjB,GAAKsO,KAAKiV,GAAK,EAE9B2E,EAAMnoB,SAASqjB,KAAKlB,EAAUjiB,GAC9BioB,EAAMnoB,SAASojB,KAAKjB,EAAUliB,GAG9B,IAAImoB,EAAWnF,EAAYC,cAAc3c,EAAS,oBAElD4hB,EAAMhF,MAAME,KAAKnnB,KAAK0R,MAAQwa,EAASvE,OAAO3jB,GAG9CioB,EAAMhhB,KAAO,4BACbjL,KAAK6P,MAAM3D,IAAI+f,GACfjsB,KAAK0mB,MAAMyF,6BAA6B7R,KAAK2R,oDAGxBnS,EAAOkM,EAAWC,GACvC,IAAIC,EACJ,IAAIrW,EAAQ7P,KAAK6P,MACjB,UAAWmW,IAAc,YAAa,CACpCE,EAAMlmB,KAAKmmB,aAAaH,EAAWC,OAC9B,CACLC,EAAM,CACJE,QAAS,KACTC,OAAQ,KACRC,WAAY,MAGhB,GAAItmB,KAAK0R,OAAS1R,KAAKumB,eAAgB,CACrCL,EAAIG,OAAS,KACbH,EAAIM,KAAO,MAEb,GAAIN,EAAIM,OAAS,KAAM,CACrB,OAAO,KAGT,IAAIoF,EAEJ,IAAIjF,EAAWtU,KAAKO,IAAIqT,EAAUW,OAAO7iB,EAAIkiB,EAAUY,IAAI9iB,GAS3D,GAAImiB,EAAIE,QAAS,CACfwF,EAAOvhB,EAAS,cAAc8P,YACzB,CACLyR,EAAO5rB,KAAK0mB,MAAM2F,eAAevS,GAInC,GAAIoM,EAAIG,OAAQ,CACd,IAAIiG,EAAgBvF,EAAYC,cAAc3c,EAAS,eACvD,IAAI4c,EAAQN,EAAW2F,EAAc3E,OAAO5jB,EAC5C6nB,EAAK3E,MAAMC,KAAKD,GAChB2E,EAAKvE,SAAStjB,GAAMsO,KAAKiV,GAAG,EAE5B,GAAIpB,EAAIE,QAAS,CACfwF,EAAK3E,MAAMG,KAAK,GAElBwE,EAAK3E,MAAME,KAAKnnB,KAAK0R,MAAQ4a,EAAc3E,OAAO3jB,GAElD4nB,EAAK9nB,SAASE,EAAI8V,EAASmM,EAAUW,OAAO5iB,EAAI,IAAQiiB,EAAUW,OAAO5iB,EAAK,IAC9E4nB,EAAK9nB,SAASojB,KACZ7U,KAAKG,IAAIyT,EAAUW,OAAO7iB,EAAGkiB,EAAUY,IAAI9iB,GAAK4iB,EAAW,GAI/D,GAAIT,EAAII,WAAY,CAElBsF,EAAK9nB,SAASE,EAAI8V,EAASmM,EAAUW,OAAO5iB,EAAI,IAAQiiB,EAAUW,OAAO5iB,EAAK,IAC9E4nB,EAAK9nB,SAASojB,KACZ7U,KAAKG,IAAIyT,EAAUW,OAAO7iB,EAAGkiB,EAAUY,IAAI9iB,GAAK4iB,EAAW,GAI/D,GAAIT,EAAIE,QAAS,CACfwF,EAAK3gB,KAAO,OACZjL,KAAK6P,MAAM3D,IAAI0f,GACf5rB,KAAK0mB,MAAM2F,eAAe/R,KAAKsR,yCAIxB9G,GACT9kB,KAAK8kB,SAAWA,gDAQhB,IAJA1hB,EAIAsF,EAJAtF,MACA+Q,EAGAzL,EAHAyL,WAGAuhB,EAAAhtB,EAFAkM,eAEA8gB,SAAA,EAFe,KAEfA,EAEA,IAAIC,EAAgB31B,KAAKqK,SACzB,IAAIqc,EAAQ1mB,KAAK0mB,MACjB,IAAIkP,EAAkB51B,KAAKkqB,2BAO3BtV,EAAeA,GAAgB,MAE/B,IAAIpR,EACJ,IAAIH,EACJ,IAAIC,EACJ,IAAIC,EACJ,IAAIE,EACJ,IAAIC,EACJ,IAAIP,EAnBJ,IAAA0yB,EA8BMxhB,eAA0BjR,EAAOkR,SAASH,GAAc3J,OAAO8L,KAAKC,KAAKpC,aA9B/E,IAAA2hB,EAAAthB,IAAAqhB,EAAA,GAsBE1yB,EAtBF2yB,EAAA,GAuBE1yB,EAvBF0yB,EAAA,GAwBEtyB,EAxBFsyB,EAAA,GAyBEzyB,EAzBFyyB,EAAA,GA0BExyB,EA1BFwyB,EAAA,GA2BEvyB,EA3BFuyB,EAAA,GA4BEryB,EA5BFqyB,EAAA,GA6BEpyB,EA7BFoyB,EAAA,GAiCA,IAAIrH,EAAiBkH,EAAc,WAEnCA,EAAc,YAAY/nB,SAAU,SAAWC,GAC3C,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAAS8yB,UACfloB,EAAM5K,SAASuI,IAAMmqB,EAAcvyB,EAAM,SACzCyK,EAAM5K,SAAS4mB,YAAc,KAE7Bhc,EAAM5K,SAAS6K,YAAc,MAC7BD,EAAM5K,SAASiuB,aAAeztB,KAKtCkyB,EAAc,cAAc/nB,SAAU,SAAWC,GAC7C,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAAS8yB,UACfloB,EAAM5K,SAASuI,IAAMmqB,EAAcvyB,EAAM,SACzCyK,EAAM5K,SAAS4mB,YAAc,KAE7Bhc,EAAM5K,SAAS6K,YAAc,MAC7BD,EAAM5K,SAASiuB,aAAeztB,KAItCkyB,EAAc,mBAAmB/nB,SAAU,SAAWC,GAClD,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAAS8yB,UACfloB,EAAM5K,SAASuI,IAAMmqB,EAAcvyB,EAAM,SACzCyK,EAAM5K,SAAS4mB,YAAc,KAC7Bhc,EAAM5K,SAAS6K,YAAc,MAC7BD,EAAM5K,SAASiuB,aAAeztB,KAItCkyB,EAAc,WAAW/nB,SAAU,SAAWC,GAC1C,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAAS8yB,UACfloB,EAAM5K,SAASuI,IAAMnB,EAASjH,EAAM,YACpCyK,EAAM5K,SAAS4mB,YAAc,KAC7Bhc,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM,EAAE,EAAE,GAC3CtjB,EAAM5K,SAAS6K,YAAc,MAC7BD,EAAM5K,SAASiuB,aAAeztB,EAC9BoK,EAAMwd,YAAc,MAI5BsK,EAAc,UAAU/nB,SAAU,SAAWC,GACzC,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAAS8yB,UACfloB,EAAM5K,SAASuI,IAAMnB,EAASjH,EAAM,YACpCyK,EAAM5K,SAAS4mB,YAAc,KAC7Bhc,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM,EAAE,EAAE,GAC3CtjB,EAAM5K,SAAS6K,YAAc,MAC7BD,EAAM5K,SAASiuB,aAAeztB,EAC9BoK,EAAMwd,YAAc,MAI5BsK,EAAc,kBAAkB/nB,SAAU,SAAWC,GACjD,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAAS8yB,UACfloB,EAAM5K,SAASuI,IAAMmqB,EAAcvyB,EAAM,mBACzCyK,EAAM5K,SAAS4mB,YAAc,KAC7Bhc,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM,EAAE,EAAE,GAC3CtjB,EAAM5K,SAAS6K,YAAc,MAC7BD,EAAM5K,SAASiuB,aAAeztB,KAItCkyB,EAAc,UAAU/nB,SAAU,SAAWC,GACzC,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAAS8yB,UACfloB,EAAM5K,SAASuI,IAAMmqB,EAAcvyB,EAAM,cACzCyK,EAAM5K,SAAS4mB,YAAc,KAC7Bhc,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM,EAAE,EAAE,GAC3CtjB,EAAM5K,SAAS6K,YAAc,MAC7BD,EAAM5K,SAASiuB,aAAeztB,EAC9BoK,EAAMwd,YAAc,KAI5BsK,EAAc,eAAe/nB,SAAU,SAAWC,GAC9C,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAAS8yB,UACfloB,EAAM5K,SAASuI,IAAMmqB,EAAcvyB,EAAM,cACzCyK,EAAM5K,SAAS4mB,YAAc,KAC7Bhc,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM,EAAE,EAAE,GAC3CtjB,EAAM5K,SAAS6K,YAAc,MAC7BD,EAAM5K,SAASiuB,aAAeztB,KAItCkyB,EAAc,gBAAgB/nB,SAAU,SAAWC,GAC/C,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAAS8yB,UACfloB,EAAM5K,SAASuI,IAAMmqB,EAAcvyB,EAAM,cACzCyK,EAAM5K,SAAS4mB,YAAc,KAC7Bhc,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM,EAAE,EAAE,GAC3CtjB,EAAM5K,SAAS6K,YAAc,MAC7BD,EAAM5K,SAASiuB,aAAeztB,KAItCkyB,EAAc,cAAc/nB,SAAU,SAAWC,GAC7C,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAAS8yB,UACfloB,EAAM5K,SAASuI,IAAMmqB,EAAcvyB,EAAM,SACzCyK,EAAM5K,SAAS4mB,YAAc,KAE7Bhc,EAAM5K,SAAS6K,YAAc,MAC7BD,EAAM5K,SAASiuB,aAAeztB,KAItCkyB,EAAc,cAAc/nB,SAAU,SAAWC,GAC7C,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAAS8yB,UACfloB,EAAM5K,SAASuI,IAAMmqB,EAAcvyB,EAAM,SACzCyK,EAAM5K,SAAS4mB,YAAc,KAE7Bhc,EAAM5K,SAASiuB,aAAeztB,KAItCkyB,EAAc,SAAS/nB,SAAU,SAAWC,GACxC,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM5tB,GAEvCsK,EAAMwd,aAAc,OAI5BsK,EAAc,gBAAgB/nB,SAAU,SAAWC,GAC/C,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B,UAAU+S,MAAQ,YAAa,CAC3B,GAAIhoB,OAAO8L,KAAKC,KAAKpC,aAAe,GAAKG,SAASke,IAAIvvB,YAAc,EAAG,CACnE4K,EAAM5K,SAASuI,IAAMmqB,EAAc,wBAChC,CACH9nB,EAAM5K,SAASuI,IAAMmqB,EAAc,eAG3C9nB,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM9tB,GACvCwK,EAAM5K,SAASiuB,aAAextB,EAC9BmK,EAAM5K,SAAS4mB,YAAc,QAIrC8L,EAAc,WAAW/nB,SAAU,SAAWC,GAC1C,GAAKA,aAAiBpE,MAAMgW,KAAO,CAK/B5R,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM9tB,GACvCwK,EAAM5K,SAASiuB,aAAextB,EAC9BmK,EAAM5K,SAAS4mB,YAAc,QAGrC8L,EAAc,QAAQ/nB,SAAU,SAAWC,GACvC,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B,UAAU+S,MAAQ,YAAa,CAC3B,GAAIhoB,OAAO8L,KAAKC,KAAKpC,aAAe,GAAKG,SAASke,IAAIvvB,YAAc,EAAG,CACnE4K,EAAM5K,SAASuI,IAAMmqB,EAAc,wBAChC,CACH9nB,EAAM5K,SAASuI,IAAMmqB,EAAc,eAG3C9nB,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM9tB,GACvCwK,EAAM5K,SAASiuB,aAAextB,EAC9BmK,EAAM5K,SAAS4mB,YAAc,QAIrC8L,EAAc,qBAAqB/nB,SAAU,SAAWC,GACpD,GAAKA,aAAiBpE,MAAMgW,KAAO,CAC/B5R,EAAM5K,SAASG,MAAQ,IAAIqG,MAAM0nB,MAAM7tB,MAM/C,IAAK,IAAIlB,EAAE,EAAGA,EAAIwzB,EAAgB,SAAStzB,OAAOF,IAAI,CAElDwzB,EAAgB,YAAYxzB,GAAGquB,SAASI,eAAelxB,MAAQ,IAAI8J,MAAM0nB,MAAM7tB,GAC/EsyB,EAAgB,YAAYxzB,GAAGynB,YAAc,KAEjD,IAAK,IAAIznB,EAAE,EAAGA,EAAIwzB,EAAgB,YAAYtzB,OAAOF,IAAI,CAErDwzB,EAAgB,YAAYxzB,GAAGquB,SAASI,eAAelxB,MAAQ,IAAI8J,MAAM0nB,MAAM7tB,GAC/EsyB,EAAgB,YAAYxzB,GAAGynB,YAAc,8CAIrC/P,EAAOkM,EAAWC,EAAW5kB,GACzC,IAAIwO,EAAQ7P,KAAK6P,MACjB,GAAIrF,OAAOwrB,SAASjsB,KAAK8S,QAAQ,aAAe,EAAG,CACjD,OAEF,IAAIqJ,EACJ,UAAWF,IAAc,YAAa,CACpCE,EAAMlmB,KAAKi2B,gBAAgBjQ,EAAWC,OACjC,CACLC,EAAM,CACJE,QAAS,KACTC,OAAQ,KACRC,WAAY,MAIhB,GAAIJ,EAAIM,OAAS,KAAM,CACrB,OAAO,KAET,IAAI7iB,EAGJ,IAAIuyB,EACDl2B,KAAKkvB,UAAY,SAAWlvB,KAAK+uB,UACjCvkB,OAAO2rB,UAAUC,WAAa,MAC9B5rB,OAAO2rB,UAAUE,mBAAqB7rB,OAAO8rB,kBAAoB9rB,OAAO+rB,kBAK3E,IAAIC,EACJ,IAAIC,EAEJ,GAAIvQ,EAAIE,QAAS,CACf,OAAQ/kB,GACN,KAAK,EACHsC,EAAS0G,EAAS,UAAU8P,QAC5Bsc,EAAY1P,EAAYC,cAAc3c,EAAS,WAC/C,GAAG6rB,EAAYM,EAAWnsB,EAAS,2BAA2B8P,QAC9D,MAEF,KAAK,EACHxW,EAAS0G,EAAS,eAAe8P,QACjCsc,EAAY1P,EAAYC,cAAc3c,EAAS,gBAC/C,MAEF,KAAK,EACH1G,EAAS0G,EAAS,gBAAgB8P,QAClCxW,EAAOsH,KAAO,eACdwrB,EAAY1P,EAAYC,cAAc3c,EAAS,iBAC/C,WAGC,CACL,OAAQhJ,GACN,KAAK,EACHsC,EAAS3D,KAAK0mB,MAAMpe,QAAQ,GAAGwR,GAC/B2c,EAAY1P,EAAYC,cAAc3c,EAAS,WAC/C,MAEF,KAAK,EACH1G,EAAS3D,KAAK0mB,MAAMpe,QAAQ,GAAGwR,GAC/B2c,EAAY1P,EAAYC,cAAc3c,EAAS,gBAC/C,MAEF,KAAK,EACH1G,EAAS3D,KAAK0mB,MAAMpe,QAAQ,GAAGwR,GAC/B2c,EAAY1P,EAAYC,cAAc3c,EAAS,iBAC/C,MAEF,KAAK,EACH1G,EAAS0G,EAAS,gBAAgB8P,QAClCsc,EAAY1P,EAAYC,cAAc3c,EAAS,WAC/C,MAEF,KAAK,EACH1G,EAAS0G,EAAS,iBAAiB8P,QACnCsc,EAAY1P,EAAYC,cAAc3c,EAAS,WAC/C,OAKN,GAAI6b,EAAIG,OAAQ,CAEd1iB,EAAO0jB,SAAStjB,GAAMsO,KAAKiV,GAAG,EAE9B3jB,EAAOsjB,MAAMC,MAAMjB,EAAU0B,KAAK5jB,EAAI,GAAK,KAC3CJ,EAAOsjB,MAAMG,MAAMnB,EAAU0B,KAAK3jB,EAAI,GAAK,KAG3C,UAAUwyB,GAAY,aAAeN,IAAe,KAAM,CACtDM,EAASvP,MAAMC,MAAMjB,EAAU0B,KAAK5jB,EAAI,IAAM,KAC9CyyB,EAASvP,MAAME,MAAMlB,EAAU0B,KAAK3jB,EAAI,IAAM,KAC9CwyB,EAAS1yB,SAASojB,KAAKjB,EAAUniB,SAASC,EAAI,GAC9CyyB,EAAS1yB,SAASqjB,KAAKlB,EAAUniB,SAASE,EAAIiiB,EAAU0B,KAAK3jB,EAAI,GACjEwyB,EAAS1yB,SAASsjB,MAAM,GAG5B,GAAI/lB,IAAS,GAAKA,IAAS,EAAG,CAC5BsC,EAAOsjB,MAAMG,KAAKnB,EAAU0B,KAAK1jB,EAAIwyB,EAAU9O,OAAO1jB,GACtDN,EAAOG,SAASsjB,KAAKnB,EAAUniB,SAASG,OACnC,CACLN,EAAOsjB,MAAME,KAAKnnB,KAAK0R,MAAQ+kB,EAAU9O,OAAO1jB,GAElDN,EAAOG,SAASojB,KAAKjB,EAAUniB,SAASC,GACxCJ,EAAOG,SAASqjB,KAAKlB,EAAUniB,SAASE,GAG1C,GAAIkiB,EAAIwQ,MAAO,CACb/yB,EAAOG,SAASojB,KAAKjB,EAAUniB,SAASC,GACxCJ,EAAOG,SAASqjB,KAAKlB,EAAUniB,SAASE,GAK1CL,EAAO0nB,YAAc,IAErB,GAAInF,EAAIE,QAAS,CAEfvW,EAAM3D,IAAIvI,GACV,GAAGuyB,GAAcM,EAAU3mB,EAAM3D,IAAIsqB,GACrC,OAAQn1B,GACN,KAAK,EACHrB,KAAK0mB,MAAMpe,QAAQ,GAAGgS,KAAK3W,GAC3B,GAAGuyB,EAAYl2B,KAAK0mB,MAAMpe,QAAQ,GAAGgS,KAAKkc,GAC1C,MACF,KAAK,EACHx2B,KAAK0mB,MAAMpe,QAAQ,GAAGgS,KAAK3W,GAC3B,MACF,KAAK,EACH3D,KAAK0mB,MAAMpe,QAAQ,GAAGgS,KAAK3W,GAC3B,MACF,KAAK,EACHA,EAAS0G,EAAS,gBAAgB8P,QAClCsc,EAAY1P,EAAYC,cAAc3c,EAAS,WAC/C,MACF,KAAK,EACH1G,EAAS0G,EAAS,iBAAiB8P,QACnCsc,EAAY1P,EAAYC,cAAc3c,EAAS,WAC/C,OAIN,GAAG6rB,EAAY,wCAONS,GAUT,IAAIC,EAAQ,CAAE7yB,GAAI,GAAIC,EAAG,IAAM,IAC/B,IAAI6yB,EAAgB,CAAE9yB,GAAI,EAAGC,GAAI,GAEjC,MAAO,GAAArB,OAAAm0B,IACFtI,EAAQkD,oBAAoBC,aAD1BmF,IAEFtI,EAAQkD,oBAAoBG,eAEhCrmB,IAAI,SAACurB,GAAD,MAAW,CACdhzB,EAAGgzB,EAAK/jB,GACRhP,EAAG+yB,EAAKjkB,GACR7O,EAAG8yB,EAAKhkB,GACRikB,EAAGD,EAAKlkB,MAETrH,IAAI,SAACyrB,GAAD,MAAa,CAChBD,EAAGC,EAAOhzB,EAAIgzB,EAAOlzB,EACrBmzB,EAAGD,EAAOD,EAAIC,EAAOjzB,EACrBD,EAAGkzB,EAAOhzB,EACVD,EAAGizB,EAAOD,KAEXxrB,IAAI,SAACmc,EAAK/D,GAET,IAAIuT,EAAO,GACPC,EAAU,CAAEpzB,GAAI,GAAID,EAAG,IAE3B,IAAIszB,GAAQ1P,EAAK5jB,EAAI4jB,EAAKqP,EAAIG,EAC9B,IAAItQ,EAAM8P,EAAU,GAAKtkB,KAAKO,IAAI+U,EAAK3jB,EAAI2jB,EAAKuP,EAAIC,EAAOC,EAAQpzB,GAAK,GAExE,MAAO,CAAEqzB,EAAMxQ,EACbc,EAAKqP,EAAIG,EAAOC,EAAQrzB,EACxB4jB,EAAKuP,EAAIC,EAAOC,EAAQpzB,iDAOZ2N,GAChB,IAAI9B,EAAQ7P,KAAK6P,MACjB,GAAI8B,IAAUnQ,UAAW,CACvBmQ,EAAQ3R,KAAK2R,MAEf,IAAI2lB,EAAcC,EAAYC,EAC9BF,EAAejtB,EAAS,sBAAsB8P,QAC9Cqd,EAAcntB,EAAS,qBAAqB8P,QAC5Cod,EAAaltB,EAAS,oBAAoB8P,QAE1C,IAAK,IAAI/X,EAAI,EAAGA,EAAIpC,KAAK0mB,MAAM8I,YAAYltB,OAAQF,IAAK,CACtDyN,EAAMkiB,OAAO/xB,KAAK0mB,MAAM8I,YAAYptB,IAGtCpC,KAAK0mB,MAAM8I,YAAYltB,OAAS,EAKhC,IAAIm1B,GAAkB,GACtB,IAAIC,EAAY13B,KAAK0R,MAAQ,EAC7B,IAAI+kB,EAAY1P,EAAYC,cAAc3c,EAAS,uBACnD,IAAI2d,EAAShoB,KAAK0R,MAAQ,EAAI+kB,EAAU9O,OAAO1jB,EAC/CqzB,EAAarQ,MAAMC,KAAKvV,EAAQ8kB,EAAU9O,OAAO5jB,GACjDuzB,EAAarQ,MAAMG,KAAKY,GACxBsP,EAAaxzB,SAASqjB,KAAKsQ,GAC3BH,EAAaxzB,SAASsjB,KAAKsQ,GAC3B13B,KAAK0mB,MAAM8I,YAAYlV,KAAKgd,GAE5BA,EAAajM,YAAc,IAE3BoL,EAAY1P,EAAYC,cAAc3c,EAAS,sBAC/C,IAAIod,EAASznB,KAAK0R,MAAQ,EAAI+kB,EAAU9O,OAAO5jB,EAE/CyzB,EAAYvQ,MAAMC,KAAKO,GACvB+P,EAAYvQ,MAAMG,KAAKY,GACvBwP,EAAY1zB,SAASojB,MAAMvV,EAAQ,EAAI3R,KAAK0R,MAAQ,EAAI,GACxD8lB,EAAY1zB,SAASqjB,KAAKsQ,GAC1BD,EAAY1zB,SAASsjB,KAAKsQ,GAC1B13B,KAAK0mB,MAAM8I,YAAYlV,KAAKkd,GAE5BD,EAAWtQ,MAAMC,KAAKO,GACtB8P,EAAWtQ,MAAMG,KAAKY,GACtBuP,EAAWzzB,SAASojB,KAAKvV,EAAQ,EAAI3R,KAAK0R,MAAQ,EAAI,GACtD6lB,EAAWzzB,SAASqjB,KAAKsQ,GACzBF,EAAWzzB,SAASsjB,KAAKsQ,GACzB13B,KAAK0mB,MAAM8I,YAAYlV,KAAKid,GAE5Bv3B,KAAK6P,MAAM3D,IAAIorB,GACft3B,KAAK6P,MAAM3D,IAAIqrB,GACfv3B,KAAK6P,MAAM3D,IAAIsrB,0CAIJG,EAASC,GACpB,IAAI72B,EAAS,GAGb,GACE42B,EAAQ/Q,OAAO7iB,IAAM6zB,EAAQhR,OAAO7iB,GACpC4zB,EAAQ/Q,OAAO5iB,IAAM4zB,EAAQhR,OAAO5iB,GACpC2zB,EAAQ9Q,IAAI9iB,IAAM6zB,EAAQ/Q,IAAI9iB,GAC9B4zB,EAAQ9Q,IAAI7iB,IAAM4zB,EAAQ/Q,IAAI7iB,EAC9B,CACAjD,EAAOylB,KAAO,KACd,OAAOzlB,EAGT,GACEsR,KAAKO,IAAI+kB,EAAQ/Q,OAAO7iB,EAAI4zB,EAAQ9Q,IAAI9iB,KACtCsO,KAAKO,IAAIglB,EAAQhR,OAAO7iB,EAAI6zB,EAAQ/Q,IAAI9iB,IAC1CsO,KAAKO,IAAI+kB,EAAQ/Q,OAAO5iB,EAAI2zB,EAAQ9Q,IAAI7iB,KACtCqO,KAAKO,IAAIglB,EAAQhR,OAAO5iB,EAAI4zB,EAAQ/Q,IAAI7iB,GAC1C,CACAjD,EAAOulB,WAAa,KAGtB,GACEjU,KAAKO,IAAI+kB,EAAQ9Q,IAAI7iB,EAAI2zB,EAAQ/Q,OAAO5iB,KACtCqO,KAAKO,IAAIglB,EAAQ/Q,IAAI7iB,EAAI4zB,EAAQhR,OAAO5iB,IAC1CqO,KAAKO,IAAI+kB,EAAQ9Q,IAAI9iB,EAAI4zB,EAAQ/Q,OAAO7iB,KACtCsO,KAAKO,IAAIglB,EAAQ/Q,IAAI9iB,EAAI6zB,EAAQhR,OAAO7iB,GAC1C,CACAhD,EAAOslB,OAAS,KAGlB,OAAOtlB,4CAIO42B,EAASC,GACvB,IAAI72B,EAAS,GAEb,GACE42B,EAAQ7zB,SAASC,IAAM6zB,EAAQ9zB,SAASC,GACxC4zB,EAAQ7zB,SAASE,IAAM4zB,EAAQ9zB,SAASE,GACxC2zB,EAAQhQ,KAAK5jB,IAAM6zB,EAAQjQ,KAAK5jB,GAChC4zB,EAAQhQ,KAAK3jB,IAAM4zB,EAAQjQ,KAAK3jB,EAChC,CACAjD,EAAOylB,KAAO,KACd,OAAOzlB,EAET,GACE42B,EAAQ7zB,SAASC,IAAM6zB,EAAQ9zB,SAASC,GACxC4zB,EAAQ7zB,SAASE,IAAM4zB,EAAQ9zB,SAASE,EACxC,CACAjD,EAAO21B,MAAQ,KAEjB,GACEiB,EAAQhQ,KAAK5jB,IAAM6zB,EAAQjQ,KAAK5jB,GAChC4zB,EAAQhQ,KAAK3jB,IAAM4zB,EAAQjQ,KAAK3jB,EAChC,CACAjD,EAAOslB,OAAS,KAGlB,OAAOtlB,4CAGOyxB,EAAKnQ,EAAS2C,EAAQrT,EAAOC,EAAM4hB,EAAWtO,GAC5D,IAAI7T,EAASrR,KAAK8kB,SAASY,aACzB8M,EAAIqF,SAASxV,GAASpS,KACtB+U,EACArT,EAAQ,GACRC,EACA4hB,EACAtO,EACA,IACA,EACA,IACA,KACA,CAAC,EAAE,EAAE,EAAE,IAET,OAAO7T,yCAGIymB,GAAkD,IAAAxrB,EAAAtM,KAAA,IAAzCqlB,EAAyChjB,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GAA9B,MAA8B,IAAvB01B,EAAuB11B,UAAAC,OAAA,GAAAD,UAAA,KAAAb,UAAAa,UAAA,GAAZ,MAAY,IAALmwB,EAAKnwB,UAAAC,OAAA,EAAAD,UAAA,GAAAb,UAC7DxB,KAAK0R,MAAQ8gB,EAAI9gB,MAEjB,GAAIlH,OAAOwtB,eAAgB,CACzB,OAEF,IAAInoB,EAAQ7P,KAAK6P,MACjB,GAAI0e,EAAUjsB,OAAS,EAAG,CACxB,IAAK,IAAIF,EAAI,EAAGA,EAAImsB,EAAUjsB,OAAQF,IAAK,CACzCyN,EAAMkiB,OAAOxD,EAAUnsB,IAEzBmsB,EAAY,GAGd,IAAI0J,EAAaH,GAAW,MAC5B,GAAIG,GAAc3J,GAAmB,MAAO,CAC1C,UAAW4J,UAAY,YAAa,CAClCA,SAASC,cAAc3F,EAAI4F,iBAC3B9J,EAAkB,UAEf,EAGP,IAAI+J,EAAkB3F,GAAgB,kBAAoB,EAC1D,IAAIrhB,EACJ,GAAI0mB,IAAa,KAAM,CACrB/3B,KAAK0mB,MAAMpe,QAAQvF,QAAQ,SAAAmwB,GACzB,IAAK,IAAI9wB,EAAI,EAAGA,EAAI8wB,EAAY5wB,OAAQF,IAAK,CAC3CkK,EAAKuD,MAAMkiB,OAAOmB,EAAY9wB,IAEhC8wB,EAAY5wB,OAAS,IAEvB,IAAIwwB,EAAmB,CAAC,YAAa,cAAe,WAAY,cAEhEA,EAAiB/vB,QAAQ,SAAAgwB,GAEvB,IAAK,IAAIvkB,EAAI,EAAGA,EAAIlC,EAAKoa,MAAMqM,GAAczwB,OAAQkM,IAAK,CACxDlC,EAAKuD,MAAMkiB,OAAOzlB,EAAKoa,MAAMqM,GAAcvkB,IAE7ClC,EAAKoa,MAAMqM,GAAczwB,OAAS,IAEpC+O,EAAS,CACPmC,MAAO,GACP3B,YAAa2gB,EAAIxvB,KAAK6O,YACtB2W,KAAM,GACN1W,UAAW0gB,EAAIxvB,KAAK8O,UACpBC,SAAUygB,EAAIxvB,KAAK+O,SACnB2f,oBAAqB,CACnBC,YAAa,GACbC,cAAe,GACfC,aAAc,GACdC,YAAa,KAGjB,IAAIwG,EAAat4B,KAAK8kB,SAASyT,aAC7B/F,EAAI5gB,KACJ4gB,EAAIe,UAAUC,WAEhB,IAAIgF,EAAkBx4B,KAAK8kB,SAAS2T,kCAClCpnB,EACAinB,EACA,GAEF,IAAII,EAAW14B,KAAK8kB,SAAS6T,qBAC3BH,EACAhG,EAAI7gB,MACJ,GAEFN,EAAO,uBAAyBrR,KAAK8kB,SAAS8T,qBAC5CF,EACAJ,EACA9F,EAAI7gB,MACJ,EACA6gB,EAAI9gB,OAENL,EAAO,QAAUrR,KAAK8kB,SAAS+T,cAC7B,CAAC,EAAErG,EAAI7gB,MAAQ,EAAG6gB,EAAI7gB,MAAQ,GAAI6mB,EAAgB,GAAG,IACrDF,EACA9F,EAAI9gB,WAED,CACL,IAAK,IAAItP,EAAI,EAAGA,EAAIi2B,EAAiBj2B,IAAK,CAExC,GAAIowB,EAAIqF,SAASrF,EAAInQ,SAASpS,OAASzO,UAAW,CAChD,IAAIs3B,EAAYtG,EAAIqF,SAAS,GAAGkB,cAC9BvG,EAAIwG,UACJxG,EAAI7gB,MACJ6gB,EAAI5gB,KACJ4gB,EAAIe,UAAUC,UACdhB,EAAI9gB,MACJ8gB,EAAIuC,KACJvC,EAAIwC,MAEN3jB,EAAS,CACPmC,MAAO,GACP3B,YAAa,GACb2W,KAAM,GACN1W,UAAW,GACXC,SAAU,GACV2f,oBAAqB,CACnBC,YAAa,GACbC,cAAe,GACfC,aAAc,GACdC,YAAa,KAGjB,IAAK,IAAI1vB,EAAI,EAAGA,EAAI02B,EAAU,oBAAoBx2B,OAAQF,GAAK,EAAG,CAChEiP,EAAO,eAAeiJ,KAAK,CACzBtH,GAAI8lB,EAAU,oBAAoB12B,GAAG,KACrC0Q,GAAIgmB,EAAU,oBAAoB12B,GAAG,KACrCwT,GAAIkjB,EAAU,oBAAoB12B,GAAG,KACrC2Q,GAAI+lB,EAAU,oBAAoB12B,EAAI,GAAG,KACzCyQ,GAAIimB,EAAU,oBAAoB12B,EAAI,GAAG,KACzCyT,GAAIijB,EAAU,oBAAoB12B,EAAI,GAAG,OAG7C,IAAK,IAAIA,EAAI,EAAGA,EAAI02B,EAAU,kBAAkBx2B,OAAQF,GAAK,EAAG,CAC9DiP,EAAO,aAAaiJ,KAAK,CACvBvH,GAAI+lB,EAAU,kBAAkB12B,GAAG,KACnCyQ,GAAIimB,EAAU,kBAAkB12B,GAAG,KACnCyT,GAAIijB,EAAU,kBAAkB12B,GAAG,KACnC4Q,GAAI8lB,EAAU,kBAAkB12B,EAAI,GAAG,KACvC0Q,GAAIgmB,EAAU,kBAAkB12B,EAAI,GAAG,KACvCwT,GAAIkjB,EAAU,kBAAkB12B,EAAI,GAAG,OAG3C,IAAK,IAAIA,EAAI,EAAGA,EAAI02B,EAAU,kBAAkBx2B,OAAQF,GAAK,EAAG,CAC9DiP,EAAO,YAAYiJ,KAAK,CACtBvH,GAAI+lB,EAAU,kBAAkB12B,GAAG,KACnCyQ,GAAIimB,EAAU,kBAAkB12B,GAAG,KACnCyT,GAAIijB,EAAU,kBAAkB12B,GAAG,KACnC4Q,GAAI8lB,EAAU,kBAAkB12B,EAAI,GAAG,KACvC0Q,GAAIgmB,EAAU,kBAAkB12B,EAAI,GAAG,KACvCwT,GAAIkjB,EAAU,kBAAkB12B,EAAI,GAAG,OAG3C,IAAK,IAAIA,EAAI,EAAGA,EAAI02B,EAAU,mBAAmBx2B,OAAQF,GAAK,EAAG,CAC/DiP,EAAO,YAAYiJ,KAAK,CACtBvH,GAAI+lB,EAAU,mBAAmB12B,GAAG,KACpCyQ,GAAIimB,EAAU,mBAAmB12B,GAAG,KACpCyT,GAAIijB,EAAU,mBAAmB12B,GAAG,KACpC4Q,GAAI8lB,EAAU,mBAAmB12B,EAAI,GAAG,KACxC0Q,GAAIgmB,EAAU,mBAAmB12B,EAAI,GAAG,KACxCwT,GAAIkjB,EAAU,mBAAmB12B,EAAI,GAAG,OAG5C,IAAIk2B,EAAat4B,KAAK8kB,SAASyT,aAC7B/F,EAAI5gB,KACJ4gB,EAAIe,UAAUC,WAEhB,IAAIgF,EAAkBx4B,KAAK8kB,SAAS2T,kCAClCpnB,EACAinB,EACA,GAEF,IAAII,EAAW14B,KAAK8kB,SAAS6T,qBAC3BH,EACAhG,EAAI7gB,MACJ,GAEFN,EAAO,uBAAyBrR,KAAK8kB,SAAS8T,qBAC5CF,EACAJ,EACA9F,EAAI7gB,MACJ,EACA6gB,EAAI9gB,OAENL,EAAO,QAAUrR,KAAK8kB,SAAS+T,cAC7B,CAAC,EAAE74B,KAAK2R,MAAQ,EAAG3R,KAAK2R,MAAQ,GAAI6mB,EAAgB,GAAG,IACvDF,EACA9F,EAAI9gB,OAEN8gB,EAAI4F,gBAAkB,gBACjB,CACL,GAAIp4B,KAAK6vB,sBAAwB,EAAG,CAClC2C,EAAIe,UAAUrO,UAAYllB,KAAK8kB,SAASmU,oBACtCj5B,KAAK8kB,SAASoU,eAAe1G,EAAIqF,SAASrF,EAAInQ,SAASpS,MACvD4f,mBACA2C,EAAI5gB,MAGRP,EAASwT,EAAc7kB,KAAK8kB,SAC1B0N,EAAIqF,SAASrF,EAAInQ,SAASpS,KAC1BuiB,EAAIwG,UACJxG,EAAI7gB,MACJ6gB,EAAI5gB,KACJ4gB,EAAIe,UAAUC,UACdhB,EAAIe,UAAUrO,UACdsN,EAAI9gB,MACJ,EACA,IACA2T,GAAYyS,EACZ,CAAC,EAAE,EAAE,EAAE,GAAI,MAAO,EAAGtF,EAAI2G,iBAAkB3G,EAAIre,YAEjD9C,EAAOgb,eAAiBrsB,KAAKkyB,yBAAyB7gB,GACtDA,EAAOwa,eAAiB7rB,KAAKmyB,yBAAyB9gB,GACtDA,EAAO8a,6BAA+BnsB,KAAKoyB,qCAAqC/gB,GAEhFmd,EAAUnd,EAEV,GAAImhB,EAAIqF,SAASrF,EAAInQ,SAASpS,KAAK,cAAgBzO,UAAW,CAC5DgxB,EAAI4F,gBACF5F,EAAIqF,SAASrF,EAAInQ,SAASpS,KAAK,aAAe,MAKxD,IAAIwiB,EAAiBC,GAAgB,YAAc,EACnD,IAAIC,EAAoB,CACtBf,cAAe,EACfD,YAAa,EACbE,aAAc,EACdC,YAAa,GAIf,IAAK,IAAItjB,EAAI,EAAGA,EAAIikB,EAAgBjkB,IAAK,CACvC,IAAIskB,EAAmB,CAAC,YAAa,cAAe,WAAY,QAAS,iBAAkB,kBAC3FA,EAAiB/vB,QAAQ,SAAAgwB,GAEvB,GAAIzmB,EAAK+E,OAAO0hB,KAAkBvxB,UAAW,CAC3C8K,EAAK+E,OAAO0hB,GAAgB,GAG9B,GAAIzmB,EAAK+E,OAAO0hB,IAAiBzmB,EAAK+E,OAAO0hB,GAAczwB,OAAS,GAAK+O,EAAO0hB,GAAczwB,OAASgK,EAAK+E,OAAO0hB,GAAczwB,OAAQ,CACvI,IAAI4jB,EAAM5Z,EAAK+E,OAAO0hB,GAAczwB,OAAS+O,EAAO0hB,GAAczwB,OAElE,GAAIgK,EAAKoa,MAAMqM,GAAe,CAC5B,IAAM,IAAIvkB,EAAIlC,EAAKoa,MAAMqM,GAAczwB,OAAS4jB,EAAK1X,EAAIlC,EAAKoa,MAAMqM,GAAczwB,OAASkM,IAAK,CAC9FqB,EAAMkiB,OAAOzlB,EAAKoa,MAAMqM,GAAcvkB,IAExClC,EAAKoa,MAAMqM,GAAczwB,OAASgK,EAAKoa,MAAMqM,GAAczwB,OAAS4jB,EAAM,EAAI,EAAI5Z,EAAKoa,MAAMqM,GAAczwB,OAAS4jB,GAKxH,IAAID,EAAY,GAChB,IAAID,EAAY,GAGhB,IAAK,IAAI5jB,EAAI,EAAGA,EAAIiP,EAAO0hB,GAAczwB,OAAQF,IAAK,CAEpD,UACSkK,EAAK+E,OAAO0hB,GAAc3wB,KAAO,cACvC61B,EACD,CACAhS,EAAU3L,KAAK,CACbsM,OAAQ,IAAInd,MAAMqnB,QAChBzf,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,OAE1BykB,IAAK,IAAIpd,MAAMqnB,QACbzf,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,SAG5B4jB,EAAU1L,KAAK,CACbsM,OAAQ,IAAInd,MAAMqnB,QAChBxkB,EAAK+E,OAAO0hB,GAAc3wB,GAAG,MAC7BkK,EAAK+E,OAAO0hB,GAAc3wB,GAAG,MAC7BkK,EAAK+E,OAAO0hB,GAAc3wB,GAAG,OAE/BykB,IAAK,IAAIpd,MAAMqnB,QACbxkB,EAAK+E,OAAO0hB,GAAc3wB,GAAG,MAC7BkK,EAAK+E,OAAO0hB,GAAc3wB,GAAG,MAC7BkK,EAAK+E,OAAO0hB,GAAc3wB,GAAG,aAG5B,CAEL6jB,EAAU3L,KAAK,CACbsM,OAAQ,IAAInd,MAAMqnB,QAChBzf,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,OAE1BykB,IAAK,IAAIpd,MAAMqnB,QACbzf,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,MACxBiP,EAAO0hB,GAAc3wB,GAAG,UAQhC,IAAK,IAAIA,EAAI,EAAGA,EAAI6jB,EAAU3jB,OAAQF,IAAK,CACzC,GAAI2wB,IAAiB,YAAa,CAChCzmB,EAAKyZ,qBAAqB3jB,EAAG4jB,EAAU5jB,GAAI6jB,EAAU7jB,SAChD,GAAI2wB,IAAiB,cAAe,CACzCzmB,EAAK4b,iBAAiB9lB,EAAG4jB,EAAU5jB,GAAI6jB,EAAU7jB,SAC5C,GAAI2wB,IAAiB,WAAY,CACtC,IAAIlL,OAAW,EACf,GAAIxW,EAAO0hB,GAAc3wB,GAAG2Q,GAAK1B,EAAO0hB,GAAc3wB,GAAG4Q,GAAM,EAAG,CAChE6U,EAAcxV,KAAKO,IAAIvB,EAAO0hB,GAAc3wB,GAAG2Q,IAAMV,KAAKO,IAAIvB,EAAO0hB,GAAc3wB,GAAG4Q,IAAM,OAAS,YAChG,CACL6U,EAAcxV,KAAKO,IAAIvB,EAAO0hB,GAAc3wB,GAAG2Q,IAAMV,KAAKO,IAAIvB,EAAO0hB,GAAc3wB,GAAG4Q,IAAM,OAAS,QAEvG1G,EAAKsb,oBAAoBxlB,EAAG4jB,EAAU5jB,GAAI6jB,EAAU7jB,GAAIylB,QACnD,GAAIkL,IAAiB,QAAS,CACnCzmB,EAAKib,iBAAiBnlB,EAAG4jB,EAAU5jB,GAAI6jB,EAAU7jB,SAC5C,GAAI2wB,IAAiB,iBAAkB,CAC5CzmB,EAAK8f,uBAAuBhqB,EAAG4jB,EAAU5jB,GAAI6jB,EAAU7jB,SAClD,GAAI2wB,IAAiB,iBAAkB,CAC5CzmB,EAAKqf,qBAAqBvpB,EAAG4jB,EAAU5jB,GAAI6jB,EAAU7jB,GAAIowB,EAAInQ,aAWnE,IAAI/Z,EAAU+I,EAAOqgB,oBACrB,IAAIsB,EAAmB,SAAnBA,EAA4Bzc,GAC9B,OAAO,IAAI9M,MAAMqnB,QACfze,KAAKO,IAAI2D,EAAKvD,GAAKuD,EAAKxD,IACxBV,KAAKO,IAAI2D,EAAKzD,GAAKyD,EAAK1D,IACxBR,KAAKO,IAAI2D,EAAKX,GAAKW,EAAKV,MAG5B,IAAIod,EAAuB,SAAvBA,EAAgC1c,GAClC,OAAO,IAAI9M,MAAMqnB,SACdva,EAAKvD,GAAKuD,EAAKxD,IAAM,EACtBwD,EAAKzD,GAAK,KACTyD,EAAKX,GAAKW,EAAKV,IAAM,IAG1B7V,KAAK0mB,MAAMpe,QAAQvF,QAAQ,SAAAmwB,GACzB,IAAK,IAAI9wB,EAAI,EAAGA,EAAI8wB,EAAY5wB,OAAQF,IAAK,CAC3CyN,EAAMkiB,OAAOmB,EAAY9wB,IAE3B8wB,EAAY5wB,OAAS,IAEvB,IAAK,IAAI6wB,KAAe7qB,EAAS,CAC/B,GAAI6qB,IAAgB,SAAU,CAC5B,SAEF,IAAID,EAAc5qB,EAAQ6qB,GAE1B,IAAIlN,EAAY,GAChB,IAAK,IAAI7jB,EAAI,EAAGA,EAAI8wB,EAAY5wB,OAAQF,IAAK,CAC3C6jB,EAAU3L,KAAK,CACbqN,KAAMqL,EAAiBE,EAAY9wB,IACnC0B,SAAUmvB,EAAqBC,EAAY9wB,MAG/C,IAAK,IAAIA,EAAI,EAAGA,EAAI6jB,EAAU3jB,OAAQF,IAAK,CACzCpC,KAAKozB,cACHhxB,EACAZ,UACAykB,EAAU7jB,GACVuwB,EAAkBQ,KAMxB,IAAK,IAAI/wB,EAAI,EAAGA,EAAIpC,KAAK0mB,MAAMyF,6BAA6B7pB,OAAQF,IAAK,CACvEyN,EAAMkiB,OAAO/xB,KAAK0mB,MAAMyF,6BAA6B/pB,IAEvDpC,KAAK0mB,MAAMyF,6BAA6B7pB,OAAS,EAEjD,IAAK,IAAIF,EAAI,EAAGA,EAAIiP,EAAO8a,6BAA6B7pB,OAAQF,IAAK,CACnEpC,KAAKgsB,iCAAiC3a,EAAO8a,6BAA6B/pB,IAI5E,IAAK,IAAIA,EAAI,EAAGA,EAAIpC,KAAK0mB,MAAM8B,KAAKlmB,OAAQF,IAAK,CAC/CyN,EAAMkiB,OAAO/xB,KAAK0mB,MAAM8B,KAAKpmB,IAE/BpC,KAAK0mB,MAAM8B,KAAKlmB,OAAS,EAEzB,IAAK,IAAIF,EAAI,EAAGA,EAAIiP,EAAOmX,KAAKlmB,OAAQF,IAAK,CAC3CpC,KAAKqoB,WAAWhX,EAAOmX,KAAKpmB,IAK9BpC,KAAK8sB,WAAa,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAC/D9sB,KAAKqzB,oBAAsB,CACzB,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,IAGFrzB,KAAKwrB,cAAgB,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAElE,IAAK,IAAIppB,EAAI,EAAGA,EAAIpC,KAAK0mB,MAAMlT,MAAMlR,OAAQF,IAAK,CAChDyN,EAAMkiB,OAAO/xB,KAAK0mB,MAAMlT,MAAMpR,IAGhC,IAAK,IAAIA,EAAI,EAAGA,EAAIpC,KAAK0mB,MAAMqG,YAAYzqB,OAAQF,IAAK,CACtDyN,EAAMkiB,OAAO/xB,KAAK0mB,MAAMqG,YAAY3qB,IAEtCpC,KAAK0mB,MAAMqG,YAAYzqB,OAAS,EAChCtC,KAAK0mB,MAAMlT,MAAMlR,OAAS,EAE1B,IAAK,IAAIF,EAAI,EAAGA,EAAIiP,EAAOmC,MAAMlR,OAAQF,IAAK,CAC5CpC,KAAKusB,WACH0G,EAAqB5hB,EAAOmC,MAAMpR,IAClC4wB,EAAiB3hB,EAAOmC,MAAMpR,IAC9BiP,EAAOmC,MAAMpR,GAAGf,KAChBgQ,EAAOmC,MAAMpR,GAAGkxB,KAChBd,EAAIe,UAAUC,UACdxzB,KAAKmU,YAKT,IAAK,IAAI/R,EAAI,EAAGA,EAAIpC,KAAK0mB,MAAMjT,QAAQnR,OAAQF,IAAK,CAClDyN,EAAMkiB,OAAO/xB,KAAK0mB,MAAMjT,QAAQrR,IAElCpC,KAAK0mB,MAAMjT,QAAQnR,OAAS,EAE5B,IAAK,IAAIF,EAAI,EAAGA,EAAIiP,EAAOoC,QAAQnR,OAAQF,IAAK,CAC9CpC,KAAKyoB,cACHwK,EAAqB5hB,EAAOoC,QAAQrR,IACpC4wB,EAAiB3hB,EAAOoC,QAAQrR,IAChCowB,EAAIe,UAAUC,UACdxzB,KAAKmU,YAKT,IAAK,IAAI/R,EAAI,EAAGA,EAAIpC,KAAKuvB,SAASjtB,OAAQF,IAAK,CAC7CyN,EAAMkiB,OAAO/xB,KAAKuvB,SAASntB,IAE7B,IAAK,IAAIA,EAAI,EAAGA,EAAIowB,EAAI5gB,KAAMxP,IAAK,CACjC,IAAIuR,EAAS,EACb,IAAK,IAAInF,EAAI,EAAGA,EAAIpM,EAAGoM,IAAK,CAC1BmF,GAAU6e,EAAIe,UAAUC,UAAUhlB,GAEpCmF,GAAU6e,EAAIe,UAAUC,UAAUpxB,GAAK,EACvC,IAAIP,EAAM,IAAI4H,MAAMgqB,SAEpBzzB,KAAKuvB,SAASjV,KAAKzY,GACnB7B,KAAKuvB,SAASntB,GAAG0B,SAASojB,KAAKsL,EAAI7gB,MAAQ,EAAI,IAC/C3R,KAAKuvB,SAASntB,GAAG0B,SAASqjB,KAAKxT,GAC/B3T,KAAKuvB,SAASntB,GAAG0B,SAASsjB,KAAK,KAC/BpnB,KAAKuvB,SAASntB,GAAGsxB,uBAAyB,KAC1C7jB,EAAM3D,IAAIlM,KAAKuvB,SAASntB,KAI5B,IAAIuxB,GAAqBC,GACvBviB,EACAmhB,EAAI5gB,KACJ4gB,EAAInQ,QACJmQ,EAAI7gB,MACJ6gB,EAAIre,YAGNnU,KAAK6zB,SAAWF,GAAmB,GACnC3zB,KAAK8zB,oBAAsB,CAACH,GAAmB,GAAIA,GAAmB,IAEtE3zB,KAAKqR,OAASA,EACdrR,KAAK+zB,wBAA0B1iB,EAAOqgB,oBAAoBsC,OAC1Dh0B,KAAKumB,eAAiBvmB,KAAK0R,MAM3B,UAAWuiB,gBAAkB,YAAa,CACxCA,eACEzB,EAAI7gB,MACJ6gB,EAAI0B,YACJl0B,KAAKqR,OAAOQ,YACZ7R,KAAKqR,OAAOS,UACZ9R,KAAKqR,OAAOU,UAMhB,IAAK,IAAI3P,GAAI,EAAGA,GAAIpC,KAAKkqB,2BAA2B,SAAS5nB,OAAQF,KAAK,EAU1E,GAAIijB,GAAYyS,EAAS,CAEvB1D,OAAOE,QAAQ,2BACV,CACLF,OAAOC,YAAY,8DAarBhG,GAAqBA,EACrBruB,KAAKo5B,kCA5yEavT,GAgzEtB,SAAS6M,GAAgB2G,GACvB,IAAIC,EAAWC,mBAAmB/uB,OAAOwrB,SAAS9S,OAAO1F,UAAU,IACjEgc,EAAgBF,EAASprB,MAAM,KAC/BurB,EACAr3B,EAEF,IAAKA,EAAI,EAAGA,EAAIo3B,EAAcl3B,OAAQF,IAAK,CACzCq3B,EAAiBD,EAAcp3B,GAAG8L,MAAM,KAExC,GAAIurB,EAAe,KAAOJ,EAAQ,CAChC,OAAOI,EAAe,KAAOj4B,UAAY,KAAOi4B,EAAe,KAKrE,SAAStO,GAAU0C,EAASC,GAC1B,IAAIC,EAAM,EACV,IAAIC,EAAM,EACV,QAASD,EAAMF,GAAWE,EAAMD,EAASE,GAAOH,GAAU,CACxDE,GAAOD,EAASE,GAChBA,IAEA,GAAIA,GAAO,GAAI,CACb,OAAQ,GAGZ,OAAOA,EAGT,SAAS4F,GAAiB8F,EAAeC,EAAWC,EAAOjoB,EAAO6E,GAChE,IAAIqjB,EAAgB,CAClBC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,KAAQ,IAGV,IAAIC,EAAiB,CAGnBC,EAAK,CACHC,QAAS,EACTC,WAAY,GACZjnB,MAAO,GACPknB,QAAS,IACTC,QAAS,IACTC,QAAS,IACTC,MAAO,EACPC,MAAO,GACPC,MAAO,GACPC,MAAO,IAITC,EAAK,CACHT,QAAS,GACTC,WAAY,IACZjnB,MAAO,IACPknB,QAAS,EACTC,QAAS,IACTC,QAAS,IACTC,MAAO,GACPC,MAAO,GACPC,MAAO,GACPC,MAAO,IAKTE,EAAK,CACHV,QAAS,EACTC,WAAY,IACZjnB,MAAO,GACPknB,QAAS,EACTC,QAAS,IACTC,QAAS,IACTC,MAAO,GACPC,MAAO,GACPC,MAAO,GACPC,MAAO,IAMTG,EAAK,CACHX,QAAS,EACTC,WAAY,GACZjnB,MAAO,GACPknB,QAAS,IACTC,QAAS,IACTC,QAAS,IACTC,MAAO,GACPC,MAAO,GACPC,MAAO,GACPC,MAAO,KAOX,IAAII,EAAW,CAGbb,EAAK,CACHC,QAAS,EACTC,WAAY,GACZjnB,MAAO,GACPknB,QAAS,IACTC,QAAS,IACTC,QAAS,IACTC,MAAO,EACPC,MAAO,GACPC,MAAO,GACPC,MAAO,IAKTC,EAAK,CACHT,QAAS,EACTC,WAAY,GACZjnB,MAAO,GACPknB,QAAS,IACTC,QAAS,IACTC,QAAS,IACTC,MAAO,GACPC,MAAO,GACPC,MAAO,GACPC,MAAO,IAKTE,EAAK,CACHV,QAAS,EACTC,WAAY,IACZjnB,MAAO,GACPknB,QAAS,EACTC,QAAS,IACTC,QAAS,IACTC,MAAO,GACPC,MAAO,GACPC,MAAO,GACPC,MAAO,IAKTG,EAAK,CACHX,QAAS,GACTC,WAAY,IACZjnB,MAAO,IACPknB,QAAS,EACTC,QAAS,IACTC,QAAS,IACTC,MAAO,GACPC,MAAO,GACPC,MAAO,GACPC,MAAO,KAIX,IAAIK,EAAaD,EAAS,KAE1B,GAAI5kB,IAAc,EAAG,CACnB,GAAIojB,KAASU,EAAgB,CAC3Be,EAAaf,EAAeV,QAEzB,CACL,GAAIA,KAASwB,EAAU,CACrBC,EAAaD,EAASxB,IAI1B,GAAIF,EAAchI,oBAAoBE,cAActvB,SAAW,EAAG,CAEhE,OAAO,MAGT,IAAIg5B,EAAY5B,EAAchI,oBAAoBE,cAC/CpmB,IAAI,SAAA+vB,GACH,OAAOA,EAAGxoB,GAAKwoB,EAAGvoB,KAEnBwoB,KAAK,SAACC,EAAOC,GACZ,OAAOA,EAASD,IAGpB,IAAIE,EAAYL,EAAUh5B,OAE1B,IAAIs5B,EACFtnB,SAASjC,KAAKwpB,MAAMF,EAAYN,EAAWZ,YAAa,KAAO,EACjE,IAAIqB,EAASR,EACVS,MAAM,EAAGH,GACTtoB,OAAO,SAAC0oB,EAAaC,GACpB,OAAOD,EAAcC,IAGzB,IAAIC,EACFN,GAAeN,EAAUh5B,OAASs5B,EAAcN,EAAUh5B,OAC5D,IAAI65B,EAAaL,EAASI,EAE1B,SAASE,EAAYC,GACnB,IAAIxI,EAAWwH,EAAWb,QAE1B,IAAK,IAAIj6B,KAAOs5B,EAAe,CAC7B,GAAIvlB,SAAS/T,IAAQ87B,EAAG,CACtBxI,EAAWgG,EAAct5B,GACzB,OAGJ,OAAOszB,EAGT,IAAIrgB,EAAQkmB,EAAclmB,OAAS,GACnC,IAAIC,EAAUimB,EAAcjmB,SAAW,GACvC,IAAI6oB,EAAkB9oB,EAAM7Q,OAAO8Q,GAEnC,IAAI8oB,EAAaD,EAAgBh6B,OAAS,EAAI+4B,EAAW7nB,MAAQ,EACjE+oB,GAAclqB,KAAKY,IAAIooB,EAAWX,QAAUW,EAAWV,QAAShB,GAIhE,IAAIjB,EAAW,CACb8D,EAAQJ,EAAYd,EAAU,IAAMiB,EAAY,GAChDC,EAAQJ,EAAYd,EAAUA,EAAUh5B,OAAS,GAAKi6B,GAAa,IAMrE,SAASE,EAAwBzlB,GAE/B,IAAM0lB,EAA+B,EACrC,IAAK,IAAIt6B,EAAI,EAAGA,EAAI4U,EAAM1U,OAAQF,IAAK,CACrC,GAAIiQ,KAAKO,IAAIoE,EAAM,GAAKA,EAAM5U,IAAMs6B,EAA8B,CAChE,OAAO,OAGX,OAAO,KAGT,IAAIC,EAASF,EAAwBnB,GACjC,KACAjpB,KAAKI,IAAI4oB,EAAWN,MAAO1oB,KAAKG,IAAI4O,MAAM/O,KAAMqmB,IAEpD,IAAIkE,EAASvqB,KAAKG,IAAI6oB,EAAWL,MAAO3oB,KAAKI,IAAI2O,MAAM/O,KAAMqmB,IAG7D,IAAImE,EAAiBlD,EAAYhoB,EAAQ,IACzC,IAAImrB,EACFV,EAAYD,GAAcI,EAAaZ,EAAYN,EAAW,WAEhE,IAAI0B,EAAW1qB,KAAKI,IAClBJ,KAAKG,IAAI6oB,EAAWP,MAAQ+B,EAAgBC,GAC5CzB,EAAWR,MAAQgC,EACnBlB,EAAYN,EAAWN,OAGzB,SAASyB,EAAQ78B,EAAOq9B,GACtB,IAAIC,EAAU3oB,SAAS3U,EAAQq9B,EAAc,IAAMA,EACnD,OAAOC,EAGT,IAAIC,EAAeV,EAAQO,EAAU,IAErC,GAAIvmB,GAAa,EAAG,CAClB,MAAO,CAACgmB,EAAQU,EAAe,IAAM,IAAKV,EAAQG,EAAS,IAAK,GAAIH,EAAQI,EAAS,IAAK,QACrF,CACL,MAAO,CAACM,EAAcP,EAAQC,yBC9pFlC,SAAAO,EAAAnmB,EAAAomB,GACA,IAAAtjB,GAAA,EACAxX,EAAA0U,GAAA,OAAAA,EAAA1U,OACA+6B,EAAA,EACAt8B,EAAA,GAEA,QAAA+Y,EAAAxX,EAAA,CACA,IAAA3C,EAAAqX,EAAA8C,GACA,GAAAsjB,EAAAz9B,EAAAma,EAAA9C,GAAA,CACAjW,EAAAs8B,KAAA19B,GAGA,OAAAoB,EAGAlB,EAAAC,QAAAq9B,sBCjBA,SAAAG,EAAA/8B,GACA,gBAAA+U,GACA,OAAAA,GAAA,KAAA9T,UAAA8T,EAAA/U,IAIAV,EAAAC,QAAAw9B,wBCbA,IAAAC,EAAgB30B,EAAQ,QAExB,IAAA9G,EAAA,WACA,IACA,IAAAqH,EAAAo0B,EAAAj+B,OAAA,kBACA6J,EAAA,GAAW,OACX,OAAAA,EACG,MAAA2F,KALH,GAQAjP,EAAAC,QAAAgC,sBCMA,SAAAuH,EAAA1J,GACA,OAAAA,EAGAE,EAAAC,QAAAuJ,wBCpBA,IAAA+Y,EAAmBxZ,EAAQ,QAC3Bwb,EAAexb,EAAQ,QAUvB,SAAA20B,EAAAjoB,EAAA/U,GACA,IAAAZ,EAAAykB,EAAA9O,EAAA/U,GACA,OAAA6hB,EAAAziB,KAAA6B,UAGA3B,EAAAC,QAAAy9B,wBChBA,IAAA3Y,EAAsBhc,EAAQ,QAC9B8Y,EAAmB9Y,EAAQ,QAG3B,IAAAvJ,EAAAC,OAAAC,UAGA,IAAA0iB,EAAA5iB,EAAA4iB,eAGA,IAAAub,EAAAn+B,EAAAm+B,qBAoBA,IAAAhd,EAAAoE,EAAA,WAA8C,OAAAviB,UAA9C,IAAkEuiB,EAAA,SAAAjlB,GAClE,OAAA+hB,EAAA/hB,IAAAsiB,EAAAriB,KAAAD,EAAA,YACA69B,EAAA59B,KAAAD,EAAA,WAGAE,EAAAC,QAAA0gB,sBC3BA,SAAAid,EAAAzmB,EAAA0mB,GACA,IAAA5jB,GAAA,EACAxX,EAAAo7B,EAAAp7B,OACA0mB,EAAAhS,EAAA1U,OAEA,QAAAwX,EAAAxX,EAAA,CACA0U,EAAAgS,EAAAlP,GAAA4jB,EAAA5jB,GAEA,OAAA9C,EAGAnX,EAAAC,QAAA29B,wBCnBA,IAAAnd,EAAiB1X,EAAQ,QACzB4b,EAAe5b,EAAQ,QA2BvB,SAAA+Y,EAAAhiB,GACA,OAAAA,GAAA,MAAA6kB,EAAA7kB,EAAA2C,UAAAge,EAAA3gB,GAGAE,EAAAC,QAAA6hB,wBChCA,IAAA8b,EAAgB70B,EAAQ,QACxB+X,EAAoB/X,EAAQ,QAa5B,SAAAkO,EAAAE,EAAAtF,EAAA0rB,EAAAO,EAAA58B,GACA,IAAA+Y,GAAA,EACAxX,EAAA0U,EAAA1U,OAEA86B,MAAAzc,GACA5f,MAAA,IAEA,QAAA+Y,EAAAxX,EAAA,CACA,IAAA3C,EAAAqX,EAAA8C,GACA,GAAApI,EAAA,GAAA0rB,EAAAz9B,GAAA,CACA,GAAA+R,EAAA,GAEAoF,EAAAnX,EAAA+R,EAAA,EAAA0rB,EAAAO,EAAA58B,OACO,CACP08B,EAAA18B,EAAApB,SAEK,IAAAg+B,EAAA,CACL58B,IAAAuB,QAAA3C,GAGA,OAAAoB,EAGAlB,EAAAC,QAAAgX,wBCrCA,IAAA+J,EAAqBjY,EAAQ,QAE7B,SAAAg1B,EAAAC,EAAAC,GACA,UAAAA,IAAA,YAAAA,IAAA,MACA,UAAAC,UAAA,sDAGAF,EAAAt+B,UAAAD,OAAAuf,OAAAif,KAAAv+B,UAAA,CACA0Y,YAAA,CACAtY,MAAAk+B,EACA57B,SAAA,KACAD,aAAA,QAGA,GAAA87B,EAAAjd,EAAAgd,EAAAC,GAGAj+B,EAAAC,QAAA89B,wBChBAh1B,EAAQ,OAARA,CAAuB,mBAAAma,EAAAib,EAAAC,GAEvB,gBAAA5vB,EAAAgJ,GACA,aACA,IAAA8L,EAAAJ,EAAA/iB,MACA,IAAAojB,EAAA/L,GAAA7V,oBAAA6V,EAAA2mB,GACA,OAAA5a,IAAA5hB,UAAA4hB,EAAAxjB,KAAAyX,EAAA8L,GAAA,IAAAhB,OAAA9K,GAAA2mB,GAAA3a,OAAAF,KACG8a,yBCCH,SAAAC,EAAAlnB,EAAA6M,GACA,IAAA/J,GAAA,EACAxX,EAAA0U,GAAA,OAAAA,EAAA1U,OACAvB,EAAAwN,MAAAjM,GAEA,QAAAwX,EAAAxX,EAAA,CACAvB,EAAA+Y,GAAA+J,EAAA7M,EAAA8C,KAAA9C,GAEA,OAAAjW,EAGAlB,EAAAC,QAAAo+B,wBCpBA,IAAAhd,EAAetY,EAAQ,QACvBu1B,EAAYv1B,EAAQ,QAkBpB,IAAA2C,EAAA2V,EAAAid,GAEAt+B,EAAAC,QAAAyL,wBCrBA,IAAA4xB,EAAkBv0B,EAAQ,QAC1Bs1B,EAAet1B,EAAQ,QACvB00B,EAAmB10B,EAAQ,QAC3B+a,EAAgB/a,EAAQ,SACxBgZ,EAAwBhZ,EAAQ,QAGhC,IAAAqb,EAAA5R,KAAAI,IAqBA,SAAA0rB,EAAAnnB,GACA,KAAAA,KAAA1U,QAAA,CACA,SAEA,IAAAA,EAAA,EACA0U,EAAAmmB,EAAAnmB,EAAA,SAAAonB,GACA,GAAAxc,EAAAwc,GAAA,CACA97B,EAAA2hB,EAAAma,EAAA97B,UACA,eAGA,OAAAqhB,EAAArhB,EAAA,SAAAwX,GACA,OAAAokB,EAAAlnB,EAAAsmB,EAAAxjB,MAIAja,EAAAC,QAAAq+B,wBC5CA,IAAA7Z,EAAiB1b,EAAQ,QAGzB,IAAAy1B,SAAAjxB,MAAA,UAAAA,WAAA9N,iBAAA8N,KAGA,IAAAzE,EAAA2b,GAAA+Z,GAAAxe,SAAA,cAAAA,GAEAhgB,EAAAC,QAAA6I", "file": "js/29cfa401.f4fe1cb7.js", "sourcesContent": ["/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\n\nmodule.exports = objectToString;\n", "function _AwaitValue(value) {\n  this.wrapped = value;\n}\n\nfunction _AsyncGenerator(gen) {\n  var front, back;\n\n  function send(key, arg) {\n    return new Promise(function (resolve, reject) {\n      var request = {\n        key: key,\n        arg: arg,\n        resolve: resolve,\n        reject: reject,\n        next: null\n      };\n\n      if (back) {\n        back = back.next = request;\n      } else {\n        front = back = request;\n        resume(key, arg);\n      }\n    });\n  }\n\n  function resume(key, arg) {\n    try {\n      var result = gen[key](arg);\n      var value = result.value;\n      var wrappedAwait = value instanceof _AwaitValue;\n      Promise.resolve(wrappedAwait ? value.wrapped : value).then(function (arg) {\n        if (wrappedAwait) {\n          resume(\"next\", arg);\n          return;\n        }\n\n        settle(result.done ? \"return\" : \"normal\", arg);\n      }, function (err) {\n        resume(\"throw\", err);\n      });\n    } catch (err) {\n      settle(\"throw\", err);\n    }\n  }\n\n  function settle(type, value) {\n    switch (type) {\n      case \"return\":\n        front.resolve({\n          value: value,\n          done: true\n        });\n        break;\n\n      case \"throw\":\n        front.reject(value);\n        break;\n\n      default:\n        front.resolve({\n          value: value,\n          done: false\n        });\n        break;\n    }\n\n    front = front.next;\n\n    if (front) {\n      resume(front.key, front.arg);\n    } else {\n      back = null;\n    }\n  }\n\n  this._invoke = send;\n\n  if (typeof gen.return !== \"function\") {\n    this.return = undefined;\n  }\n}\n\nif (typeof Symbol === \"function\" && Symbol.asyncIterator) {\n  _AsyncGenerator.prototype[Symbol.asyncIterator] = function () {\n    return this;\n  };\n}\n\n_AsyncGenerator.prototype.next = function (arg) {\n  return this._invoke(\"next\", arg);\n};\n\n_AsyncGenerator.prototype.throw = function (arg) {\n  return this._invoke(\"throw\", arg);\n};\n\n_AsyncGenerator.prototype.return = function (arg) {\n  return this._invoke(\"return\", arg);\n};\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    var ownKeys = Object.keys(source);\n\n    if (typeof Object.getOwnPropertySymbols === 'function') {\n      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n      }));\n    }\n\n    ownKeys.forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    });\n  }\n\n  return target;\n}\n\nvar data = { material:{ id:1,\n    material_id:1,\n    color:\"black\",\n    hex_color:4671303,\n    hex_handler_color:5592405,\n    backs_color:3750201,\n    opacity:0.6,\n    reflectivityValue:0.18,\n    reflectivityValueDoors:0.5 },\n  shadow:{ bakeShadow:\"Yes\",\n    useSoftShadows:\"Yes\",\n    source:{ position:[ { x:100 },\n        { y:200 },\n        { z:300 } ] } },\n  cubemap:null,\n  textures:null };\n\nvar dynamic = {\n  foo: function foo() {\n    return 'bar';\n  }\n};\n\nvar bundle = _objectSpread({}, data.material, dynamic);\n\n// General 'White Color' material settings\nvar material_id = 0;\nvar color = 'white';\nvar materialSettings = {\n  material_id: material_id,\n  color: color,\n  hex_color: 0xffffff,\n  hex_handler_color: 0xFFFFFF,\n  backs_color: 0xDADADA,\n  opacity: 0.35,\n  reflectivityValue: 0.02,\n  reflectivityValueDoors: 0.06\n};\nvar shadowSettings = {\n  lightPoint: {\n    x: 0,\n    y: 0,\n    z: 0\n  },\n  liveShadowEnabled: false\n};\n\nvar settings$1 = _objectSpread({\n  id: material_id\n}, materialSettings, shadowSettings);\n\n// General 'Gray Color' material settings\nvar material_id$1 = 3;\nvar color$1 = 'grey';\nvar materialSettings$1 = {\n  material_id: material_id$1,\n  color: color$1,\n  hex_color: 0xcfcfcf,\n  hex_handler_color: 0xC9C9C9,\n  backs_color: 0x818181,\n  opacity: 0.55,\n  reflectivityValue: 0.14,\n  reflectivityValueDoors: 0.17\n};\nvar shadowSettings$1 = {\n  lightPoint: {\n    x: 0,\n    y: 0,\n    z: 0\n  },\n  liveShadowEnabled: false\n};\n\nvar settings$2 = _objectSpread({\n  id: material_id$1\n}, materialSettings$1, shadowSettings$1);\n\n// General 'Ash Color' material settings\nvar material_id$2 = 5;\nvar color$2 = 'ash';\nvar materialSettings$2 = {\n  material_id: material_id$2,\n  color: color$2,\n  hex_color: 0x011615a,\n  hex_handler_color: 0x015b55,\n  backs_color: 0x013e3c,\n  opacity: 0.55,\n  reflectivityValue: 0.07,\n  reflectivityValueDoors: 0.3\n};\nvar shadowSettings$2 = {\n  lightPoint: {\n    x: 0,\n    y: 0,\n    z: 0\n  },\n  liveShadowEnabled: false\n};\n\nvar settings$3 = _objectSpread({\n  id: material_id$2\n}, materialSettings$2, shadowSettings$2);\n\n// General 'Maple Color' material settings\nvar material_id$3 = 2;\nvar color$3 = 'maple';\nvar materialSettings$3 = {\n  material_id: material_id$3,\n  color: color$3,\n  hex_color: 0x34ff88,\n  hex_handler_color: 0x646464,\n  backs_color: 0x313131,\n  opacity: 0.35,\n  reflectivityValue: 0.18,\n  reflectivityValueDoors: 0.17\n};\nvar shadowSettings$3 = {\n  lightPoint: {\n    x: 0,\n    y: 0,\n    z: 0\n  },\n  liveShadowEnabled: false\n};\n\nvar settings$4 = _objectSpread({\n  id: material_id$3\n}, materialSettings$3, shadowSettings$3);\n\n// General 'Purple Color' material settings\nvar material_id$4 = 4;\nvar color$4 = 'purple';\nvar materialSettings$4 = {\n  material_id: material_id$4,\n  color: color$4,\n  hex_color: 0x856b76,\n  hex_handler_color: 0x67525e,\n  backs_color: 0x5a464f,\n  opacity: 0.35,\n  reflectivityValue: 0.18,\n  reflectivityValueDoors: 0.29\n};\nvar shadowSettings$4 = {\n  lightPoint: {\n    x: 0,\n    y: 0,\n    z: 0\n  },\n  liveShadowEnabled: false\n};\n\nvar settings$5 = _objectSpread({\n  id: material_id$4\n}, materialSettings$4, shadowSettings$4);\n\n// General 'White Color' material settings\nvar material_id$5 = 0;\nvar color$5 = 'basic_white';\nvar materialSettings$5 = {\n  material_id: material_id$5,\n  color: color$5,\n  hex_color: 0xFFFFFF,\n  hex_handler_color: 0xFFFFFF,\n  backs_color: 0xfffefa,\n  opacity: 0,\n  reflectivityValue: 0,\n  reflectivityValueDoors: 0\n};\nvar shadowSettings$5 = {\n  lightPoint: {\n    x: 0,\n    y: 0,\n    z: 0\n  },\n  liveShadowEnabled: false\n};\n\nvar settings$6 = _objectSpread({\n  id: material_id$5\n}, materialSettings$5, shadowSettings$5);\n\n// General 'White Color' material settings\nvar material_id$6 = 3;\nvar color$6 = 'beige';\nvar materialSettings$6 = {\n  material_id: material_id$6,\n  color: color$6,\n  hex_color: 0xf3e9db,\n  hex_handler_color: 0xf3e9db,\n  backs_color: 0xebd8c7,\n  opacity: 0.35,\n  reflectivityValue: 0.08,\n  reflectivityValueDoors: 0.08\n};\nvar shadowSettings$6 = {\n  lightPoint: {\n    x: 0,\n    y: 0,\n    z: 0\n  },\n  liveShadowEnabled: false\n};\n\nvar settings$7 = _objectSpread({\n  id: material_id$6\n}, materialSettings$6, shadowSettings$6);\n\n// General 'White Color' material settings\nvar material_id$7 = 2;\nvar color$7 = 'indygo';\nvar materialSettings$7 = {\n  material_id: material_id$7,\n  color: color$7,\n  hex_color: 0x2C3541,\n  hex_handler_color: 0x2C3541,\n  backs_color: 0x1a1f2b,\n  opacity: 0.6,\n  reflectivityValue: 0.18,\n  reflectivityValueDoors: 0.18\n};\nvar shadowSettings$7 = {\n  lightPoint: {\n    x: 0,\n    y: 0,\n    z: 0\n  },\n  liveShadowEnabled: false\n};\n\nvar settings$8 = _objectSpread({\n  id: material_id$7\n}, materialSettings$7, shadowSettings$7);\n\n// General 'White Color' material settings\nvar material_id$8 = 4;\nvar color$8 = 'mint';\nvar materialSettings$8 = {\n  material_id: material_id$8,\n  color: color$8,\n  hex_color: 0xF0F7F3,\n  hex_handler_color: 0xF0F7F3,\n  backs_color: 0x7c9393,\n  opacity: 0.35,\n  reflectivityValue: 0.07,\n  reflectivityValueDoors: 0.07\n};\nvar shadowSettings$8 = {\n  lightPoint: {\n    x: 0,\n    y: 0,\n    z: 0\n  },\n  liveShadowEnabled: false\n};\n\nvar settings$9 = _objectSpread({\n  id: material_id$8\n}, materialSettings$8, shadowSettings$8);\n\n// General 'White Color' material settings\nvar material_id$9 = 1;\nvar color$9 = 'orange';\nvar materialSettings$9 = {\n  material_id: material_id$9,\n  color: color$9,\n  hex_color: 0xBE5A45,\n  hex_handler_color: 0xBE5A45,\n  backs_color: 0x742a1e,\n  opacity: 0.35,\n  reflectivityValue: 0.12,\n  reflectivityValueDoors: 0.12\n};\nvar shadowSettings$9 = {\n  lightPoint: {\n    x: 0,\n    y: 0,\n    z: 0\n  },\n  liveShadowEnabled: false\n};\n\nvar settings$10 = _objectSpread({\n  id: material_id$9\n}, materialSettings$9, shadowSettings$9);\n\nvar enabledMaterials = [settings$1, bundle, settings$4, settings$2, settings$5, settings$3];\nvar enabledBasicMaterials = [settings$6, settings$7, settings$8, settings$9, settings$10];\n\nvar getMaterialSettings = function getMaterialSettings(materialId, itemType) {\n  var m;\n\n  switch (itemType) {\n    case 0:\n      m = enabledMaterials.find(function (o) {\n        return o.material_id == materialId;\n      });\n      break;\n\n    case 1:\n      m = enabledBasicMaterials.find(function (o) {\n        return o.material_id == materialId;\n      });\n      break;\n\n    default:\n      m = enabledMaterials.find(function (o) {\n        return o.material_id == materialId;\n      });\n      break;\n  }\n\n  if (m == undefined) {\n    console.warn(\"UNDEFINED MATERIAL, taking 0 as fallback\", materialId, itemType);\n    m = itemType == 1 ? enabledBasicMaterials.find(function (o) {\n      return o.material_id == 0;\n    }) : enabledMaterials.find(function (o) {\n      return o.material_id == 0;\n    });\n  }\n\n  return [m.material_id, m.color, m.opacity, m.hex_color, m.hex_handler_color, m.backs_color, m.reflectivityValue, m.reflectivityValueDoors];\n};\n\nvar data$1 = { flags:{ ao:false },\n  shadows:null };\n\nconsole.log(data$1);\n\nvar scenePresets = function getScenePresetsByColor(_ref) {\n  return data$1;\n};\n\nexport { getMaterialSettings as getDefinitionForMaterialOld, getMaterialSettings as getDefinitionForMaterial, scenePresets as getSceneSettings };\n", "var root = require('./_root');\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\n\nmodule.exports = coreJsData;\n", "var coreJsData = require('./_coreJsData');\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = (function() {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? ('Symbol(src)_1.' + uid) : '';\n}());\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && (maskSrcKey in func);\n}\n\nmodule.exports = isMasked;\n", "var constant = require('./constant'),\n    defineProperty = require('./_defineProperty'),\n    identity = require('./identity');\n\n/**\n * The base implementation of `setToString` without support for hot loop shorting.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar baseSetToString = !defineProperty ? identity : function(func, string) {\n  return defineProperty(func, 'toString', {\n    'configurable': true,\n    'enumerable': false,\n    'value': constant(string),\n    'writable': true\n  });\n};\n\nmodule.exports = baseSetToString;\n", "//import loadColorTextures from './bukls';\n\nimport zip from 'lodash/zip';\nimport flattenDeep from 'lodash/flattenDeep';\n\nimport * as THREE from 'three';\nimport * as OBJLoader from 'three-obj-loader';\nOBJLoader(THREE);\n\nconst TYLKO_BLADE_OF_SLAWEK = 'o=}===>';\nconst url = (a, b) => new URL(b, a).href; \n\nclass AsyncLoader {\n\n\tconstructor({\n\t\troot = 'r_static/src_webgl/ivy/models/',\n\t\tmaxFilesPerCycle = 5,\n    } = { /* */ }) {\n\n\t\tthis.THREE = THREE;\n\n\t\tthis.elements = [];\n\t\tthis.cache = [];\n\n\t\tthis.base = url(window.origin, root);\n\t}\n\n\tloadColorTextures(colors, { fileType = 'jpg' } = { /* */ }) {\n\n\t\t// Loads all textures for provided color name\n\t\t// Loads colors set with priotity determined by horizontal \n\t\t// relation to onboardingColor index in array (neighbours first).\n\n\t\tlet baseUrl = url(this.base, \"textures/\");\n\n\t\tlet fix = (name, isName) => name.replace(' ', isName ? '-' : '_');\n\t\tlet names = ['hori', 'vert', 'support', 'support-drawer', 'shadowbox'];\n\t\tlet fileNames = ['top_bottom', 'left_right', 'support', 'support_drawer', 'shadow_box'];\n\t\n\t\tlet loadColor = (color) => \n\t\t\tzip(\n\t\t\t\tnames.map((name) => `${fix(color, true)}-${name}`),\n\t\t\t\tfileNames.map((name) => `${fix(color)}_${name}.${fileType}`)\n\t\t\t).map((texture) => this.loadTexture({ \n\t\t\t\tname: texture[0], \n\t\t\t\turl: url(baseUrl, texture[1]) \n\t\t\t})\n\t\t);\n\n\t\tlet jobs = [].concat(colors).map(loadColor);\n\n\t\treturn flattenDeep(jobs);\n\t}\n\n\taddTextureToCache(textureDescription) {\n\t\tthis.cache[textureDescription.name] = textureDescription.texture;\n\t\treturn textureDescription.name;\n\t}\n\n\taddModelToCache(model, name = null) {\n\t\tthis.cache[name?name:model.name] = model;\n\t\treturn model.name;\n\t}\n\n\tloadTexture(textureDescription, resolveBase = true) {\n\n\t\treturn new Promise((resolve) => {\n\t\t\tvar finialize = (add, t) => {\n\t\t\t\tresolve(add ? this.addTextureToCache({ \n\t\t\t\t\tname: textureDescription.name, \n\t\t\t\t\ttexture: t\n\t\t\t\t}) : \"ERROR\");\n\t\t\t}\n\n\t\t\tvar texture = new THREE.TextureLoader().load(\n\t\t\t\tresolveBase ? url(this.base, textureDescription.url) : textureDescription.url, \n\t\t\t\t(t) => finialize(true, t),\n\t\t\t\tundefined, \n\t\t\t\t(t) => finialize(false)\n\t\t\t);\n\t\t});\n\t}\n\n\tloadCubemap(name, {\n\t\t fileType = 'jpg',\n\t\t filenames = ['px', 'nx', 'py', 'ny', 'pz', 'nz']\n\t} = { /**/ }) {\n\n\t\tlet baseUrl = url(this.base, \"textures/\");\n\n\t\tlet urls = filenames.map((face) => url(url(baseUrl, `${name}/`), `${face}.${fileType}`));\n\n\t\treturn new Promise((resolve) => {\n\t\t\tlet finialize = (add) => (result) => \n\t\t\t\tresolve(add ? this.addTextureToCache({ \n\t\t\t\t\tname: name, \n\t\t\t\t\ttexture \n\t\t\t\t}) : \"ERROR\");\n\t\t\n\t\t\tlet texture = new THREE.CubeTextureLoader().load(\n\t\t\t\turls, \n\t\t\t\tfinialize(true), \n\t\t\t\tundefined, \n\t\t\t\tfinialize(false)\n\t\t\t);\n\t\t});\n\t}\n\n\tloadPlane(planeDescription, resolveBase = true) {\n\t\t\n\t\treturn new Promise((resolve) => {\n\t\t\tlet finialize = (add) => (result) => {\n\t\t\t\tvar geo = new THREE.PlaneGeometry( 100, 100, 32 );\t\t\n\t\t\t\tvar mat = new THREE.MeshBasicMaterial({ map : result });\n\t\t\t\tthis.addModelToCache(new THREE.Mesh(geo, mat), planeDescription.name);\n\t\t\t\t//console.log(\"SPECIAL\", planeDescription.name, this.cache[planeDescription.name]);\n\t\t\t\tresolve();\n\t\t\t};\n\n\t\t\tlet texture = new THREE.TextureLoader().load(\n\t\t\t\tresolveBase ? url(this.base, planeDescription.url) : planeDescription.url, \n\t\t\t\tfinialize(true), \n\t\t\t\tundefined, \n\t\t\t\tfinialize(false)\n\t\t\t);\n\t\t});\n\t}\n\n\tloadModels(config) {\n\n\n\t\tvar self = this;\n\n\t\tvar manager = new THREE.LoadingManager();\n\t\tvar objLoader = new THREE.OBJLoader( manager );\n\t\tvar xhrLoader = new THREE.XHRLoader( objLoader.manager );\n\n\t\tvar createModel = (name, texture_name, model) => {\n\t\n\n\t\t\tmodel.castShadow = false;\n\t\t\tmodel.receiveShadow = false;\n\t\t\tmodel.name = name;\n\t\t\tmodel.traverse(  ( child )  => {\n\t\t\t\tif ( child instanceof THREE.Mesh ) {\n\n\t\t\t\t\tif(texture_name) {\n\t\t\t\t\t\tchild.material = new THREE.MeshBasicMaterial({\n\t\t\t\t\t\t//\tmap: self.cache[\"basic_white-hori\"].clone(),\n\t\t\t\t\t\t\ttransparent: true,\n\t\t\t\t\t\t\tside: THREE.OneSide\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tif(texture_name==\"cast_shadow\") {\n\t\t\t\t\t\t\tchild.material.map = self.cache[\"cast_shadow\"];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif(texture_name==\"leg_texture\") {\n\t\t\t\t\t\t\tchild.material.map = self.cache[\"leg_texture\"];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\n\t\t\treturn model;\n\t\t}\n\n\t\t// TODO CROSS ORIGIN NOT NEEDED FOR NOW\n\t\t// loader.setCrossOrigin(objLoader.crossOrigin);\n\n\t\treturn new Promise((resolve) => {\n\n\t\t\txhrLoader.load( '/r_static/ivy.objx?14', (modelsData) => {\n\n\t\t\t\tvar models = modelsData.split(TYLKO_BLADE_OF_SLAWEK);\n\t\t\t\tmodels.splice(0, 1);\n\n\t\t\t\tmodels.map((model) => {\n\t\t\t\t\tif(!model) return;\n\n\t\t\t\t\tlet filename = model.match(/[\\w\\d-]+\\.obj/)[0];\n\t\t\t\t\tlet cfg = config[filename];\n\n\t\t\t\t\tif (cfg) {\n\t\t\t\t\t\tif (cfg[0] instanceof Array) {\n\t\t\t\t\t\t\tfor (var j = 0; j < cfg.length; j++) {\n\t\t\t\t\t\t\t\tlet currentModel = createModel(cfg[j][0], cfg[j][1], objLoader.parse(model));\n\t\t\t\t\t\t\t\tthis.addModelToCache(currentModel);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlet currentModel = createModel(cfg[0], cfg[1], objLoader.parse(model));\n\t\t\t\t\t\t\tthis.addModelToCache(currentModel);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tresolve();\n\n\t\t\t}, null, null);\n\t\t});\n\t}\n\n\tloadImage() {\n\t\treturn new Promise((resolve) => {\n\t\t\tvar img = new Image();\n\t\t\timg.addEventListener('load', (e) => {\n\t\t\t\tresolve();\n\t\t\t});\n\t\t});\n\t}\n\n\tgetElements() {\n\t\treturn this.cache;\n\t}\n\n\taddOnLoadedEvent(mainCallback) {\n\t\tmainCallback();\n\t}\n}\n\nexport { AsyncLoader };", "\nlet prepare_preloader = function (colorName){\n    let preloader_config = {};\n\n    preloader_config[\"2-vertical_edge.obj\"] = [\"vertical\", `${colorName}-vert`];\n    preloader_config[\"2-horizontal_edge.obj\"] = [\"horizontal\", `${colorName}-hori`];\n    preloader_config[\"2-horizontal_edge_plug.obj\"] = [\"horizontal-plug\", `${colorName}-hori`];\n    preloader_config[\"2-support.obj\"] = [\"support\", `${colorName}-support`];\n    preloader_config[\"2-support_drawer.obj\"] = [\"support-drawer\", `${colorName}-support-drawer`];\n    preloader_config[\"2-shadow_box.obj\"] = [\"shadow\", `${colorName}-shadowbox`];\n    preloader_config[\"2-shadow_box_left.obj\"] = [\"shadow-left\", `${colorName}-shadowbox`];\n    preloader_config[\"2-shadow_box_right.obj\"] = [\"shadow-right\", `${colorName}-shadowbox`];\n    preloader_config[\"2-left_right.obj\"] = [\"left-right\", `${colorName}-vert`];\n    preloader_config[\"2-top_bottom.obj\"] = [\"top-bottom\", `${colorName}-hori`];\n    preloader_config[\"insert.obj\"] = [\"insert\", `${colorName}-support`];\n\n    preloader_config[\"cast_shadow_center2.obj\"] = [\"cast-shadow-center\", \"cast_shadow\"];\n    preloader_config[\"cast_shadow_left.obj\"] = [\"cast-shadow-right\", \"cast_shadow\"];\n    preloader_config[\"cast_shadow_right.obj\"] = [\"cast-shadow-left\", \"cast_shadow\"];\n\n    preloader_config[\"doors.obj\"] = [\"door\", \"doors_open\", \"doors_close\"];\n    preloader_config[\"drawers.obj\"] = [\"drawer_front\", \"doors_open\"];\n    preloader_config[\"leg.obj\"] = [\"leg\", \"leg_texture\"];\n    preloader_config[\"handle_big.obj\"] = [\"handle_big\", \"doors_open\", \"doors_close\"];\n    preloader_config[\"handle_small.obj\"] = [\"handle_small\", \"doors_open\", \"doors_close\"];\n    preloader_config[\"handle_drawer.obj\"] = [\"handle_drawer\", \"doors_open\", \"doors_close\"];\n    preloader_config[\"handle_short_left.obj\"] = [\"handle_short_left\", \"handle_short_left_texture\"];\n    preloader_config[\"handle_short_left_shadow.obj\"] = [\"handle_short_left_shadow\", \"handle_short_left_texture\"];\n\n    return preloader_config;\n};\nexport { prepare_preloader as ObjectsConfig };", "import {getDefinitionForMaterial} from \"./presets/bundle/presets\";\n\n\nfunction designer<PERSON><PERSON>er(loader, required_build3d) {\n    var build3d = new required_build3d.Build3d(loader.getElements(), null, null, 'main', false);\n\n    build3d.setDesignerMode(1);\n    build3d.init3d();\n    build3d.createFacePlaneForRaycasting();\n\n    function setScene(scene){\n        build3d.setScene(scene);\n    }\n    function clearScene(scene){\n        build3d.clearScene(scene);\n    }\n    function setBackgroundScene(scene){\n        build3d.setBackgroundScene(scene);\n    }\n    function getPrice(json) {\n\n        let getPriceConf = function(override_color=null) {\n            let price_data = {\n                // HVS\n                'factor_hvs_area': 216.2 * 1.016, 'factor_verticals_item': 13.85, 'factor_supports_item': 10.36,\n                'factor_horizontals_item': 32.0, 'factor_horizontals_row': 60 * 0.65,\n                // BACKS\n                'factor_backs_item': 27, 'factor_backs_area': 92 * 1.11,\n                // DOORS\n                'factor_doors_item': 93 * 1.1,\n                // DRAWERS\n                'factor_drawers_multiplier': 1.25,\n                // MARGINS\n                'factor_margin_multiplier': 1.53,\n                'factor_hvs_mass': 13.5, 'factor_doors_mass': 12.0, 'factor_backs_mass': 9.75,\n                // OTHER\n                'factor_euro': 4.3,\n                'factor_material_multiplier': 1.0,\n            };\n            // if (self.depth == 400){\n            //     price_data['factor_verticals_item'] *= 1.08;\n            // }\n            // if (self.shelf_type == 0 && (override_color || self.material) == 4) {  // Oberżyna\n            //     price_data['factor_drawers_multiplier'] -= 0.028;\n            //     price_data['factor_hvs_area'] *= 1.067;\n            //     price_data['factor_backs_area'] = 97;\n            // }\n            // else if (self.shelf_type == 0 && (override_color || self.material) == 5){  // Fornir\n            //     price_data['factor_drawers_multiplier'] += 0.09;  // Cena szuflady ~300\n            //     price_data['factor_hvs_area'] *= 1.525;\n            //     price_data['factor_backs_area'] = 120;\n            // }\n            // else if (self.shelf_type == 1 && ((override_color || self.material) == 2)){\n            //     price_data['factor_material_multiplier'] = -0.15;\n            // }\n            // else if (self.shelf_type == 1){\n            //     price_data['factor_material_multiplier'] = -0.2;\n            // }\n\n            return price_data\n        };\n\n        let calculatePrice = function(points, material_override, width_override, number_of_rows_override) {\n\n            let prices = getPriceConf();\n            let depth = 320;\n\n            let material = material_override;\n            let width = width_override || 2400;\n            let rows = number_of_rows_override || 1;\n\n            let horizontals = points.horizontals;\n            let verticals = points.verticals;\n            let supports = points.supports;\n\n            var marza_x = function(waga_kg,\n                x=0.24285, y=0.0286624,\n                b=0.8, e=-0.12, min_=0.03, max_=0.5){\n\n                var base = Number((x * (1.57 - Math.atan(y * waga_kg - b)) + e).toFixed(2));\n                return (1 + Math.min(Math.max(base, min_), max_))\n            };\n            var get_hvs_area = function(horizontals, verticals, supports, depth){\n\n                let area = 0;\n                for(let i=0; i<verticals.length; i+=1) {\n                    area += Math.abs(verticals[i].y2 - verticals[i].y1) * depth;\n                }\n                for(let i=0; i<horizontals.length; i+=1) {\n                    area += Math.abs(horizontals[i].x2 - horizontals[i].x1) * depth;\n                }\n                for(let i=0; i<supports.length; i+=1) {\n                    area += Math.abs(supports[i].y2 - supports[i].y1) * Math.abs(supports[i].x2 - supports[i].x1);\n                }\n\n                return area / Math.pow(10, 6);\n            };\n\n\n            var total_price = 0;\n            var hvs_area = get_hvs_area(horizontals, verticals, supports, depth);\n\n            total_price += hvs_area * prices.factor_hvs_area;\n\n\n            total_price += verticals.length * prices.factor_verticals_item;\n\n            total_price += supports.length * prices.factor_supports_item;\n\n            // temporary place for new pricing. backs + doors\n\n            if (width > 2400){\n                total_price += (rows+1) * prices.factor_horizontals_row; //#self.factor_row = 60\n                total_price += horizontals.length * prices.factor_horizontals_item * 2;\n            }\n            else {\n                total_price += horizontals.length * prices.factor_horizontals_item;\n            }\n\n            if (points.backs.length > 0){\n                total_price += points.backs.length * prices.factor_backs_item;\n                let wall_material_price = (points.backs.map(b=>Math.abs((b['x2'] - b['x1']) * (b['y2'] - b['y1']))).reduce((sum, value)=>sum +value, 0) / 1000 / 1000) * prices.factor_backs_area;\n                total_price += wall_material_price;\n            }\n\n            total_price += points.doors.length * prices.factor_doors_item;\n\n            // drawers\n\n            points.drawers.map(d=>{\n                let width = Math.abs(d['x2'] - d['x1']);\n                let height = Math.abs(d['y2'] - d['y1']);\n                let drawers_price = ((width > 800 ? 198 : 152) + Math.pow(width,2) / 40000.0 + 0.05*width + 22) * (height < 220 ? 1 : (height < 310 ? 1.08 : 1.13));\n                drawers_price *= prices.factor_drawers_multiplier;\n                total_price += drawers_price;\n            });\n\n\n            total_price *= prices.factor_margin_multiplier;\n\n            // weight, plywood + doors + backs\n            var doors_weight = (points.doors.map(b=>Math.abs((b['x2'] - b['x1']) * (b['y2'] - b['y1']))).reduce((sum, value)=>sum +value, 0) / 1000 / 1000) * prices.factor_doors_mass;\n            var backs_weight = (points.backs.map(b=>Math.abs((b['x2'] - b['x1']) * (b['y2'] - b['y1']))).reduce((sum, value)=>sum +value, 0) / 1000 / 1000) * prices.factor_backs_mass;\n            var weight = (prices.factor_hvs_mass * hvs_area + doors_weight + backs_weight).toFixed(2); //+((cstm.prices.pricew * area + doors_weight + backs_weight).toFixed(2));\n            // place for Jonasz function\n            total_price *= marza_x(weight);\n\n            if (prices.factor_material_multiplier == 1){\n                //lets do nothing\n            } else {\n                total_price *= (1.0+prices.factor_material_multiplier);\n            }\n\n            total_price /= prices.factor_euro;\n\n            return Math.round(Math.ceil(total_price*1.23))\n        };\n        return calculatePrice(json, 0);\n    }\n\n    function setColor(color, shelf_type){\n        let [ , colorName] = getDefinitionForMaterial(parseInt(color), parseInt(shelf_type));\n\n        return Promise.all(loader.loadColorTextures(colorName)).then(() => {\n            build3d.setMaterialColor({ color, shelf_type, skipTracking:true });\n        });\n    }\n\n    function raycastComponentNo(camera, compList, mousePoint) {\n        let raycaster = new THREE.Raycaster();\n        raycaster.setFromCamera( mousePoint, camera );\n        let intersects = raycaster.intersectObjects( compList );\n        console.log(\"bbox\",intersects);\n        if(intersects.length>0) {\n            return intersects[0].object.no;\n        } else {\n            return false;\n        }\n    }\n\n    function displayShelf(json, color, scene, camera, renderer){\n       \n        let timeout;\n\n        if(true) {\n\n            /*\n            renderer.domElement.addEventListener(\"mousemove\", (e) => {\n                console.log(\"lol\");\n                if(timeout) window.clearTimeout(timeout);\n                timeout = window.setTimeout(() => {\n                    let point = new THREE.Vector2();\n                    let w = parseFloat(renderer.domElement.style.width), h = parseFloat(renderer.domElement.style.height);\n\n                    point.x = (e.clientX / (w)) * 2 - 1;\n                    point.y = (e.clientY / (h)) * 2 - 1;\n                    let no = raycastComponentNo(\n                         camera,\n                         build3d.componentHoverBoxes,\n                         point\n                    );\n                    if(!(no===false)) {\n                        if(this.mouseMoveSetComponent) {\n                            this.mouseMoveSetComponent(no);\n                        }\n                    } else {\n                        this.mouseMoveOnLeave();\n                    }\n                }, 50);\n            });\n            renderer.domElement.addEventListener(\"mousedown\", (e) => {\n                console.log(\"bbox\", camera, build3d.componentHoverBoxes);\n                console.log(\"bbox\", renderer.domElement);\n                let point = new THREE.Vector2();\n\n                let w = parseFloat(renderer.domElement.style.width), h = parseFloat(renderer.domElement.style.height);\n\n                point.x = (e.clientX / (w)) * 2 - 1;\n                point.y = (e.clientY / (h)) * 2 - 1;\n\n                let no = raycastComponentNo(\n                     camera,\n                     build3d.componentHoverBoxes,\n                     point\n                );\n                if(!(no===false)) {\n                    if(this.previewSetComponent) {\n                        this.previewSetComponent(no);\n                    }\n                } else {\n                    this.deselectComponent();\n                }\n            });\n            renderer.raycasting = true;\n            */\n        }\n\n        // console.log('output do renderera', json);\n        build3d.setScene(scene);\n        // clearScene(scene);\n        build3d.setShelfType(color.split(':')[0]);\n\n        if(json.horizontals) build3d.depth = Math.abs(json.horizontals[0].z1 - json.horizontals[0].z2);\n\n        setColor(color.split(':')[1], color.split(':')[0]);\n\n        build3d.rebuildWallsFromJson(json, scene);\n\n        renderer.render(scene, camera);\n \n    }\n    function getIndicatorBoxesPositions() {\n        return build3d.getIndicatorBoxesPositions()\n    }\n\n    function setDesignerMode(mode){\n        build3d.setDesignerMode(mode);\n    }\n    window.designerRendererWindow = this;\n\n    return {\n        getIndicatorBoxesPositions,\n        clearScene: clearScene,\n        displayShelf: displayShelf,\n        setDesignerMode: setDesignerMode,\n        setScene: setScene,\n        setColor: setColor,\n        setBackgroundScene: setBackgroundScene,\n        getPrice: getPrice\n    };\n}\nexport {designerRenderer}", "import { AsyncLoader } from './../utils/loader/asyncLoader.js';\nimport { ObjectsConfig } from './modelConfig.js';\nimport { getDefinitionForMaterial } from './presets/bundle/presets.js';\nimport { designer<PERSON>enderer} from \"./designerRenderer\";\n\nimport zip from 'lodash/zip';\nimport flattenDeep from 'lodash/flattenDeep';\n\n// var main = require('./main');\nvar loader;\n\nexport function WebdesignerRenderer() {\n\n  loader = new AsyncLoader();\n  var build = require('./build3d');\n\n  [ [\"cast_shadow\", \"cast_shadow3.png\"],\n    [\"leg_texture\", \"leg.jpg\"],\n    [\"doors_open\", \"doors_open.jpg\"],\n    [\"doors_open_white\", \"doors_open_white.jpg\"],\n    [\"doors_close\", \"doors_close.jpg\"],\n    [\"handle_short_left_texture\", \"handle_short_left.png\"],\n    [\"s3d_wall\", \"shadows/_wall.png\"],\n    [\"s3d_wall2\", \"shadows/_wall2.png\"],\n    [\"s3d_floor\", \"shadows/_floor.png\"],\n  ].map((texture) => {\n    loader.loadTexture({ name: texture[0], url: `textures/${texture[1]}` }, true);\n  });\n\n  let { material : materialId, shelf_type : shelfType } =  (window.cstm ? window.cstm.item : {material:0,shelf_type:0});\n  let [ , initialColorName ] = getDefinitionForMaterial(materialId, shelfType);\n\n  return Promise.all(flattenDeep([\n    loader.loadCubemap(\"cubemap\"),\n    loader.loadPlane({\n      name: \"wall_compartment_shadow\",\n      url: \"textures/wall_shadow.jpg\"\n    }),\n    Promise.all(flattenDeep([['basic_white', 'beige', 'indygo', 'mint', 'orange','ash','grey','white', 'purple','black' ].map(x=> loader.loadColorTextures(x))]))\n  ])).then(() => {\n    return loader.loadModels(ObjectsConfig(initialColorName)).then(() => {\n      return designerRenderer(loader, build);\n    });\n  });\n};\n", "var baseFlatten = require('./_baseFlatten');\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Recursively flattens `array`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * _.flattenDeep([1, [2, [3, [4]], 5]]);\n * // => [1, 2, 3, 4, 5]\n */\nfunction flattenDeep(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseFlatten(array, INFINITY) : [];\n}\n\nmodule.exports = flattenDeep;\n", "'use strict';\n\nfunction defaultOnError(err) {\n  throw new Error(err);\n}\n\nmodule.exports = function (THREE) {\n\n  /**\n   * <AUTHOR> / http://mrdoob.com/\n   */\n\n  THREE.OBJLoader = function (manager) {\n\n    this.manager = manager !== undefined ? manager : THREE.DefaultLoadingManager;\n\n    this.materials = null;\n\n    this.regexp = {\n      // v float float float\n      vertex_pattern: /^v\\s+([\\d|\\.|\\+|\\-|e|E]+)\\s+([\\d|\\.|\\+|\\-|e|E]+)\\s+([\\d|\\.|\\+|\\-|e|E]+)/,\n      // vn float float float\n      normal_pattern: /^vn\\s+([\\d|\\.|\\+|\\-|e|E]+)\\s+([\\d|\\.|\\+|\\-|e|E]+)\\s+([\\d|\\.|\\+|\\-|e|E]+)/,\n      // vt float float\n      uv_pattern: /^vt\\s+([\\d|\\.|\\+|\\-|e|E]+)\\s+([\\d|\\.|\\+|\\-|e|E]+)/,\n      // f vertex vertex vertex\n      face_vertex: /^f\\s+(-?\\d+)\\s+(-?\\d+)\\s+(-?\\d+)(?:\\s+(-?\\d+))?/,\n      // f vertex/uv vertex/uv vertex/uv\n      face_vertex_uv: /^f\\s+(-?\\d+)\\/(-?\\d+)\\s+(-?\\d+)\\/(-?\\d+)\\s+(-?\\d+)\\/(-?\\d+)(?:\\s+(-?\\d+)\\/(-?\\d+))?/,\n      // f vertex/uv/normal vertex/uv/normal vertex/uv/normal\n      face_vertex_uv_normal: /^f\\s+(-?\\d+)\\/(-?\\d+)\\/(-?\\d+)\\s+(-?\\d+)\\/(-?\\d+)\\/(-?\\d+)\\s+(-?\\d+)\\/(-?\\d+)\\/(-?\\d+)(?:\\s+(-?\\d+)\\/(-?\\d+)\\/(-?\\d+))?/,\n      // f vertex//normal vertex//normal vertex//normal\n      face_vertex_normal: /^f\\s+(-?\\d+)\\/\\/(-?\\d+)\\s+(-?\\d+)\\/\\/(-?\\d+)\\s+(-?\\d+)\\/\\/(-?\\d+)(?:\\s+(-?\\d+)\\/\\/(-?\\d+))?/,\n      // o object_name | g group_name\n      object_pattern: /^[og]\\s*(.+)?/,\n      // s boolean\n      smoothing_pattern: /^s\\s+(\\d+|on|off)/,\n      // mtllib file_reference\n      material_library_pattern: /^mtllib /,\n      // usemtl material_name\n      material_use_pattern: /^usemtl /\n    };\n  };\n\n  THREE.OBJLoader.prototype = {\n\n    constructor: THREE.OBJLoader,\n\n    load: function load(url, onLoad, onProgress, onError) {\n\n      var scope = this;\n      this.onError = onError || defaultOnError;\n\n      var loader = new THREE.FileLoader(scope.manager);\n      loader.setPath(this.path);\n      loader.load(url, function (text) {\n\n        onLoad(scope.parse(text));\n      }, onProgress, onError);\n    },\n\n    setPath: function setPath(value) {\n\n      this.path = value;\n    },\n\n    setMaterials: function setMaterials(materials) {\n\n      this.materials = materials;\n    },\n\n    _createParserState: function _createParserState() {\n\n      var state = {\n        objects: [],\n        object: {},\n\n        vertices: [],\n        normals: [],\n        uvs: [],\n\n        materialLibraries: [],\n\n        startObject: function startObject(name, fromDeclaration) {\n\n          // If the current object (initial from reset) is not from a g/o declaration in the parsed\n          // file. We need to use it for the first parsed g/o to keep things in sync.\n          if (this.object && this.object.fromDeclaration === false) {\n\n            this.object.name = name;\n            this.object.fromDeclaration = fromDeclaration !== false;\n            return;\n          }\n\n          var previousMaterial = this.object && typeof this.object.currentMaterial === 'function' ? this.object.currentMaterial() : undefined;\n\n          if (this.object && typeof this.object._finalize === 'function') {\n\n            this.object._finalize(true);\n          }\n\n          this.object = {\n            name: name || '',\n            fromDeclaration: fromDeclaration !== false,\n\n            geometry: {\n              vertices: [],\n              normals: [],\n              uvs: []\n            },\n            materials: [],\n            smooth: true,\n\n            startMaterial: function startMaterial(name, libraries) {\n\n              var previous = this._finalize(false);\n\n              // New usemtl declaration overwrites an inherited material, except if faces were declared\n              // after the material, then it must be preserved for proper MultiMaterial continuation.\n              if (previous && (previous.inherited || previous.groupCount <= 0)) {\n\n                this.materials.splice(previous.index, 1);\n              }\n\n              var material = {\n                index: this.materials.length,\n                name: name || '',\n                mtllib: Array.isArray(libraries) && libraries.length > 0 ? libraries[libraries.length - 1] : '',\n                smooth: previous !== undefined ? previous.smooth : this.smooth,\n                groupStart: previous !== undefined ? previous.groupEnd : 0,\n                groupEnd: -1,\n                groupCount: -1,\n                inherited: false,\n\n                clone: function clone(index) {\n                  var cloned = {\n                    index: typeof index === 'number' ? index : this.index,\n                    name: this.name,\n                    mtllib: this.mtllib,\n                    smooth: this.smooth,\n                    groupStart: 0,\n                    groupEnd: -1,\n                    groupCount: -1,\n                    inherited: false\n                  };\n                  cloned.clone = this.clone.bind(cloned);\n                  return cloned;\n                }\n              };\n\n              this.materials.push(material);\n\n              return material;\n            },\n\n            currentMaterial: function currentMaterial() {\n\n              if (this.materials.length > 0) {\n                return this.materials[this.materials.length - 1];\n              }\n\n              return undefined;\n            },\n\n            _finalize: function _finalize(end) {\n\n              var lastMultiMaterial = this.currentMaterial();\n              if (lastMultiMaterial && lastMultiMaterial.groupEnd === -1) {\n\n                lastMultiMaterial.groupEnd = this.geometry.vertices.length / 3;\n                lastMultiMaterial.groupCount = lastMultiMaterial.groupEnd - lastMultiMaterial.groupStart;\n                lastMultiMaterial.inherited = false;\n              }\n\n              // Ignore objects tail materials if no face declarations followed them before a new o/g started.\n              if (end && this.materials.length > 1) {\n\n                for (var mi = this.materials.length - 1; mi >= 0; mi--) {\n                  if (this.materials[mi].groupCount <= 0) {\n                    this.materials.splice(mi, 1);\n                  }\n                }\n              }\n\n              // Guarantee at least one empty material, this makes the creation later more straight forward.\n              if (end && this.materials.length === 0) {\n\n                this.materials.push({\n                  name: '',\n                  smooth: this.smooth\n                });\n              }\n\n              return lastMultiMaterial;\n            }\n          };\n\n          // Inherit previous objects material.\n          // Spec tells us that a declared material must be set to all objects until a new material is declared.\n          // If a usemtl declaration is encountered while this new object is being parsed, it will\n          // overwrite the inherited material. Exception being that there was already face declarations\n          // to the inherited material, then it will be preserved for proper MultiMaterial continuation.\n\n          if (previousMaterial && previousMaterial.name && typeof previousMaterial.clone === \"function\") {\n\n            var declared = previousMaterial.clone(0);\n            declared.inherited = true;\n            this.object.materials.push(declared);\n          }\n\n          this.objects.push(this.object);\n        },\n\n        finalize: function finalize() {\n\n          if (this.object && typeof this.object._finalize === 'function') {\n\n            this.object._finalize(true);\n          }\n        },\n\n        parseVertexIndex: function parseVertexIndex(value, len) {\n\n          var index = parseInt(value, 10);\n          return (index >= 0 ? index - 1 : index + len / 3) * 3;\n        },\n\n        parseNormalIndex: function parseNormalIndex(value, len) {\n\n          var index = parseInt(value, 10);\n          return (index >= 0 ? index - 1 : index + len / 3) * 3;\n        },\n\n        parseUVIndex: function parseUVIndex(value, len) {\n\n          var index = parseInt(value, 10);\n          return (index >= 0 ? index - 1 : index + len / 2) * 2;\n        },\n\n        addVertex: function addVertex(a, b, c) {\n\n          var src = this.vertices;\n          var dst = this.object.geometry.vertices;\n\n          dst.push(src[a + 0]);\n          dst.push(src[a + 1]);\n          dst.push(src[a + 2]);\n          dst.push(src[b + 0]);\n          dst.push(src[b + 1]);\n          dst.push(src[b + 2]);\n          dst.push(src[c + 0]);\n          dst.push(src[c + 1]);\n          dst.push(src[c + 2]);\n        },\n\n        addVertexLine: function addVertexLine(a) {\n\n          var src = this.vertices;\n          var dst = this.object.geometry.vertices;\n\n          dst.push(src[a + 0]);\n          dst.push(src[a + 1]);\n          dst.push(src[a + 2]);\n        },\n\n        addNormal: function addNormal(a, b, c) {\n\n          var src = this.normals;\n          var dst = this.object.geometry.normals;\n\n          dst.push(src[a + 0]);\n          dst.push(src[a + 1]);\n          dst.push(src[a + 2]);\n          dst.push(src[b + 0]);\n          dst.push(src[b + 1]);\n          dst.push(src[b + 2]);\n          dst.push(src[c + 0]);\n          dst.push(src[c + 1]);\n          dst.push(src[c + 2]);\n        },\n\n        addUV: function addUV(a, b, c) {\n\n          var src = this.uvs;\n          var dst = this.object.geometry.uvs;\n\n          dst.push(src[a + 0]);\n          dst.push(src[a + 1]);\n          dst.push(src[b + 0]);\n          dst.push(src[b + 1]);\n          dst.push(src[c + 0]);\n          dst.push(src[c + 1]);\n        },\n\n        addUVLine: function addUVLine(a) {\n\n          var src = this.uvs;\n          var dst = this.object.geometry.uvs;\n\n          dst.push(src[a + 0]);\n          dst.push(src[a + 1]);\n        },\n\n        addFace: function addFace(a, b, c, d, ua, ub, uc, ud, na, nb, nc, nd) {\n\n          var vLen = this.vertices.length;\n\n          var ia = this.parseVertexIndex(a, vLen);\n          var ib = this.parseVertexIndex(b, vLen);\n          var ic = this.parseVertexIndex(c, vLen);\n          var id;\n\n          if (d === undefined) {\n\n            this.addVertex(ia, ib, ic);\n          } else {\n\n            id = this.parseVertexIndex(d, vLen);\n\n            this.addVertex(ia, ib, id);\n            this.addVertex(ib, ic, id);\n          }\n\n          if (ua !== undefined) {\n\n            var uvLen = this.uvs.length;\n\n            ia = this.parseUVIndex(ua, uvLen);\n            ib = this.parseUVIndex(ub, uvLen);\n            ic = this.parseUVIndex(uc, uvLen);\n\n            if (d === undefined) {\n\n              this.addUV(ia, ib, ic);\n            } else {\n\n              id = this.parseUVIndex(ud, uvLen);\n\n              this.addUV(ia, ib, id);\n              this.addUV(ib, ic, id);\n            }\n          }\n\n          if (na !== undefined) {\n\n            // Normals are many times the same. If so, skip function call and parseInt.\n            var nLen = this.normals.length;\n            ia = this.parseNormalIndex(na, nLen);\n\n            ib = na === nb ? ia : this.parseNormalIndex(nb, nLen);\n            ic = na === nc ? ia : this.parseNormalIndex(nc, nLen);\n\n            if (d === undefined) {\n\n              this.addNormal(ia, ib, ic);\n            } else {\n\n              id = this.parseNormalIndex(nd, nLen);\n\n              this.addNormal(ia, ib, id);\n              this.addNormal(ib, ic, id);\n            }\n          }\n        },\n\n        addLineGeometry: function addLineGeometry(vertices, uvs) {\n\n          this.object.geometry.type = 'Line';\n\n          var vLen = this.vertices.length;\n          var uvLen = this.uvs.length;\n\n          for (var vi = 0, l = vertices.length; vi < l; vi++) {\n\n            this.addVertexLine(this.parseVertexIndex(vertices[vi], vLen));\n          }\n\n          for (var uvi = 0, l = uvs.length; uvi < l; uvi++) {\n\n            this.addUVLine(this.parseUVIndex(uvs[uvi], uvLen));\n          }\n        }\n\n      };\n\n      state.startObject('', false);\n\n      return state;\n    },\n\n    parse: function parse(text, debug) {\n      if (typeof debug === 'undefined') {\n        debug = true;\n      }\n\n      if (debug) {\n        console.time('OBJLoader');\n      }\n\n      var state = this._createParserState();\n\n      if (text.indexOf('\\r\\n') !== -1) {\n\n        // This is faster than String.split with regex that splits on both\n        text = text.replace(/\\r\\n/g, '\\n');\n      }\n\n      if (text.indexOf('\\\\\\n') !== -1) {\n\n        // join lines separated by a line continuation character (\\)\n        text = text.replace(/\\\\\\n/g, '');\n      }\n\n      var lines = text.split('\\n');\n      var line = '',\n          lineFirstChar = '',\n          lineSecondChar = '';\n      var lineLength = 0;\n      var result = [];\n\n      // Faster to just trim left side of the line. Use if available.\n      var trimLeft = typeof ''.trimLeft === 'function';\n\n      for (var i = 0, l = lines.length; i < l; i++) {\n\n        line = lines[i];\n\n        line = trimLeft ? line.trimLeft() : line.trim();\n\n        lineLength = line.length;\n\n        if (lineLength === 0) continue;\n\n        lineFirstChar = line.charAt(0);\n\n        // @todo invoke passed in handler if any\n        if (lineFirstChar === '#') continue;\n\n        if (lineFirstChar === 'v') {\n\n          lineSecondChar = line.charAt(1);\n\n          if (lineSecondChar === ' ' && (result = this.regexp.vertex_pattern.exec(line)) !== null) {\n\n            // 0                  1      2      3\n            // [\"v 1.0 2.0 3.0\", \"1.0\", \"2.0\", \"3.0\"]\n\n            state.vertices.push(parseFloat(result[1]), parseFloat(result[2]), parseFloat(result[3]));\n          } else if (lineSecondChar === 'n' && (result = this.regexp.normal_pattern.exec(line)) !== null) {\n\n            // 0                   1      2      3\n            // [\"vn 1.0 2.0 3.0\", \"1.0\", \"2.0\", \"3.0\"]\n\n            state.normals.push(parseFloat(result[1]), parseFloat(result[2]), parseFloat(result[3]));\n          } else if (lineSecondChar === 't' && (result = this.regexp.uv_pattern.exec(line)) !== null) {\n\n            // 0               1      2\n            // [\"vt 0.1 0.2\", \"0.1\", \"0.2\"]\n\n            state.uvs.push(parseFloat(result[1]), parseFloat(result[2]));\n          } else {\n\n            this.onError(\"Unexpected vertex/normal/uv line: '\" + line + \"'\");\n          }\n        } else if (lineFirstChar === \"f\") {\n\n          if ((result = this.regexp.face_vertex_uv_normal.exec(line)) !== null) {\n\n            // f vertex/uv/normal vertex/uv/normal vertex/uv/normal\n            // 0                        1    2    3    4    5    6    7    8    9   10         11         12\n            // [\"f 1/1/1 2/2/2 3/3/3\", \"1\", \"1\", \"1\", \"2\", \"2\", \"2\", \"3\", \"3\", \"3\", undefined, undefined, undefined]\n\n            state.addFace(result[1], result[4], result[7], result[10], result[2], result[5], result[8], result[11], result[3], result[6], result[9], result[12]);\n          } else if ((result = this.regexp.face_vertex_uv.exec(line)) !== null) {\n\n            // f vertex/uv vertex/uv vertex/uv\n            // 0                  1    2    3    4    5    6   7          8\n            // [\"f 1/1 2/2 3/3\", \"1\", \"1\", \"2\", \"2\", \"3\", \"3\", undefined, undefined]\n\n            state.addFace(result[1], result[3], result[5], result[7], result[2], result[4], result[6], result[8]);\n          } else if ((result = this.regexp.face_vertex_normal.exec(line)) !== null) {\n\n            // f vertex//normal vertex//normal vertex//normal\n            // 0                     1    2    3    4    5    6   7          8\n            // [\"f 1//1 2//2 3//3\", \"1\", \"1\", \"2\", \"2\", \"3\", \"3\", undefined, undefined]\n\n            state.addFace(result[1], result[3], result[5], result[7], undefined, undefined, undefined, undefined, result[2], result[4], result[6], result[8]);\n          } else if ((result = this.regexp.face_vertex.exec(line)) !== null) {\n\n            // f vertex vertex vertex\n            // 0            1    2    3   4\n            // [\"f 1 2 3\", \"1\", \"2\", \"3\", undefined]\n\n            state.addFace(result[1], result[2], result[3], result[4]);\n          } else {\n\n            this.onError(\"Unexpected face line: '\" + line + \"'\");\n          }\n        } else if (lineFirstChar === \"l\") {\n\n          var lineParts = line.substring(1).trim().split(\" \");\n          var lineVertices = [],\n              lineUVs = [];\n\n          if (line.indexOf(\"/\") === -1) {\n\n            lineVertices = lineParts;\n          } else {\n\n            for (var li = 0, llen = lineParts.length; li < llen; li++) {\n\n              var parts = lineParts[li].split(\"/\");\n\n              if (parts[0] !== \"\") lineVertices.push(parts[0]);\n              if (parts[1] !== \"\") lineUVs.push(parts[1]);\n            }\n          }\n          state.addLineGeometry(lineVertices, lineUVs);\n        } else if ((result = this.regexp.object_pattern.exec(line)) !== null) {\n\n          // o object_name\n          // or\n          // g group_name\n\n          // WORKAROUND: https://bugs.chromium.org/p/v8/issues/detail?id=2869\n          // var name = result[ 0 ].substr( 1 ).trim();\n          var name = (\" \" + result[0].substr(1).trim()).substr(1);\n\n          state.startObject(name);\n        } else if (this.regexp.material_use_pattern.test(line)) {\n\n          // material\n\n          state.object.startMaterial(line.substring(7).trim(), state.materialLibraries);\n        } else if (this.regexp.material_library_pattern.test(line)) {\n\n          // mtl file\n\n          state.materialLibraries.push(line.substring(7).trim());\n        } else if ((result = this.regexp.smoothing_pattern.exec(line)) !== null) {\n\n          // smooth shading\n\n          // @todo Handle files that have varying smooth values for a set of faces inside one geometry,\n          // but does not define a usemtl for each face set.\n          // This should be detected and a dummy material created (later MultiMaterial and geometry groups).\n          // This requires some care to not create extra material on each smooth value for \"normal\" obj files.\n          // where explicit usemtl defines geometry groups.\n          // Example asset: examples/models/obj/cerberus/Cerberus.obj\n\n          var value = result[1].trim().toLowerCase();\n          state.object.smooth = value === '1' || value === 'on';\n\n          var material = state.object.currentMaterial();\n          if (material) {\n\n            material.smooth = state.object.smooth;\n          }\n        } else {\n\n          // Handle null terminated files without exception\n          if (line === '\\0') continue;\n\n          this.onError(\"Unexpected line: '\" + line + \"'\");\n        }\n      }\n\n      state.finalize();\n\n      var container = new THREE.Group();\n      container.materialLibraries = [].concat(state.materialLibraries);\n\n      for (var i = 0, l = state.objects.length; i < l; i++) {\n\n        var object = state.objects[i];\n        var geometry = object.geometry;\n        var materials = object.materials;\n        var isLine = geometry.type === 'Line';\n\n        // Skip o/g line declarations that did not follow with any faces\n        if (geometry.vertices.length === 0) continue;\n\n        var buffergeometry = new THREE.BufferGeometry();\n\n        buffergeometry.addAttribute('position', new THREE.BufferAttribute(new Float32Array(geometry.vertices), 3));\n\n        if (geometry.normals.length > 0) {\n\n          buffergeometry.addAttribute('normal', new THREE.BufferAttribute(new Float32Array(geometry.normals), 3));\n        } else {\n\n          buffergeometry.computeVertexNormals();\n        }\n\n        if (geometry.uvs.length > 0) {\n\n          buffergeometry.addAttribute('uv', new THREE.BufferAttribute(new Float32Array(geometry.uvs), 2));\n        }\n\n        // Create materials\n\n        var createdMaterials = [];\n\n        for (var mi = 0, miLen = materials.length; mi < miLen; mi++) {\n\n          var sourceMaterial = materials[mi];\n          var material = undefined;\n\n          if (this.materials !== null) {\n\n            material = this.materials.create(sourceMaterial.name);\n\n            // mtl etc. loaders probably can't create line materials correctly, copy properties to a line material.\n            if (isLine && material && !(material instanceof THREE.LineBasicMaterial)) {\n\n              var materialLine = new THREE.LineBasicMaterial();\n              materialLine.copy(material);\n              material = materialLine;\n            }\n          }\n\n          if (!material) {\n\n            material = !isLine ? new THREE.MeshPhongMaterial() : new THREE.LineBasicMaterial();\n            material.name = sourceMaterial.name;\n          }\n\n          material.shading = sourceMaterial.smooth ? THREE.SmoothShading : THREE.FlatShading;\n\n          createdMaterials.push(material);\n        }\n\n        // Create mesh\n\n        var mesh;\n\n        if (createdMaterials.length > 1) {\n\n          for (var mi = 0, miLen = materials.length; mi < miLen; mi++) {\n\n            var sourceMaterial = materials[mi];\n            buffergeometry.addGroup(sourceMaterial.groupStart, sourceMaterial.groupCount, mi);\n          }\n\n          var multiMaterial = new THREE.MultiMaterial(createdMaterials);\n          mesh = !isLine ? new THREE.Mesh(buffergeometry, multiMaterial) : new THREE.LineSegments(buffergeometry, multiMaterial);\n        } else {\n\n          mesh = !isLine ? new THREE.Mesh(buffergeometry, createdMaterials[0]) : new THREE.LineSegments(buffergeometry, createdMaterials[0]);\n        }\n\n        mesh.name = object.name;\n\n        container.add(mesh);\n      }\n\n      if (debug) {\n        console.timeEnd('OBJLoader');\n      }\n\n      return container;\n    }\n\n  };\n};", "/** Used for built-in method references. */\nvar funcProto = Function.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to convert.\n * @returns {string} Returns the source code.\n */\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {}\n    try {\n      return (func + '');\n    } catch (e) {}\n  }\n  return '';\n}\n\nmodule.exports = toSource;\n", "var baseGetTag = require('./_baseGetTag'),\n    isObject = require('./isObject');\n\n/** `Object#toString` result references. */\nvar asyncTag = '[object AsyncFunction]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    proxyTag = '[object Proxy]';\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 9 which returns 'object' for typed arrays and other constructors.\n  var tag = baseGetTag(value);\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\n\nmodule.exports = isFunction;\n", "var Symbol = require('./_Symbol'),\n    isArguments = require('./isArguments'),\n    isArray = require('./isArray');\n\n/** Built-in value references. */\nvar spreadableSymbol = Symbol ? Symbol.isConcatSpreadable : undefined;\n\n/**\n * Checks if `value` is a flattenable `arguments` object or array.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is flattenable, else `false`.\n */\nfunction isFlattenable(value) {\n  return isArray(value) || isArguments(value) ||\n    !!(spreadableSymbol && value && value[spreadableSymbol]);\n}\n\nmodule.exports = isFlattenable;\n", "function _getPrototypeOf(o) {\n  module.exports = _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nmodule.exports = _getPrototypeOf;", "var identity = require('./identity'),\n    overRest = require('./_overRest'),\n    setToString = require('./_setToString');\n\n/**\n * The base implementation of `_.rest` which doesn't validate or coerce arguments.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @returns {Function} Returns the new function.\n */\nfunction baseRest(func, start) {\n  return setToString(overRest(func, start, identity), func + '');\n}\n\nmodule.exports = baseRest;\n", "/**\n * A faster alternative to `Function#apply`, this function invokes `func`\n * with the `this` binding of `thisArg` and the arguments of `args`.\n *\n * @private\n * @param {Function} func The function to invoke.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {Array} args The arguments to invoke `func` with.\n * @returns {*} Returns the result of `func`.\n */\nfunction apply(func, thisArg, args) {\n  switch (args.length) {\n    case 0: return func.call(thisArg);\n    case 1: return func.call(thisArg, args[0]);\n    case 2: return func.call(thisArg, args[0], args[1]);\n    case 3: return func.call(thisArg, args[0], args[1], args[2]);\n  }\n  return func.apply(thisArg, args);\n}\n\nmodule.exports = apply;\n", "function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nmodule.exports = _assertThisInitialized;", "var baseSetToString = require('./_baseSetToString'),\n    shortOut = require('./_shortOut');\n\n/**\n * Sets the `toString` method of `func` to return `string`.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar setToString = shortOut(baseSetToString);\n\nmodule.exports = setToString;\n", "/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\nmodule.exports = isArray;\n", "/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return value != null && typeof value == 'object';\n}\n\nmodule.exports = isObjectLike;\n", "var isArrayLike = require('./isArrayLike'),\n    isObjectLike = require('./isObjectLike');\n\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */\nfunction isArrayLikeObject(value) {\n  return isObjectLike(value) && isArrayLike(value);\n}\n\nmodule.exports = isArrayLikeObject;\n", "function _setPrototypeOf(o, p) {\n  module.exports = _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nmodule.exports = _setPrototypeOf;", "var isFunction = require('./isFunction'),\n    isMasked = require('./_isMasked'),\n    isObject = require('./isObject'),\n    toSource = require('./_toSource');\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n\n/** Used to detect host constructors (Safari). */\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n\n/** Used for built-in method references. */\nvar funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to detect if a method is native. */\nvar reIsNative = RegExp('^' +\n  funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&')\n  .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$'\n);\n\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n  var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\n\nmodule.exports = baseIsNative;\n", "/** Used to detect hot functions by number of calls within a span of milliseconds. */\nvar HOT_COUNT = 800,\n    HOT_SPAN = 16;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeNow = Date.now;\n\n/**\n * Creates a function that'll short out and invoke `identity` instead\n * of `func` when it's called `HOT_COUNT` or more times in `HOT_SPAN`\n * milliseconds.\n *\n * @private\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new shortable function.\n */\nfunction shortOut(func) {\n  var count = 0,\n      lastCalled = 0;\n\n  return function() {\n    var stamp = nativeNow(),\n        remaining = HOT_SPAN - (stamp - lastCalled);\n\n    lastCalled = stamp;\n    if (remaining > 0) {\n      if (++count >= HOT_COUNT) {\n        return arguments[0];\n      }\n    } else {\n      count = 0;\n    }\n    return func.apply(undefined, arguments);\n  };\n}\n\nmodule.exports = shortOut;\n", "/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\nmodule.exports = isObject;\n", "// @@search logic\nrequire('./_fix-re-wks')('search', 1, function (defined, SEARCH, $search) {\n  // ********* String.prototype.search(regexp)\n  return [function search(regexp) {\n    'use strict';\n    var O = defined(this);\n    var fn = regexp == undefined ? undefined : regexp[SEARCH];\n    return fn !== undefined ? fn.call(regexp, O) : new RegExp(regexp)[SEARCH](String(O));\n  }, $search];\n});\n", "var Symbol = require('./_Symbol'),\n    getRawTag = require('./_getRawTag'),\n    objectToString = require('./_objectToString');\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n    undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\nmodule.exports = baseGetTag;\n", "var root = require('./_root');\n\n/** Built-in value references. */\nvar Symbol = root.Symbol;\n\nmodule.exports = Symbol;\n", "/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */\nfunction baseTimes(n, iteratee) {\n  var index = -1,\n      result = Array(n);\n\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n  return result;\n}\n\nmodule.exports = baseTimes;\n", "var _typeof = require(\"../helpers/typeof\");\n\nvar assertThisInitialized = require(\"./assertThisInitialized\");\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return assertThisInitialized(self);\n}\n\nmodule.exports = _possibleConstructorReturn;", "var apply = require('./_apply');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * A specialized version of `baseRest` which transforms the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @param {Function} transform The rest array transform.\n * @returns {Function} Returns the new function.\n */\nfunction overRest(func, start, transform) {\n  start = nativeMax(start === undefined ? (func.length - 1) : start, 0);\n  return function() {\n    var args = arguments,\n        index = -1,\n        length = nativeMax(args.length - start, 0),\n        array = Array(length);\n\n    while (++index < length) {\n      array[index] = args[start + index];\n    }\n    index = -1;\n    var otherArgs = Array(start + 1);\n    while (++index < start) {\n      otherArgs[index] = args[index];\n    }\n    otherArgs[start] = transform(array);\n    return apply(func, this, otherArgs);\n  };\n}\n\nmodule.exports = overRest;\n", "/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction getValue(object, key) {\n  return object == null ? undefined : object[key];\n}\n\nmodule.exports = getValue;\n", "/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\nmodule.exports = freeGlobal;\n", "/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */\nfunction isLength(value) {\n  return typeof value == 'number' &&\n    value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n\nmodule.exports = isLength;\n", "var Symbol = require('./_Symbol');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n      tag = value[symToStringTag];\n\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\n\nmodule.exports = getRawTag;\n", "var baseGetTag = require('./_baseGetTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]';\n\n/**\n * The base implementation of `_.isArguments`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n */\nfunction baseIsArguments(value) {\n  return isObjectLike(value) && baseGetTag(value) == argsTag;\n}\n\nmodule.exports = baseIsArguments;\n", "/**\n * Creates a function that returns `value`.\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Util\n * @param {*} value The value to return from the new function.\n * @returns {Function} Returns the new constant function.\n * @example\n *\n * var objects = _.times(2, _.constant({ 'a': 1 }));\n *\n * console.log(objects);\n * // => [{ 'a': 1 }, { 'a': 1 }]\n *\n * console.log(objects[0] === objects[1]);\n * // => true\n */\nfunction constant(value) {\n  return function() {\n    return value;\n  };\n}\n\nmodule.exports = constant;\n", "var preparePoints = function(dnaTools, dnaJson, motion, width, rows, rowHeights, rowStyles,\n                       depth, half_material, support_size, snapping,\n                       shadow_settings=[2,2,4,2], gen_row_styles_for_buttons=true,\n                       styles_slider=-1, backpanels_rows=null, shelf_type=0) {\n    let points = dnaTools.get_elements(\n      dnaJson,\n      motion,\n      width,\n      rows,\n      rowHeights,\n      rowStyles,\n      depth,\n      half_material,\n      support_size,\n      snapping,\n      shadow_settings,\n    gen_row_styles_for_buttons,\n    styles_slider,\n    backpanels_rows || [],\n    shelf_type || 0\n    );\n    return points;\n};\n\n\nexport { preparePoints as preparePoints};\n", "import * as THREE from 'three';\n\n\nconst plankScaleMM = 18;\nconst MAX_DOOR_OPEN = 60;\n\nexport class BuildFunctions {\n    createSingleVertical(index, oldPoints, newPoints) {\n        let dif;\n        let scene = this.scene;\n        // check if there are any old points, used for initial run\n        if (typeof oldPoints !== \"undefined\") {\n            dif = this.compare_dnas(oldPoints, newPoints);\n        } else {\n            dif = {\n                newitem: true,\n                scaled: true,\n                only_moved: true\n            };\n        }\n        if (this.depth != this.depth_previous) {\n            dif.scaled = true;\n            dif.same = false;\n        }\n\n        // if its the same quit\n        if (dif.same === true) {\n            return true;\n        }\n        let vertical;\n        // if new item\n        if (dif.newitem) {\n            // clone the base element\n            vertical = this.elements[\"vertical\"].clone();\n        } else {\n            // else take a vertical already existing\n            vertical = this.walls.verticals[index];\n        }\n\n        let distance = Math.abs(newPoints.bottom.y - newPoints.top.y) + 3;\n\n        // if scaled set proper height\n        if (dif.scaled) {\n            let verticalBox = this.boundingBox.setFromObject(this.elements[\"vertical\"]);\n            vertical.scale.setX(1);\n            vertical.scale.setY(this.depth / 100);\n            vertical.scale.setZ(distance / 100);\n            vertical.rotation.x = -Math.PI / 2;\n            if (dif.newitem) {\n                // if its a new item it also needs width and depth\n                vertical.scale.setX(1);\n                vertical.scale.setY(this.depth / 100);\n                vertical.scale.setZ(distance / 100);\n            }\n            // set proper position\n            vertical.position.setX(newPoints.bottom.x);\n            vertical.position.setY(\n                Math.min(newPoints.bottom.y, newPoints.top.y) - 1.5\n            );\n        }\n\n        if (dif.only_moved) {\n            // if it was only moved changed x,y. Z is always the same\n            vertical.position.setX(newPoints.bottom.x);\n            vertical.position.setY(\n                Math.min(newPoints.bottom.y, newPoints.top.y) - 1.5\n            );\n            vertical.scale.setY(this.depth / 100);\n            vertical.scale.setZ(distance / 100);\n        }\n\n        if (dif.newitem) {\n            // if it is a new item add it to scene and to the furniture parts pool\n            vertical.name = \"vertical\";\n            scene.add(vertical);\n            this.walls.verticals.push(vertical);\n        }\n    }\n\n    createSingleBack(index, oldPoints, newPoints) {\n        let dif;\n        let scene = this.scene;\n\n        if (typeof oldPoints !== \"undefined\") {\n            dif = this.compare_dnas(oldPoints, newPoints);\n        } else {\n            dif = {\n                newitem: true,\n                scaled: true,\n                only_moved: true\n            };\n        }\n        if (dif.same === true) {\n            return true;\n        }\n\n        let back;\n        if (dif.newitem) {\n            back = this.elements[\"top-bottom\"].clone();\n        } else {\n            back = this.walls.backs[index];\n        }\n        let backBox = this.boundingBox.setFromObject(this.elements[\"top-bottom\"]);\n\n        let scaleX, scaleY;\n        back.rotation.x = -Math.PI;\n\n        scaleY = Math.abs(newPoints.bottom.y - newPoints.top.y - 18) / backBox.size().x;\n        scaleX = Math.abs(newPoints.bottom.x - newPoints.top.x - 18) / backBox.size().x;\n\n        back.position.setX((newPoints.bottom.x + newPoints.top.x) / 2);\n        back.position.setY(newPoints.bottom.y - 9);\n        back.position.setZ(newPoints.bottom.z);\n        back.scale.setY(scaleY);\n        back.scale.setX(scaleX);\n\n        // if (dif.scaled) {\n        //   back.scale.setZ(scaleZ);\n        //   back.scale.setX(scaleX);\n        //   if (dif.newitem) {\n        //     back.scale.setY(plankScale);\n        //   }\n        //   backBox = boundingBox.setFromObject(support);\n        //   back.position.set(\n        //     Math.min(newPoints.bottom.x, newPoints.top.x) + backBox.size().x / 2,\n        //     Math.min(newPoints.bottom.y, newPoints.top.y) +\n        //       Math.abs(newPoints.bottom.y - newPoints.top.y) -\n        //       supportBox.size().y / 2,\n        //     supportBox.size().z / 2 + 2 + 30\n        //   );\n        // }\n        //\n        // if (dif.only_moved) {\n        //   supportBox = boundingBox.setFromObject(support);\n        //   support.position.set(\n        //     Math.min(newPoints.bottom.x, newPoints.top.x) + supportBox.size().x / 2,\n        //     Math.min(newPoints.bottom.y, newPoints.top.y) +\n        //       Math.abs(newPoints.bottom.y - newPoints.top.y) -\n        //       supportBox.size().y / 2,\n        //     supportBox.size().z / 2 + 2 + 30\n        //   );\n        // }\n\n        if (dif.newitem) {\n            back.name = \"backss\";\n            scene.add(back);\n            if (this.walls.backs === undefined) {\n                this.walls.backs = [];\n            }\n            this.walls.backs.push(back);\n        }\n    }\n\n    createSingleSupport(index, oldPoints, newPoints, supportType) {\n\n        let dif;\n        let scene = this.scene;\n        if (typeof oldPoints !== \"undefined\") {\n            dif = this.compare_dnas(oldPoints, newPoints);\n        } else {\n            dif = {\n                newitem: true,\n                scaled: true,\n                only_moved: true\n            };\n        }\n        if (this.depth != this.depth_previous) {\n            dif.scaled = true;\n            dif.same = false;\n        }\n        if (dif.same === true) {\n            return true;\n        }\n\n        let support;\n        if (dif.newitem) {\n            support = this.elements[\"support\"].clone();\n        } else {\n            support = this.walls[\"supports\"][index];\n        }\n        let supportBox = this.boundingBox.setFromObject(this.elements[\"horizontal\"]);\n\n        let scaleX, scaleZ;\n        if (supportType == 'left') {\n            support.rotation.x = -Math.PI / 2;\n        } else {\n            support.rotation.x = Math.PI / 2;\n        }\n        if (dif.newitem) {\n\n        }\n        scaleZ =\n            Math.abs(newPoints.bottom.y - newPoints.top.y) / supportBox.size().x;\n        //scaleX = this.constants.gradientSupportWidth / supportBox.size().x;\n        scaleX =\n            Math.abs(newPoints.bottom.x - newPoints.top.x) / supportBox.size().x;\n\n        if (dif.scaled) {\n            support.scale.setZ(scaleZ);\n            support.scale.setX(scaleX);\n            if (dif.newitem) {\n                support.scale.setY(1);\n            }\n            supportBox = this.boundingBox.setFromObject(support);\n            support.position.set(\n                Math.min(newPoints.bottom.x, newPoints.top.x) + supportBox.size().x / 2,\n                Math.min(newPoints.bottom.y, newPoints.top.y) +\n                Math.abs(newPoints.bottom.y - newPoints.top.y) / 2,\n                0\n            );\n        }\n\n        if (dif.only_moved) {\n            supportBox = this.boundingBox.setFromObject(support);\n            support.position.set(\n                Math.min(newPoints.bottom.x, newPoints.top.x) + supportBox.size().x / 2,\n                Math.min(newPoints.bottom.y, newPoints.top.y) +\n                Math.abs(newPoints.bottom.y - newPoints.top.y) / 2,\n                9\n            );\n        }\n\n        if (dif.newitem) {\n            support.name = \"supports\";\n            scene.add(support);\n            if (this.walls[\"supports\"] === undefined) {\n                this.walls[\"supports\"] = [];\n            }\n            this.walls[\"supports\"].push(support);\n        }\n    }\n\n    createHorizontal(index, oldPoints, newPoints) {\n        let dif;\n        let scene = this.scene;\n        if (typeof oldPoints !== \"undefined\") {\n            dif = this.compare_dnas(oldPoints, newPoints);\n        } else {\n            dif = {\n                newitem: true,\n                scaled: true,\n                only_moved: true\n            };\n        }\n        if (this.depth != this.depth_previous) {\n            dif.scaled = true;\n            dif.same = false;\n        }\n        if (dif.same === true) {\n            return true;\n        }\n        let horizontal;\n        if (dif.newitem) {\n            horizontal = this.elements[\"horizontal\"].clone();\n        } else {\n            horizontal = this.walls.horizontals[index];\n        }\n\n        let distance = Math.abs(newPoints.bottom.x - newPoints.top.x);\n        if (dif.scaled) {\n            let horizontalBox = this.boundingBox.setFromObject(this.elements[\"horizontal\"]);\n            let scale = distance / horizontalBox.size().x;\n            horizontal.scale.setX(scale);\n            horizontal.rotation.x = -Math.PI / 2;\n\n            horizontal.scale.setZ(1);\n            horizontal.scale.setY(this.depth / horizontalBox.size().y);\n            horizontal.position.setY(newPoints.bottom.y);\n            horizontal.position.setX(\n                Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2\n            );\n        }\n\n        if (dif.only_moved) {\n            horizontal.position.setY(newPoints.bottom.y);\n            horizontal.position.setX(\n                Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2\n            );\n        }\n\n        if (dif.newitem) {\n            horizontal.name = \"horizontal\";\n            this.scene.add(horizontal);\n            this.walls.horizontals.push(horizontal);\n        }\n    }\n\n    createLegs(position) {\n\n        let leg = this.elements[\"leg\"].clone();\n        let legBox = this.boundingBox.setFromObject(this.elements[\"leg\"]);\n        leg.position.setX(position.x1);\n        leg.position.setZ(position.z1);\n        leg.position.setY((position.y1+position.y2)/2 + Math.abs(position.y1 - position.y2) / 2);\n        leg.scale.setZ(Math.abs(position.y1 - position.y2) / legBox.size().z);\n\n        leg.rotation.x = leg.rotation.x + 90 * Math.PI / 180;\n\n\n        leg.name = \"leg\";\n        this.scene.add(leg);\n        this.walls.legs.push(leg);\n    }\n\n    // the same as verticals, horizontals but also takes what type of shadow it is to know which element to clone\n\n    createDrawers(position, size, rowHeights, shelf_type, drawer_description) {\n        let selected = !!drawer_description.selected;\n        if (this.designer_mode > 0) { // total customized version, for webdesigner\n            let door_group = new THREE.Group();\n            let scene = this.scene;\n            let door_handler_width = drawer_description.door_handler_width;\n            let door_handler_height = drawer_description.door_handler_height;\n\n            door_group.name = \"drawer group\";\n            let offset = drawer_description.innerOffset;\n            let drawer_cutout = drawer_description.drawer_cutout;\n            let front_handling_size = drawer_description.front_handling_size;\n            // let front_offset_from_top = 4;\n            // let front_height_offset = 37;\n            // let front_thickness = 18;\n\n            let bottom_offset = drawer_description.bottom_offset;\n            let bottom_thickness = drawer_description.bottom_thickness;\n            let bottom_depth = drawer_description.bottom_depth;\n\n            // let back_width_offset = 14;\n            // let back_thickness = 13;\n            let back_height = drawer_description.back_height;\n\n            let sides_length = drawer_description.sides_length;\n            // let sides_thickness = 13;\n            let sides_height = drawer_description.sides_height;\n\n            let doorBox;\n\n            // whole group\n            door_group.position.setX(position.x);\n            door_group.position.setY(position.y);\n            door_group.position.setZ(position.z + (selected || this.designer_mode == 3 ? 300 :0));\n\n\n            // handler\n            if (drawer_description.type == 1) {\n                // front\n                let door = this.elements[\"door\"].clone();\n                let self = this;\n                door.traverse(function (child) {\n                    if (child instanceof THREE.Mesh) {\n                        /*child.material = self.magical_materials_for_rows['doors'][row_number];*/\n                    }\n                });\n                door.name = \"szufladaFront\";\n                //console.log('color szuflady', ivy.material);\n\n                door.position.setX(0);\n                door.position.setY(\n                    position.y - door_group.position.y\n                );\n                door.position.setZ(0);\n\n                doorBox = this.boundingBox.setFromObject(this.elements[\"drawer_front\"]);\n\n                door.scale.setX((size.x - offset) / doorBox.size().x);\n                door.scale.setY((size.y - offset) / doorBox.size().y);\n                door.scale.setZ(size.z / doorBox.size().z);\n\n                if (this.designer_mode != 2) {\n                    door_group.add(door);\n                }\n                let drawer_handler = this.elements[\"handle_short_left\"].clone();\n                drawer_handler.name = \"Handler\";\n                // console.log('posy', size.y);\n                drawer_handler.rotation.x = -Math.PI / 2;\n                drawer_handler.position.setX(position.x - size.x / 2 - door_group.position.x + door_handler_width / 2 + offset);\n                drawer_handler.position.setY(size.y / 2 - door_handler_height / 2);\n                drawer_handler.position.setZ(size.z - plankScaleMM / 2 - 1);\n\n                doorBox = this.boundingBox.setFromObject(drawer_handler);\n                drawer_handler.scale.setY(1);\n                drawer_handler.scale.setX(1);\n                drawer_handler.scale.setZ(1);\n\n                drawer_handler.traverse(function (child) {\n                    if (\n                        child instanceof THREE.Mesh &&\n                        typeof child.material !== \"undefined\"\n                    ) {\n                        child.material.needsUpdate = true;\n                        child.geometry.center();\n                    }\n                });\n                door_group.add(drawer_handler);\n\n                let drawer_handler_shadow = this.elements[\"handle_short_left_shadow\"].clone();\n                drawer_handler_shadow.name = \"Handler shadow\";\n\n                drawer_handler_shadow.rotation.x = -Math.PI / 2;\n                drawer_handler_shadow.position.setX(position.x - size.x / 2 - door_group.position.x);\n                drawer_handler_shadow.position.setY(size.y / 2 - door_handler_height / 2 + 8);\n                drawer_handler_shadow.position.setZ(size.z - plankScaleMM + 3);\n\n                doorBox = this.boundingBox.setFromObject(drawer_handler_shadow);\n                drawer_handler_shadow.scale.setY(1);\n                drawer_handler_shadow.scale.setX(1);\n                drawer_handler_shadow.scale.setZ(1);\n\n                door_group.add(drawer_handler_shadow);\n\n            } else {\n                // front\n                let door = this.elements[\"drawer_front\"].clone();\n                let self = this;\n                door.traverse(function (child) {\n                    if (child instanceof THREE.Mesh) {\n                        // child.material = self.magical_materials_for_rows['doors'][0];\n                    }\n                });\n                door.name = \"szufladaFront\";\n                //console.log('color szuflady', ivy.material);\n\n                door.position.setX(0);\n                door.position.setY(\n                    position.y - front_handling_size / 2 - door_group.position.y\n                );\n                door.position.setZ(0);\n\n                doorBox = this.boundingBox.setFromObject(this.elements[\"drawer_front\"]);\n\n                door.scale.setX((size.x + offset * 2) / doorBox.size().x);\n                door.scale.setY((size.y - front_handling_size - offset) / doorBox.size().y);\n                door.scale.setZ(size.z / doorBox.size().z);\n                if (this.designer_mode != 2) {\n                    door_group.add(door);\n                }\n                // handler\n                let door_handler = this.elements[\"handle_drawer\"].clone();\n                door_handler.name = \"Trzymadelko\";\n\n                // door_handler.rotation.z = door_handler.rotation.z - 90 * Math.PI / 180;\n\n                door_handler.rotation.y = door_handler.rotation.y + 90 * Math.PI / 180;\n\n                //door_handler.position.setX(position.x+(handling_flip*((size.x/2)-(handling_size))) + (handling_flip *offset) - door_group.position.x);\n                door_handler.position.setY(\n                    position.y +\n                    (size.y / 2 - front_handling_size) +\n                    offset -\n                    door_group.position.y + 3\n                );\n\n                door_handler.position.setX(0);\n                door_handler.position.setZ(0);\n\n                doorBox = this.boundingBox.setFromObject(door_handler);\n                let overscale = 1;\n                door_handler.scale.setY(\n                    10\n                );\n                door_handler.scale.setZ(size.x * overscale / doorBox.size().x);\n                door_handler.scale.setX(size.z * overscale / doorBox.size().z);\n\n                door_handler.traverse(function (child) {\n                    if (child instanceof THREE.Mesh) {\n                        child.material = self.magical_materials_for_rows[\"handlers\"][0];\n                        child.material.needsUpdate = true;\n                        child.geometry.center();\n                    }\n                });\n                door_group.add(door_handler);\n            }\n\n            // Bottom\n\n            let bottom = this.elements[\"support-drawer\"].clone();\n\n            bottom.name = \"szufladaBottom\";\n\n            bottom.position.setX(0);\n            bottom.position.setY(\n                position.y - size.y / 2 + drawer_cutout - door_group.position.y\n            );\n            bottom.position.setZ(-bottom_depth / 2);\n            bottom.rotation.x = Math.PI / 360;\n\n            doorBox = this.boundingBox.setFromObject(bottom);\n            bottom.scale.setX((size.x - bottom_offset) / doorBox.size().x);\n            bottom.scale.setY(bottom_thickness / doorBox.size().y);\n            bottom.scale.setZ(bottom_depth / doorBox.size().z);\n\n            door_group.add(bottom);\n\n            // Back\n\n            let back = this.elements[\"support-drawer\"].clone();\n\n            back.name = \"szufladaBack\";\n\n            //back.rotation.y = back.rotation.y + 90 * Math.PI / 180;\n            back.rotation.x = -90 * Math.PI / 180;\n\n            back.position.setX(0);\n            //back.position.setY(((position.y - (front_handling_size / 2)) + drawer_cutout) - door_group.position.y);\n            back.position.setY(\n                position.y -\n                size.y / 2 +\n                drawer_cutout -\n                door_group.position.y +\n                back_height / 2\n            );\n            back.position.setZ(-bottom_depth);\n\n            doorBox = this.boundingBox.setFromObject(back);\n            back.scale.setX(\n                (size.x - bottom_offset + drawer_cutout - offset * 6) / doorBox.size().x\n            ); // was xyz before rotation\n            back.scale.setZ(\n                back_height /\n                doorBox.size().y\n            );\n            //back.scale.setX(back_thickness / doorBox.size().z);\n\n            door_group.add(back);\n\n            // side left\n\n            let sideA = this.elements[\"support-drawer\"].clone();\n\n            sideA.name = \"szufladaSideA\";\n\n            //back.rotation.y =\n            sideA.rotation.z = -90 * Math.PI / 180;\n            sideA.rotation.x = Math.PI;\n            sideA.position.setX(-(size.x / 2) + offset * 5);\n            sideA.position.setY(\n                position.y -\n                size.y / 2 +\n                drawer_cutout -\n                door_group.position.y +\n                sides_height /\n                2\n            );\n            sideA.position.setZ(-bottom_depth / 2);\n\n            doorBox = this.boundingBox.setFromObject(sideA);\n            sideA.scale.setX(\n                sides_height /\n                doorBox.size().y\n            );\n            sideA.scale.setZ(sides_length / doorBox.size().z);\n\n            door_group.add(sideA);\n\n            // side right\n\n            let sideB = this.elements[\"support-drawer\"].clone();\n\n            sideB.name = \"szufladaSideB\";\n\n            sideB.rotation.z = 90 * Math.PI / 180;\n            sideB.rotation.x = Math.PI;\n            sideB.position.setX(size.x / 2 - offset * 5);\n            sideB.position.setY(\n                position.y -\n                size.y / 2 +\n                drawer_cutout -\n                door_group.position.y +\n                sides_height /\n                2\n            );\n            sideB.position.setZ(-bottom_depth / 2);\n\n            doorBox = this.boundingBox.setFromObject(sideB);\n\n            //sideB.scale.setZ(sides_thickness / doorBox.size().x);\n            sideB.scale.setX(\n                sides_height /\n                doorBox.size().y\n            );\n            sideB.scale.setZ(sides_length / doorBox.size().z);\n\n            door_group.add(sideB);\n\n            this.walls.drawers.push(door_group);\n            door_group.position.setY(position.y + size.y / 2);\n\n            //animation here\n            //this.drawers_lists[row_number].push(door_group);\n            this.scene.add(door_group);\n            scene.add(door_group);\n            scene.updateMatrix();\n        } else if (shelf_type === 0) {\n            let door_group = new THREE.Group();\n            let scene = this.scene;\n            door_group.name = \"drawer group\";\n            let offset = 2;\n            let drawer_cutout = 13;\n            let front_handling_size = 20;\n            let front_offset_from_top = 4;\n            let front_height_offset = 37;\n            let front_thickness = 18;\n\n            let bottom_offset = 14;\n            let bottom_thickness = 13;\n            let bottom_depth = this.depth == 320 ? 245 : 325;\n\n            let back_width_offset = 14;\n            let back_thickness = 13;\n            let back_height_a = 120;\n            let back_height_b = 190;\n            let back_height_c = 380 - 70;\n\n            let sides_length = this.depth == 320 ? 245 : 325;\n            let sides_thickness = 13;\n            let sides_height_a = 120;\n            let sides_height_b = 190;\n            let sides_height_c = 380 - 70;\n\n            // whole group\n            door_group.position.setX(position.x);\n            door_group.position.setY(position.y);\n            door_group.position.setZ(position.z + 0);\n\n            let row_number = getRowByY(position.y, rowHeights);\n\n            let row_height = rowHeights[row_number];\n\n            // front\n            let door = this.elements[\"drawer_front\"].clone();\n            let self = this;\n            door.traverse(function (child) {\n                if (child instanceof THREE.Mesh) {\n                    /*child.material = self.magical_materials_for_rows['doors'][row_number];*/\n\n                }\n            });\n            door.name = \"szufladaFront\";\n            //console.log('color szuflady', ivy.material);\n\n            door.position.setX(0);\n            door.position.setY(\n                position.y - front_handling_size / 2 - door_group.position.y\n            );\n            door.position.setZ(0);\n\n            let doorBox = this.boundingBox.setFromObject(this.elements[\"drawer_front\"]);\n\n            door.scale.setX((size.x + offset * 2) / doorBox.size().x);\n            door.scale.setY((size.y - front_handling_size - offset) / doorBox.size().y);\n            door.scale.setZ(size.z / doorBox.size().z);\n\n            door_group.add(door);\n\n            // handler\n            let door_handler = this.elements[\"handle_drawer\"].clone();\n            door_handler.name = \"Trzymadelko\";\n\n            door_handler.rotation.z = door_handler.rotation.z - 90 * Math.PI / 180;\n\n            door_handler.rotation.y = door_handler.rotation.y + 90 * Math.PI / 180;\n\n            //door_handler.position.setX(position.x+(handling_flip*((size.x/2)-(handling_size))) + (handling_flip *offset) - door_group.position.x);\n            door_handler.position.setY(\n                position.y +\n                (size.y / 2 - front_handling_size) +\n                offset -\n                door_group.position.y + 3\n            );\n\n            door_handler.position.setX(0);\n            door_handler.position.setZ(0);\n\n            doorBox = this.boundingBox.setFromObject(door_handler);\n            let overscale = 1;\n            door_handler.scale.setY(\n                10\n            );\n            door_handler.scale.setZ(size.x * overscale / doorBox.size().x);\n            door_handler.scale.setX(size.z * overscale / doorBox.size().z);\n\n            door_handler.traverse(function (child) {\n                if (child instanceof THREE.Mesh) {\n                    child.material =\n                        self.magical_materials_for_rows[\"handlers\"][row_number];\n                    child.material.needsUpdate = true;\n                    child.geometry.center();\n                }\n            });\n            door_group.add(door_handler);\n\n            // Bottom\n\n            let bottom = this.elements[\"support-drawer\"].clone();\n\n            bottom.name = \"szufladaBottom\";\n\n            bottom.position.setX(0);\n            bottom.position.setY(\n                position.y - size.y / 2 + drawer_cutout - door_group.position.y\n            );\n            bottom.position.setZ(-bottom_depth / 2);\n            bottom.rotation.x = Math.PI / 360;\n\n            doorBox = this.boundingBox.setFromObject(bottom);\n            bottom.scale.setX((size.x - bottom_offset) / doorBox.size().x);\n            bottom.scale.setY(bottom_thickness / doorBox.size().y);\n            bottom.scale.setZ(bottom_depth / doorBox.size().z);\n\n            door_group.add(bottom);\n\n            // Back\n\n            let back = this.elements[\"support-drawer\"].clone();\n\n            back.name = \"szufladaBack\";\n            back.renderOrder = 10;\n            //back.rotation.y = back.rotation.y + 90 * Math.PI / 180;\n            back.rotation.x = -90 * Math.PI / 180;\n\n            back.position.setX(0);\n            //back.position.setY(((position.y - (front_handling_size / 2)) + drawer_cutout) - door_group.position.y);\n            back.position.setY(\n                position.y -\n                size.y / 2 +\n                drawer_cutout -\n                door_group.position.y +\n                (row_height == this.row_a\n                    ? sides_height_a\n                    : row_height == this.row_b ? back_height_b : back_height_c) /\n                2\n            );\n            back.position.setZ(-bottom_depth);\n\n            doorBox = this.boundingBox.setFromObject(back);\n            back.scale.setX(\n                (size.x - bottom_offset + drawer_cutout) / doorBox.size().x\n            ); // was xyz before rotation\n            back.scale.setZ(\n                (row_height == this.row_a\n                    ? back_height_a\n                    : row_height == this.row_b ? back_height_b : back_height_c) /\n                doorBox.size().y\n            );\n            //back.scale.setX(back_thickness / doorBox.size().z);\n\n            door_group.add(back);\n\n            // side left\n\n            let sideA = this.elements[\"support-drawer\"].clone();\n\n            sideA.name = \"szufladaSideA\";\n\n            //back.rotation.y =\n            sideA.rotation.z = -90 * Math.PI / 180;\n            sideA.rotation.x = Math.PI;\n            sideA.position.setX(-(size.x / 2) + offset * 4);\n            sideA.position.setY(\n                position.y -\n                size.y / 2 +\n                drawer_cutout -\n                door_group.position.y +\n                (row_height == this.row_a\n                    ? sides_height_a\n                    : row_height == this.row_b ? sides_height_b : sides_height_c) /\n                2\n            );\n            sideA.position.setZ(-bottom_depth / 2);\n\n            doorBox = this.boundingBox.setFromObject(sideA);\n            sideA.scale.setX(\n                (row_height == this.row_a\n                    ? sides_height_a\n                    : row_height == this.row_b ? sides_height_b : sides_height_c) /\n                doorBox.size().y\n            );\n            sideA.scale.setZ(sides_length / doorBox.size().z);\n\n            door_group.add(sideA);\n\n            // side right\n\n            let sideB = this.elements[\"support-drawer\"].clone();\n\n            sideB.name = \"szufladaSideB\";\n\n            sideB.rotation.z = 90 * Math.PI / 180;\n            sideB.rotation.x = Math.PI;\n            sideB.position.setX(size.x / 2 - offset * 4);\n            sideB.position.setY(\n                position.y -\n                size.y / 2 +\n                drawer_cutout -\n                door_group.position.y +\n                (row_height == this.row_a\n                    ? sides_height_a\n                    : row_height == this.row_b ? sides_height_b : sides_height_c) /\n                2\n            );\n            sideB.position.setZ(-bottom_depth / 2);\n\n            doorBox = this.boundingBox.setFromObject(sideB);\n\n            //sideB.scale.setZ(sides_thickness / doorBox.size().x);\n            sideB.scale.setX(\n                (row_height == this.row_a\n                    ? sides_height_a\n                    : row_height == this.row_b ? sides_height_b : sides_height_c) /\n                doorBox.size().y\n            );\n            sideB.scale.setZ(sides_length / doorBox.size().z);\n\n            door_group.add(sideB);\n\n            this.walls.drawers.push(door_group);\n            door_group.position.setY(position.y + size.y / 2);\n\n            //animation here\n            this.drawers_lists[row_number].push(door_group);\n            this.scene.add(door_group);\n            scene.add(door_group);\n            //console.log(this.drawers_lists);\n        } else {\n            let door_group = new THREE.Group();\n            let scene = this.scene;\n            let door_handler_width = 130;\n            let door_handler_height = 20;\n            let door_handler_depth = 30;\n            let handling_overlap = 20;\n\n            door_group.name = \"drawer group\";\n            let offset = 2;\n            let drawer_cutout = 13;\n            let front_handling_size = 20;\n            let front_offset_from_top = 4;\n            let front_height_offset = 37;\n            let front_thickness = 18;\n\n            let bottom_offset = 14;\n            let bottom_thickness = 13;\n            let bottom_depth = this.depth == 320 ? 245 : 325;\n\n            let back_width_offset = 14;\n            let back_thickness = 13;\n            let back_height_a = 120;\n            let back_height_b = 190;\n            let back_height_c = 380 - 70;\n\n            let sides_length = this.depth == 320 ? 245 : 325;\n            let sides_thickness = 13;\n            let sides_height_a = 120;\n            let sides_height_b = 190;\n            let sides_height_c = 380 - 70;\n\n            // whole group\n            door_group.position.setX(position.x);\n            door_group.position.setY(position.y);\n            door_group.position.setZ(position.z + 0);\n\n            let row_number = getRowByY(position.y, rowHeights);\n\n            let row_height = rowHeights[row_number];\n\n            // front\n            let door = this.elements[\"door\"].clone();\n            let self = this;\n            door.traverse(function (child) {\n                if (child instanceof THREE.Mesh) {\n                    /*child.material = self.magical_materials_for_rows['doors'][row_number];*/\n\n                }\n            });\n            door.name = \"szufladaFront\";\n            //console.log('color szuflady', ivy.material);\n\n            door.position.setX(0);\n            door.position.setY(\n                position.y - door_group.position.y\n            );\n            door.position.setZ(0);\n\n            let doorBox = this.boundingBox.setFromObject(this.elements[\"drawer_front\"]);\n\n            door.scale.setX((size.x - offset) / doorBox.size().x);\n            door.scale.setY((size.y - offset) / doorBox.size().y);\n            door.scale.setZ(size.z / doorBox.size().z);\n\n            door_group.add(door);\n\n            // handler\n\n            let drawer_handler = this.elements[\"handle_short_left\"].clone();\n            drawer_handler.name = \"Handler\";\n            // console.log('posy', size.y);\n            drawer_handler.rotation.x = -Math.PI / 2;\n            drawer_handler.position.setX(position.x - size.x / 2 - door_group.position.x + door_handler_width / 2 + offset);\n            drawer_handler.position.setY(size.y / 2 - door_handler_height / 2);\n            drawer_handler.position.setZ(size.z - plankScaleMM / 2 - 1);\n\n            doorBox = this.boundingBox.setFromObject(drawer_handler);\n            drawer_handler.scale.setY(1);\n            drawer_handler.scale.setX(1);\n            drawer_handler.scale.setZ(1);\n\n            drawer_handler.traverse(function (child) {\n                if (\n                    child instanceof THREE.Mesh &&\n                    typeof child.material !== \"undefined\"\n                ) {\n                    child.material.needsUpdate = true;\n                    child.geometry.center();\n                }\n            });\n            door_group.add(drawer_handler);\n\n            let drawer_handler_shadow = this.elements[\"handle_short_left_shadow\"].clone();\n            drawer_handler_shadow.name = \"Handler shadow\";\n\n            drawer_handler_shadow.rotation.x = -Math.PI / 2;\n            drawer_handler_shadow.position.setX(position.x - size.x / 2 - door_group.position.x + door_handler_width / 2 + offset * 2);\n            drawer_handler_shadow.position.setY(size.y / 2 - door_handler_height / 2);\n            drawer_handler_shadow.position.setZ(size.z + plankScaleMM);\n\n            doorBox = this.boundingBox.setFromObject(drawer_handler_shadow);\n            drawer_handler_shadow.scale.setY(1);\n            drawer_handler_shadow.scale.setX(1);\n            drawer_handler_shadow.scale.setZ(1);\n\n            door_group.add(drawer_handler_shadow);\n\n            // Bottom\n\n            let bottom = this.elements[\"support-drawer\"].clone();\n\n            bottom.name = \"szufladaBottom\";\n\n            bottom.position.setX(0);\n            bottom.position.setY(\n                position.y - size.y / 2 + drawer_cutout - door_group.position.y\n            );\n            bottom.position.setZ(-bottom_depth / 2);\n            bottom.rotation.x = Math.PI / 360;\n\n            doorBox = this.boundingBox.setFromObject(bottom);\n            bottom.scale.setX((size.x - bottom_offset) / doorBox.size().x);\n            bottom.scale.setY(bottom_thickness / doorBox.size().y);\n            bottom.scale.setZ(bottom_depth / doorBox.size().z);\n\n            door_group.add(bottom);\n\n            // Back\n\n            let back = this.elements[\"support-drawer\"].clone();\n\n            back.name = \"szufladaBack\";\n\n            //back.rotation.y = back.rotation.y + 90 * Math.PI / 180;\n            back.rotation.x = -90 * Math.PI / 180;\n\n            back.position.setX(0);\n            //back.position.setY(((position.y - (front_handling_size / 2)) + drawer_cutout) - door_group.position.y);\n            back.position.setY(\n                position.y -\n                size.y / 2 +\n                drawer_cutout -\n                door_group.position.y +\n                (row_height == this.row_a\n                    ? sides_height_a\n                    : row_height == this.row_b ? back_height_b : back_height_c) /\n                2\n            );\n            back.position.setZ(-bottom_depth);\n\n            doorBox = this.boundingBox.setFromObject(back);\n            back.scale.setX(\n                (size.x - bottom_offset + drawer_cutout - offset * 6) / doorBox.size().x\n            ); // was xyz before rotation\n            back.scale.setZ(\n                (row_height == this.row_a\n                    ? back_height_a\n                    : row_height == this.row_b ? back_height_b : back_height_c) /\n                doorBox.size().y\n            );\n            //back.scale.setX(back_thickness / doorBox.size().z);\n\n            door_group.add(back);\n\n            // side left\n\n            let sideA = this.elements[\"support-drawer\"].clone();\n\n            sideA.name = \"szufladaSideA\";\n\n            //back.rotation.y =\n            sideA.rotation.z = -90 * Math.PI / 180;\n            sideA.rotation.x = Math.PI;\n            sideA.position.setX(-(size.x / 2) + offset * 5);\n            sideA.position.setY(\n                position.y -\n                size.y / 2 +\n                drawer_cutout -\n                door_group.position.y +\n                (row_height == this.row_a\n                    ? sides_height_a\n                    : row_height == this.row_b ? sides_height_b : sides_height_c) /\n                2\n            );\n            sideA.position.setZ(-bottom_depth / 2);\n\n            doorBox = this.boundingBox.setFromObject(sideA);\n            sideA.scale.setX(\n                (row_height == this.row_a\n                    ? sides_height_a\n                    : row_height == this.row_b ? sides_height_b : sides_height_c) /\n                doorBox.size().y\n            );\n            sideA.scale.setZ(sides_length / doorBox.size().z);\n\n            door_group.add(sideA);\n\n            // side right\n\n            let sideB = this.elements[\"support-drawer\"].clone();\n\n            sideB.name = \"szufladaSideB\";\n\n            sideB.rotation.z = 90 * Math.PI / 180;\n            sideB.rotation.x = Math.PI;\n            sideB.position.setX(size.x / 2 - offset * 5);\n            sideB.position.setY(\n                position.y -\n                size.y / 2 +\n                drawer_cutout -\n                door_group.position.y +\n                (row_height == this.row_a\n                    ? sides_height_a\n                    : row_height == this.row_b ? sides_height_b : sides_height_c) /\n                2\n            );\n            sideB.position.setZ(-bottom_depth / 2);\n\n            doorBox = this.boundingBox.setFromObject(sideB);\n\n            //sideB.scale.setZ(sides_thickness / doorBox.size().x);\n            sideB.scale.setX(\n                (row_height == this.row_a\n                    ? sides_height_a\n                    : row_height == this.row_b ? sides_height_b : sides_height_c) /\n                doorBox.size().y\n            );\n            sideB.scale.setZ(sides_length / doorBox.size().z);\n\n            door_group.add(sideB);\n\n            this.walls.drawers.push(door_group);\n            door_group.position.setY(position.y + size.y / 2);\n\n            //animation here\n            this.drawers_lists[row_number].push(door_group);\n            this.scene.add(door_group);\n            scene.add(door_group);\n            scene.updateMatrix();\n        }\n    }\n\n    createVerticalPanels(index, oldPoints, newPoints, pattern) {\n        let dif;\n        let scene = this.scene;\n\n        if (typeof oldPoints !== \"undefined\") {\n            dif = this.compare_dnas(oldPoints, newPoints);\n        } else {\n            dif = {\n                newitem: true,\n                scaled: true,\n                only_moved: true\n            };\n        }\n        if (this.depth != this.depth_previous) {\n            dif.scaled = true;\n            dif.same = false;\n        }\n        if (dif.same === true) {\n            return true;\n        }\n\n        let wall;\n\n        if (dif.newitem) {\n            wall = this.elements[\"left-right\"].clone();\n        } else {\n            wall = this.walls.leftRightWalls[index];\n        }\n\n        let distance = Math.abs(newPoints.bottom.y - newPoints.top.y);\n        let scale = (distance + 3) / 100;\n        // LEFT/RIGHT - half of horizontal edge width\n        let factor = 0;\n\n        if (newPoints.bottom.x < 0) {\n            //Left panel\n            factor = -7.5;\n        } else {\n            // Right panel\n            factor = 7.5;\n        }\n        // chowanie dla slanta scianki\n        if (pattern == 0) {\n            factor = -factor;\n        }\n\n        if (dif.scaled) {\n\n            let leftRightWall = this.boundingBox.setFromObject(this.elements[\"left-right\"]);\n            wall.rotation.x = -Math.PI / 2;\n            wall.scale.setY(this.depth / 100);\n            wall.scale.setZ(scale);\n\n\n            wall.position.setY(newPoints.bottom.y - 1.5);\n            wall.position.setX(newPoints.bottom.x + factor);\n        }\n        //\n        if (dif.only_moved) {\n            wall.position.setY(newPoints.bottom.y - 1.5);\n            wall.position.setX(newPoints.bottom.x + factor);\n            wall.scale.setY(this.depth / 100);\n            wall.scale.setZ(scale);\n        }\n        if (dif.newitem) {\n            wall.name = \"Left-right-wall\";\n            this.scene.add(wall);\n            this.walls.leftRightWalls.push(wall);\n        }\n\n    }\n\n\n    createAdditionalHorizontalPanels(newPoints) {\n        let dif;\n        let scene = this.scene;\n\n\n        let panel = this.elements[\"horizontal-plug\"].clone();\n        panel.rotation.x = -Math.PI / 2;\n\n        panel.position.setY(newPoints.y);\n        panel.position.setX(newPoints.x);\n        //panel.position.setZ();\n\n        let panelBox = this.boundingBox.setFromObject(this.elements[\"horizontal-plug\"]);\n\n        panel.scale.setY(this.depth / panelBox.size().y);\n\n\n        panel.name = \"additionalHorizontalPanel\";\n        this.scene.add(panel);\n        this.walls.additionalHorizontalElements.push(panel);\n    }\n\n    createHorizontalPanels(index, oldPoints, newPoints) {\n        let dif;\n        let scene = this.scene;\n        if (typeof oldPoints !== \"undefined\") {\n            dif = this.compare_dnas(oldPoints, newPoints);\n        } else {\n            dif = {\n                newitem: true,\n                scaled: true,\n                only_moved: true\n            };\n        }\n        if (this.depth != this.depth_previous) {\n            dif.scaled = true;\n            dif.same = false;\n        }\n        if (dif.same === true) {\n            return true;\n        }\n\n        let wall;\n\n        let distance = Math.abs(newPoints.bottom.x - newPoints.top.x);\n\n        if (dif.newitem) {\n            wall = this.elements[\"top-bottom\"].clone();\n        } else {\n            wall = this.walls.topBottomWalls[index];\n        }\n        //\n        //let distance = Math.abs(newPoints.bottom.x - newPoints.top.x);\n        if (dif.scaled) {\n            let topBottomWall = this.boundingBox.setFromObject(this.elements[\"top-bottom\"]);\n            let scale = distance / topBottomWall.size().x;\n            wall.scale.setX(scale);\n            wall.rotation.x = -Math.PI / 2;\n\n            if (dif.newitem) {\n                wall.scale.setZ(1);\n            }\n            wall.scale.setY(this.depth / topBottomWall.size().y);\n\n            wall.position.y = index ? (newPoints.bottom.y + 7.5) : (newPoints.bottom.y) - 7.5;\n            wall.position.setX(\n                Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2\n            );\n        }\n        //\n        if (dif.only_moved) {\n            //wall.position.setY(newPoints.bottom.y);\n            wall.position.y = index ? (newPoints.bottom.y + 7.5) : (newPoints.bottom.y) - 7.5;\n            wall.position.setX(\n                Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2\n            );\n        }\n        //\n        if (dif.newitem) {\n            wall.name = \"wall\";\n            this.scene.add(wall);\n            this.walls.topBottomWalls.push(wall);\n        }\n    }\n\n  createDoor(position, size, door_type, door_flip, rowHeights, shelf_type, selected, innerOffset) {\n    // if (this.designer_mode > 0) {\n    //     shelf_type = 1;\n    // }\n\n    if (shelf_type === 0) {\n      let door_group = new THREE.Group();\n      door_group.name = \"door group\";\n\n      let handling_overlap = 20;\n      //let handling_overlap = 20;\n      let handling_size = door_type === 0 ? 30 : 10;\n      let handling_flip = door_flip === 1 ? 1 : -1;\n      let offset = innerOffset;\n      let item_to_use =\n          door_type === 0 ? this.elements[\"handle_big\"] : this.elements[\"handle_small\"];\n\n      door_group.position.setX(position.x - handling_flip * (size.x / 2));\n      door_group.position.setY(position.y + size.y / 2);\n      door_group.position.setZ(position.z);\n\n      let door = this.elements[\"door\"].clone();\n      let row_number = getRowByY(position.y, rowHeights);\n      let self = this;\n      door.name = \"Drzwi\";\n\n      door.position.setX(\n          position.x - handling_flip * handling_size / 2 - door_group.position.x\n      );\n      door.position.setY(0);\n      door.position.setZ(0);\n\n      let doorBox = this.boundingBox.setFromObject(this.elements[\"door\"]);\n\n      door.scale.setY((size.y - offset) / doorBox.size().y);\n      door.scale.setX((size.x - handling_size - offset) / doorBox.size().x);\n      door.scale.setZ(size.z / doorBox.size().z);\n\n      if (this.designer_mode != 2) {\n        door_group.add(door);\n      }\n      this.walls.doors.push(door);\n\n      let door_handler = item_to_use.clone();\n      door_handler.name = \"Trzymadelko\";\n\n      door_handler.position.setX(\n          position.x +\n          handling_flip * (size.x / 2 - handling_size) +\n          handling_flip * offset -\n          door_group.position.x\n      );\n      if (handling_flip === 1) {\n          door_handler.position.x -= offset * 2;\n      }\n\n      door_handler.position.setY(0);\n      door_handler.position.setZ(0);\n      doorBox = this.boundingBox.setFromObject(item_to_use);\n      door_handler.scale.setY((size.y - offset) / doorBox.size().y);\n      door_handler.scale.setX(\n          (handling_size + handling_overlap) / doorBox.size().x\n      );\n      door_handler.scale.setZ(1);\n\n      door_handler.traverse(function (child) {\n          if (\n              child instanceof THREE.Mesh &&\n              typeof child.material !== \"undefined\"\n          ) {\n              child.material =\n                  self.magical_materials_for_rows[\"handlers\"][row_number];\n              child.material.needsUpdate = true;\n              if (handling_flip > 0) {\n                  child.rotation.y = child.rotation.y + 180 * Math.PI / 180;\n              }\n              child.geometry.center();\n          }\n      });\n        if (this.designer_mode != 2) {\n             door_group.add(door_handler);\n        }\n      this.walls.doors.push(door_handler);\n\n      //animation here\n      door_group.rotate_sign = handling_flip;\n\n      // webdesinger\n      if (selected || this.designer_mode == 3) {\n          door_group.rotation.y = -MAX_DOOR_OPEN * door_group.rotate_sign * Math.PI / 180;\n      }\n      this.door_lists[row_number].push(door_group);\n      this.walls.door_groups.push(door_group);\n      this.scene.add(door_group);\n    } else {\n      //  TYPE-02\n      let door_group = new THREE.Group();\n      door_group.name = \"door group\";\n      let door_handler_width = 130;\n      let door_handler_height = 20;\n      let door_handler_depth = 30;\n      let handling_overlap = 20;\n      //let handling_overlap = 20;\n      let handling_size = 0;\n      let handling_flip = door_flip === 1 ? 1 : -1;\n      let offset = innerOffset;\n\n\n      door_group.position.setX(position.x - handling_flip * (size.x / 2));\n      door_group.position.setY(position.y + size.y / 2);\n      door_group.position.setZ(position.z);\n\n      let door = this.elements[\"door\"].clone();\n      let row_number = getRowByY(position.y, rowHeights);\n      let self = this;\n      door.name = \"Drzwi\";\n\n      door.position.setX(\n          position.x - handling_flip - door_group.position.x\n      );\n      door.position.setY(0);\n      door.position.setZ(0);\n\n      let doorBox = this.boundingBox.setFromObject(this.elements[\"door\"]);\n\n      door.scale.setX((size.x - offset) / doorBox.size().x);\n      door.scale.setY((size.y - offset) / doorBox.size().y);\n      door.scale.setZ(size.z / doorBox.size().z);\n\n      if (this.designer_mode != 2) {\n        door_group.add(door);\n      }\n      this.walls.doors.push(door);\n      if(handling_flip === -1) {\n          let door_handler = this.elements[\"handle_short_left\"].clone();\n          door_handler.name = \"Handler\";\n          // console.log('posy', size.y);\n          door_handler.rotation.x = -Math.PI / 2;\n          door_handler.position.setX(position.x - size.x / 2 - door_group.position.x + door_handler_width / 2 + offset);\n          door_handler.position.setY(size.y / 2 - door_handler_height / 2);\n          door_handler.position.setZ(size.z - plankScaleMM / 2 - 1);\n\n          doorBox = this.boundingBox.setFromObject(door_handler);\n          door_handler.scale.setY(1);\n          door_handler.scale.setX(1);\n          door_handler.scale.setZ(1);\n\n          door_handler.traverse(function (child) {\n              if (\n                  child instanceof THREE.Mesh &&\n                  typeof child.material !== \"undefined\"\n              ) {\n                  child.material.needsUpdate = true;\n                  child.geometry.center();\n              }\n          });\n          if (this.designer_mode != 2) {\n          door_group.add(door_handler);\n          }\n          this.walls.doors.push(door_handler);\n\n          let door_handler_shadow = this.elements[\"handle_short_left_shadow\"].clone();\n          door_handler_shadow.name = \"Handler shadow\";\n\n          door_handler_shadow.rotation.x = -Math.PI / 2;\n          door_handler_shadow.position.setX(position.x - size.x / 2 - door_group.position.x + door_handler_width / 2 + offset * 2);\n          door_handler_shadow.position.setY(size.y / 2 - door_handler_height / 2);\n          door_handler_shadow.position.setZ(size.z + 2 + 5);\n\n          doorBox = this.boundingBox.setFromObject(door_handler_shadow);\n          door_handler_shadow.scale.setY(1);\n          door_handler_shadow.scale.setX(1);\n          door_handler_shadow.scale.setZ(1);\n\n          door_handler_shadow.traverse(function (child) {\n              if (\n                  child instanceof THREE.Mesh &&\n                  typeof child.material !== \"undefined\"\n              ) {\n                  child.material.needsUpdate = true;\n                  child.geometry.center();\n              }\n          });\n          door_group.add(door_handler_shadow);\n          this.walls.doors.push(door_handler_shadow);\n      }\n\n      //animation here\n      door_group.rotate_sign = handling_flip;\n\n     if (selected || this.designer_mode == 3) {\n          door_group.rotation.y = -MAX_DOOR_OPEN * door_group.rotate_sign * Math.PI / 180;\n      }\n\n      this.door_lists[row_number].push(door_group);\n      this.walls.door_groups.push(door_group);\n      this.scene.add(door_group);\n    }\n  }\n    createButton(newPoints, box_description) {\n        let geometry = new THREE.BoxGeometry( 50, 50, 2 );\n        let material = new THREE.MeshBasicMaterial( {color: (newPoints.raw && newPoints.raw.selected ? 0xff3c00 : 0xCAD0D0)});\n        let sphere = new THREE.Mesh( geometry, material );\n\n        sphere.position.set(\n            (newPoints.bottom.x + newPoints.top.x)/2,\n            (newPoints.bottom.y + newPoints.top.y)/2,\n            (newPoints.bottom.z + newPoints.top.z)/2\n        );\n        sphere.name = \"buttons\";\n\n        sphere.visible = false;\n\n        this.scene.add(sphere);\n        if (this.walls[\"boxes\"] === undefined) {\n            this.walls[\"boxes\"] = [];\n        }\n        this.walls[\"boxes\"].push(sphere);\n    }\n\n    createFacePlaneForRaycasting() {\n\n        let geometry = new THREE.BoxGeometry( 2000, 1000, 1 );\n        let material = new THREE.MeshBasicMaterial();\n        material.color = new THREE.Color(0xff0000);\n        material.opacity = 0;\n        material.transparent = true;\n        let box = new THREE.Mesh( geometry, material );\n\n        if(!this.scene3) {\n            this.scene3 = new THREE.Scene();\n        }\n        this.facePlane = box;\n        this.scene3.add(box);\n    }\n\n    createComponentHoverBox(points) {\n\n        let width = points.x1 < points.x2 ? points.x2 - points.x1 : points.x1- points.x2;\n        let height = points.y2 * 2;\n        let depth = points.z1 + 20;\n\n        let geometry = new THREE.BoxGeometry( width, height, depth );\n        let material = new THREE.MeshBasicMaterial();\n        material.color = new THREE.Color(0xff0000);\n        material.opacity = 0;\n        material.transparent = true;\n\n        let box = new THREE.Mesh( geometry, material );\n\n        //box.visible = false;\n        \n        box.position.set(\n           points.x2-width/2, 0, points.z1/2\n        );\n\n        if(!this.scene2) {\n            this.scene2 = new THREE.Scene();\n        }\n        this.scene2.add(box);\n\n        box.no = this.componentHoverBoxes.length;\n        this.componentHoverBoxes.push(box);\n    }\n\n\n    createInsertAndPlynth(newPoints, box_description) {\n        let support = this.elements[\"insert\"].clone();\n\n        let supportBox = this.boundingBox.setFromObject(this.elements[\"insert\"]);\n\n        let scaleX, scaleZ, scaleY;\n        support.rotation.x = -Math.PI / 2;\n\n        // support.rotation.y = -Math.PI / 2;\n        // if (true || supportType == 'left') {\n        //     support.rotation.x = -Math.PI / 2;\n        // } else {\n        //     support.rotation.x = Math.PI / 2;\n        // }\n\n        scaleZ =\n            Math.abs(newPoints.bottom.y - newPoints.top.y) / supportBox.size().x;\n        //scaleX = this.constants.gradientSupportWidth / supportBox.size().x;\n        scaleX =\n            Math.abs(newPoints.bottom.x - newPoints.top.x) / supportBox.size().x;\n\n        scaleY =\n            Math.abs(newPoints.bottom.z - newPoints.top.z) / supportBox.size().x;\n\n        support.scale.setZ(scaleZ);\n        support.scale.setX(scaleX);\n        support.scale.setY(scaleY);\n\n        supportBox = this.boundingBox.setFromObject(support);\n\n        support.position.set(\n            Math.min(newPoints.bottom.x, newPoints.top.x) + supportBox.size().x / 2,\n            Math.min(newPoints.bottom.y, newPoints.top.y) + Math.abs(newPoints.bottom.y - newPoints.top.y) / 2,\n            Math.abs(newPoints.bottom.z + newPoints.top.z) / 2\n        );\n        support.name = \"boxes\";\n        this.scene.add(support);\n        if (this.walls[\"boxes\"] === undefined) {\n            this.walls[\"boxes\"] = [];\n        }\n        this.walls[\"boxes\"].push(support);\n    }\n}\n\nfunction getRowByY(value_y, rowsList) {\n    let tmp = 0;\n    let row = 0;\n    while (!(tmp < value_y && tmp + rowsList[row] > value_y)) {\n        tmp += rowsList[row];\n        row++;\n        // security check\n        if (row >= 15) {\n            return -1;\n        }\n    }\n    return row;\n}\n\n", "import { getDefinitionForMaterial } from './presets/bundle/presets.js';\n\nimport { preparePoints } from './decoder_old.js';\n\nimport { BuildFunctions } from './build3d/buildFunctions'\n\nTHREE = THREE || {};\n\n\nlet VERTEX_SHADER = `        varying vec2 vUv;\n\n        void main()\n        {\n            vUv = uv;\n\n            vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );\n            gl_Position = projectionMatrix * mvPosition;\n        }`;\n\nconst FRAGMENT_SHADER =  `uniform sampler2D foreground;\n    uniform sampler2D bg;\n    uniform vec3 blending_color;\n    uniform vec3 color;\n    uniform float blending_ratio;\n    varying vec2 vUv;\n\n    float blendOverlay(float base, float blend) {\n        return base<0.5?(2.0*base*blend):(1.0-2.0*(1.0-base)*(1.0-blend));\n    }\n\n    vec3 blendOverlay(vec3 base, vec3 blend) {\n        return vec3(blendOverlay(base.r,blend.r),blendOverlay(base.g,blend.g),blendOverlay(base.b,blend.b));\n    }\n\n    vec3 blendOverlay(vec3 base, vec3 blend, float opacity) {\n        return (blendOverlay(base, blend) * opacity + base * (1.0 - opacity));\n    }\n\n    vec3 blendSoftLight(vec3 base, vec3 blend) {\n    return mix(\n        sqrt(base) * (2.0 * blend - 1.0) + 2.0 * base * (1.0 - blend),\n        2.0 * base * blend + base * base * (1.0 - 2.0 * blend),\n        step(base, vec3(0.5))\n        );\n    }\n\n    void main() {\n      vec4 bgColor = texture2D(bg, vUv);\n      vec4 fgColor = texture2D(foreground, vUv);\n\n      //vec3 color = blendSoftLight(blending_color,mix(bgColor.rgb, fgColor.rgb, blending_ratio));\n      //vec3 color = blendOverlay(blending_color,mix(bgColor.rgb, fgColor.rgb, blending_ratio),0.8);\n        vec3 color = mix(bgColor.rgb, fgColor.rgb, blending_ratio) * blending_color;\n      //vec3 color = mix(bgColor.rgb, fgColor.rgb, blending_ratio);\n\n      gl_FragColor = vec4(color, 1.0);\n    }`;\n\nvar elements = null;\nlet boundingBox = new THREE.Box3();\nlet plankScale = 0.18;\nlet plankScaleMM = 18;\nlet shadowCastEnabled = true;\nlet dnaStartWasSent = false;\n\nlet sizePlane = [];\n\nlet _points;\nlet reflectionCube;\n\nlet color;\nlet opacity;\nlet hex_color;\nlet hex_handler_color;\nlet backs_color;\nlet reflectivityValue;\nlet reflectivityValueDoors;\nlet material_id;\n\nlet actualMaterial = 0;\nlet actualType = 0;\n\nif (window.cstm && window.cstm.item) {\n  actualMaterial = window.cstm.item.material\n}\nif (window.cstm && window.cstm.item) {\n  actualType = window.cstm.item.shelf_type;\n}\n\n[\n  material_id,\n  color,\n  opacity,\n  hex_color,\n  hex_handler_color,\n  backs_color,\n  reflectivityValue,\n  reflectivityValueDoors\n  ] = getDefinitionForMaterial (actualMaterial, actualType);\n\n// here it should use build functions - later\nclass Build3d extends BuildFunctions{\n\n  constructor (\n    elements_in,\n    dnaTools,\n    sendCustomization,\n    context_in,\n    isMobile\n  ) {\n    super();\n    this.context = context_in;\n    this.designer_mode = 0; // 0 -disabled, 1 - render everything, 2 - do not render fronts, 3 - drawers and doors open\n\n    this.send_customization = sendCustomization;\n    this.elements = elements_in;\n    elements = this.elements;\n\n    this.dnaTools = dnaTools;\n    this.boundingBox = boundingBox;\n    this.scene = null;\n    this.isMobile = isMobile;\n    this.backpanel_rows = null;\n\n    this.wallShadowsObjects = [];\n\n    this.row_a = 200;\n    this.row_b = 300;\n    this.row_c = 400;\n    this.shelf_type = actualType;\n\n    this.componentHoverBoxes = [];\n\n    this.magical_materials_for_rows = {\n      'doors': [],\n      'handlers': [],\n      'shadows': [],\n      'backs': []\n    };\n    this.walls = {\n      verticals: [],\n      horizontals: [],\n      supports: [],\n      shadows: [[], [], [], [], []],\n      castShadows: [],\n      legs: [],\n      doors: [],\n      door_groups: [],\n      backs: [],\n      boxes: [],\n      drawers: [],\n      topBottomWalls: [],\n      leftRightWalls: [],\n      additionalHorizontalElements: [],\n      wallCompartmentShadow: [],\n      plinth: []\n    };\n\n    this.points = {};\n\n    this.handlers = [];\n\n    this.depth = 320;\n    this.depth_previous = 320;\n    this.depth_changed = false;\n    this.width = 1200;\n\n    this.row_styles_presets = -1;\n    this.empty_row_styles = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1];\n\n\n    // temporary geometries\n    let boxGeometry = new THREE.BoxGeometry(1, 1, 1);\n    let boxMaterial = new THREE.MeshBasicMaterial({\n      color: 0xb6b6b7,\n      wireframe: false\n    });\n    elements[\"backs\"] = new THREE.Mesh(boxGeometry, boxMaterial);\n\n    elements[\"backs\"].renderOrder = 38;\n\n    // box POF\n    boxGeometry = new THREE.BoxGeometry(1, 1, 1);\n    boxMaterial = new THREE.MeshBasicMaterial({\n      color: 0x67717f,\n      wireframe: false\n    });\n    elements[\"storage_box\"] = new THREE.Mesh(boxGeometry, boxMaterial);\n\n    // spots POF\n    let spotGeometry = new THREE.BoxGeometry(1, 1, 1);\n    let spotMaterial = new THREE.MeshBasicMaterial({\n      color: 0x550088,\n      wireframe: false\n    });\n    elements[\"drawers\"] = new THREE.Mesh(spotGeometry, spotMaterial);\n\n    // door POF\n    /*\n         let doorGeometry = new THREE.BoxGeometry( 1, 1, 1);\n         let doorMaterial = new THREE.MeshBasicMaterial( { color: 0xf2f2f2, wireframe: false } );\n         elements['door'] = new THREE.Mesh( doorGeometry, doorMaterial );\n         */\n  }\n\n  init3d() {\n    let elements = this.elements;\n    window.elements = elements;\n      for (let i = 0; i < 12; i++) {\n        let material = new THREE.ShaderMaterial({\n          vertexShader: VERTEX_SHADER,\n          fragmentShader: FRAGMENT_SHADER,\n          uniforms: {\n            foreground: {\n              type: \"t\",\n              value: elements[\"doors_open\"]\n            },\n            bg: {\n              type: \"t\",\n              value: elements[\"doors_close\"]\n            },\n            blending_ratio: {\n              type: \"f\",\n              value: 1.0\n            },\n            blending_color: {\n              type: \"c\",\n              value: new THREE.Vector3(1.0, 1.0, 1.0)\n            }\n          }\n        });\n        this.magical_materials_for_rows[\"doors\"].push(material);\n\n        material = new THREE.ShaderMaterial({\n          vertexShader: VERTEX_SHADER,\n          fragmentShader: FRAGMENT_SHADER,\n          uniforms: {\n            foreground: {\n              type: \"t\",\n              value: elements[\"doors_open\"]\n            },\n            bg: {\n              type: \"t\",\n              value: elements[\"doors_close\"]\n            },\n            blending_ratio: {\n              type: \"f\",\n              value: 0.0\n            },\n            blending_color: {\n              type: \"c\",\n              value: new THREE.Vector3(1.0, 1.0, 1.0)\n            }\n          }\n        });\n        this.magical_materials_for_rows[\"handlers\"].push(material);\n      }\n      if (this.designer_mode == 0) {\n        this.scene = new THREE.Scene();\n      }\n    //window.scene = this.scene;\n    reflectionCube = elements['cubemap'];\n    let shadows = [\"shadow\", \"shadow-left\", \"shadow-right\"];\n    for (let i = 0; i < shadows.length; i++) {\n      elements[shadows[i]].traverse(function(child) {\n        if (child instanceof THREE.Mesh) {\n          //child.rotation.y = Math.PI / 2;\n          child.material.transparent = true;\n          child.material.opacity = 1;\n          child.material.side = THREE.FrontSide;\n          child.material.envMap =  reflectionCube;\n          //child.material.combine = THREE.AdditiveBlending;\n          child.material.reflectivity = reflectivityValue;\n        }\n      });\n      elements[shadows[i]].renderOrder = 0;\n    }\n\n    shadows = [\n      \"cast-shadow-right\",\n      \"cast-shadow-left\",\n      \"cast-shadow-center\",\n    ];\n    for (let i = 0; i < shadows.length; i++) {\n      elements[shadows[i]].traverse(function(child) {\n        if (child instanceof THREE.Mesh) {\n          child.material.transparent = true;\n          child.material.opacity = 0.5;\n\n        }\n      });\n      elements[shadows[i]].renderOrder = 15;\n    }\n    elements[\"door\"].traverse(function(child) {\n      if (child instanceof THREE.Mesh) {\n          if(window.cstm && window.cstm.item && (window.cstm.item.shelf_type === 1 && parseInt(window.cstm.item.material) === 0)) {\n              child.material.map = elements['doors_open_white'];\n          }\n          child.rotation.x = Math.PI / 2;\n          child.rotation.z = -Math.PI / 2;\n          child.geometry.center();\n          child.material.envMap = reflectionCube;\n          child.material.reflectivity = reflectivityValueDoors;\n          child.material.color = new THREE.Color(hex_color);\n          //child.material.alphaTest = 0.1;\n      }\n    });\n    elements[\"door\"].renderOrder = 35;\n\n    elements[\"drawer_front\"].traverse(function(child) {\n      if (child instanceof THREE.Mesh) {\n        if(window.cstm && window.cstm.item && (window.cstm.item.shelf_type === 1 && parseInt(window.cstm.item.material) === 0)) {\n          child.material.map = elements['doors_open_white'];\n        }\n        child.rotation.x = Math.PI / 2;\n        child.rotation.z = -Math.PI / 2;\n        child.geometry.center();\n        child.material.envMap =  reflectionCube;\n        child.material.reflectivity = reflectivityValueDoors;\n        child.material.color = new THREE.Color(hex_color);\n      }\n    });\n    elements[\"drawer_front\"].renderOrder = 35;\n\n    elements[\"vertical\"].traverse(function(child) {\n      if (child instanceof THREE.Mesh) {\n        child.material.envMap =  reflectionCube;\n        //child.material.combine = THREE.AdditiveBlending;\n        child.material.reflectivity = reflectivityValue;\n\n      }\n    });\n\n    elements[\"support\"].traverse(function(child) {\n      if (child instanceof THREE.Mesh) {\n        child.material.envMap =  reflectionCube;\n        //child.material.combine = THREE.AdditiveBlending;\n        child.material.reflectivity = reflectivityValue;\n\n      }\n    });\n\n    elements[\"insert\"].traverse(function(child) {\n      if (child instanceof THREE.Mesh) {\n        child.material.envMap =  reflectionCube;\n        //child.material.combine = THREE.AdditiveBlending;\n        child.material.reflectivity = reflectivityValue;\n      }\n    });\n\n    elements[\"support\"].renderOrder = 22;\n\n    elements[\"support-drawer\"].traverse(function(child) {\n      if (child instanceof THREE.Mesh) {\n        child.material.envMap =  reflectionCube;\n        //child.material.combine = THREE.AdditiveBlending;\n        child.material.reflectivity = reflectivityValue;\n\n      }\n    });\n    elements[\"support-drawer\"].renderOrder = 21;\n\n    elements[\"vertical\"].renderOrder = 2;\n    elements[\"horizontal\"].traverse(function(child) {\n      if (child instanceof THREE.Mesh) {\n        // child.rotation.x = Math.PI;\n        // child.rotation.z = Math.PI;\n        child.material.polygonOffset = true;\n        child.material.polygonOffsetFactor = 1.0;\n        child.material.polygonOffsetUnits = -1.0;\n        child.material.envMap =  reflectionCube;\n        //child.material.combine = THREE.AdditiveBlending;\n        child.material.reflectivity = reflectivityValue;\n      }\n    });\n    elements[\"horizontal\"].renderOrder = 20;\n\n    elements[\"handle_big\"].traverse(function(child) {\n      if (child instanceof THREE.Mesh) {\n        child.rotation.x = -Math.PI / 2;\n        child.material.envMap =  reflectionCube;\n        //child.material.combine = THREE.AdditiveBlending;\n        child.material.reflectivity = reflectivityValue;\n      }\n    });\n\n    elements['backs'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.color = new THREE.Color(backs_color);\n            child.material.reflectivity = reflectivityValue;\n        }\n    });\n    elements[\"backs\"].renderOrder = 18;\n\n    elements[\"handle_big\"].renderOrder = 40;\n\n    elements[\"handle_small\"].traverse(function(child) {\n      if (child instanceof THREE.Mesh) {\n        child.rotation.x = -Math.PI / 2;\n          child.material.envMap =  reflectionCube;\n          //child.material.combine = THREE.AdditiveBlending;\n          child.material.reflectivity = reflectivityValue;\n      }\n    });\n    elements[\"handle_small\"].renderOrder = 60;\n\n    elements[\"handle_short_left\"].traverse(function(child) {\n      if (child instanceof THREE.Mesh) {\n        //child.rotation.x = -Math.PI / 2;\n        child.material.map = elements['handle_short_left_texture'];\n\n        // child.material.envMap =  reflectionCube;\n        // child.material.reflectivity = reflectivityValue;\n      }\n    });\n    elements[\"handle_short_left\"].renderOrder = 1061;\n\n    elements[\"handle_short_left_shadow\"].traverse(function(child) {\n      if (child instanceof THREE.Mesh) {\n        child.material.map = elements['handle_short_left_texture'];\n        child.material.transparent = true;\n        child.material.depthTest =  true;\n        child.material.depthWrite = false;\n        child.material.polygonOffset = true;\n        child.material.polygonOffsetFactor = 1.0;\n        child.material.polygonOffsetUnits = -1.0;\n      }\n    });\n    elements[\"handle_short_left_shadow\"].renderOrder = 100000;\n\n    elements[\"top-bottom\"].traverse(function(child){\n      if(child instanceof THREE.Mesh){\n        // child.material.polygonOffset = true;\n        // child.material.polygonOffsetFactor = 1.0;\n        // child.material.polygonOffsetUnits = -1.0;\n        child.material.envMap =  reflectionCube;\n        //child.material.combine = THREE.AdditiveOperation;\n\n\n        child.material.reflectivity = reflectivityValue;\n      }\n    });\n\n    elements[\"left-right\"].traverse(function(child){\n      if(child instanceof THREE.Mesh){\n        child.material.polygonOffset = true;\n        child.material.polygonOffsetFactor = 1.0;\n        child.material.polygonOffsetUnits = -1.0;\n        child.material.envMap =  reflectionCube;\n        child.material.reflectivity = reflectivityValue;\n      }\n    });\n  }\n\n  getScene() {\n    return this.scene;\n  }\n  setScene(scene) {\n    this.scene = scene;\n    this.scene.background = new THREE.Color(0xf0f0f0);\n    this.scene.opacity = 0.5;\n  }\n  setBackgroundScene(scene) {\n    this.scene = scene;\n    this.scene.background = new THREE.Color(0xf0f0f0);\n    this.scene.opacity = 0.5;\n  }\n  clearScene(){\n    this.points = {\n        doors: [],\n        horizontals: [],\n        legs: [],\n        verticals: [],\n        supports: [],\n        additional_elements: {\n          shadow_left: [],\n          shadow_middle: [],\n          shadow_right: [],\n          shadow_side: []\n        }\n      };\n    this.scene.remove.apply(this.scene, this.scene.children);\n    this.scene.background = new THREE.Color(0xf0f0f0);\n    this.scene.opacity = 0.5;\n  }\n\n  rebuildWallsFromJson(new_points, scene) {\n     //new_points = {\"rows\":[190,190,190,190,190,190,190,190,190,190],\"horizontals\":[{\"y1\":90,\"y2\":108,\"x2\":1209,\"x1\":-1227,\"z1\":0,\"z2\":320,\"c_config_id\":7910},{\"y1\":890,\"y2\":908,\"x2\":1209,\"x1\":-1227,\"z1\":0,\"z2\":320,\"c_config_id\":7910}],\"material\":0,\"modules\":[],\"backs\":[{\"y1\":108,\"y2\":890,\"x2\":-421,\"x1\":-1209,\"z1\":0,\"z2\":0,\"c_config_id\":7910},{\"y1\":108,\"y2\":890,\"x2\":385,\"x1\":-403,\"z1\":0,\"z2\":0,\"c_config_id\":7910},{\"y1\":108,\"y2\":890,\"x2\":1191,\"x1\":403,\"z1\":0,\"z2\":0,\"c_config_id\":7910}],\"height\":908,\"verticals\":[{\"y1\":499,\"y2\":499,\"x2\":-1209,\"x1\":-1227,\"z1\":0,\"z2\":320,\"c_config_id\":7910},{\"y1\":499,\"y2\":499,\"x2\":-403,\"x1\":-421,\"z1\":0,\"z2\":320,\"c_config_id\":7910},{\"y1\":499,\"y2\":499,\"x2\":-403,\"x1\":-421,\"z1\":0,\"z2\":320,\"c_config_id\":7910},{\"y1\":499,\"y2\":499,\"x2\":403,\"x1\":385,\"z1\":0,\"z2\":320,\"c_config_id\":7910},{\"y1\":499,\"y2\":499,\"x2\":403,\"x1\":385,\"z1\":0,\"z2\":320,\"c_config_id\":7910},{\"y1\":499,\"y2\":499,\"x2\":1209,\"x1\":1191,\"z1\":0,\"z2\":320,\"c_config_id\":7910}],\"drawers\":[],\"doors\":[{\"y2\":890,\"flip\":true,\"y1\":108,\"x2\":-806,\"x1\":-1209,\"z1\":0,\"z2\":320,\"c_config_id\":7910},{\"y2\":890,\"flip\":false,\"y1\":108,\"x2\":-421,\"x1\":-806,\"z1\":0,\"z2\":320,\"c_config_id\":7910},{\"y2\":890,\"flip\":true,\"y1\":108,\"x2\":0,\"x1\":-403,\"z1\":0,\"z2\":320,\"c_config_id\":7910},{\"y2\":890,\"flip\":false,\"y1\":108,\"x2\":385,\"x1\":0,\"z1\":0,\"z2\":320,\"c_config_id\":7910},{\"y2\":890,\"flip\":true,\"y1\":108,\"x2\":806,\"x1\":403,\"z1\":0,\"z2\":320,\"c_config_id\":7910},{\"y2\":890,\"flip\":false,\"y1\":108,\"x2\":1191,\"x1\":806,\"z1\":0,\"z2\":320,\"c_config_id\":7910}],\"legs\":[],\"supports\":[],\"row_styles\":[1,1,1,1,1,1,1,1,1,1],\"additional_elements\":{\"styles\":[[2,0,0,2,1,1],[2,0,0,2,1,1],[2,0,0,2,1,1],[2,0,0,2,1,1],[2,0,0,2,1,1],[2,0,0,2,1,1],[2,0,0,2,1,1],[2,0,0,2,1,1],[2,0,0,2,1,1],[2,0,0,2,1,1]],\"shadow_middle\":[],\"shadow_right\":[],\"shadow_side\":[],\"shadow_left\":[]}}\n\n     new_points.topBottomWalls = this.getTopBottomWallPosition(new_points);\n     new_points.leftRightWalls = this.getLeftRightWallPosition(new_points);\n     new_points.additionalHorizontalElements = this.getAdditionalHorizontalPanelPosition(new_points);\n\n     // new_points.drawers = [{\"y2\":199,\"flip\":0,\"x2\":532,\"y1\":9,\"x1\":-59,\"type\":0,\"z1\":320,\"z2\":301}];\n     // new_points.doors = [{\"y2\":477,\"flip\":0,\"x2\":-77,\"y1\":217,\"x1\":-532,\"type\":0,\"z1\":320,\"z2\":301}];\n\n      this.drawWalls(new_points, {}, scene, true);\n      _points = new_points;\n  }\n\n  getIndicatorBoxesPositions(returnObject) {\n    let boxes = this.walls[\"boxes\"].filter(box => box.name == \"buttons\").filter(Boolean);\n    return boxes.map(box => returnObject ? box : box.position);\n  }\n\n  drawWalls(points, ivy, scene, snapping) {\n    if (typeof points.boxes == \"undefined\") {\n      points.boxes = [];\n    }\n\n    let rebuildCounter = getUrlParameter(\"rebuild\") || 1;\n    let shadow_translator = {\n      shadow_middle: 0,\n      shadow_left: 1,\n      shadow_right: 2,\n      shadow_side: 0\n    };\n\n    // remove legs because there could be less\n    for (let i = 0; i < this.walls.boxes.length; i++) {\n      scene.remove(this.walls.boxes[i]);\n    }\n\n    this.getIndicatorBoxesPositions(true).map(box => {\n      scene.remove(box);\n    });\n\n  \n    if(points.components) {\n      this.componentHoverBoxes.map((box) => scene.remove(box));\n      this.componentHoverBoxes = [];\n      points.components.map(this.createComponentHoverBox.bind(this));\n    }\n    \n    _.remove(this.walls[\"boxes\"], (object) => object.name == \"buttons\");\n\n    \n    for (let j = 0; j < rebuildCounter; j++) {\n      let typical_elements = [\"verticals\", \"horizontals\", \"supports\", \"backs\", \"topBottomWalls\",\n        \"leftRightWalls\", \"boxes\", \"buttons\" ,\"inserts\"];\n      typical_elements.forEach(element_type => {\n        if (this.points[element_type] === undefined) {\n          this.points[element_type] = [];\n        }\n        if (this.points[element_type] && this.points[element_type].length > 0 && points[element_type].length < this.points[element_type].length) {\n\n          let dif = this.points[element_type].length - points[element_type].length;\n\n          if (this.walls[element_type]) {\n            for ( let j = this.walls[element_type].length - dif; j < this.walls[element_type].length;  j++) {\n              scene.remove(this.walls[element_type][j]);\n            }\n            this.walls[element_type].length = this.walls[element_type].length - dif < 0 ? 0 : this.walls[element_type].length - dif;\n          }\n        }\n\n        //arrays for holding points\n        let newPoints = [];\n        let oldPoints = [];\n        // build points\n        for (let i = 0; i < points[element_type].length; i++) {\n          //if there are points push them for comparison\n          if (\n            typeof this.points[element_type][i] !== \"undefined\"\n          ) {\n            newPoints.push({\n              bottom: new THREE.Vector3(\n                points[element_type][i][\"x1\"],\n                points[element_type][i][\"y1\"],\n                points[element_type][i][\"z1\"]\n              ),\n              top: new THREE.Vector3(\n                points[element_type][i][\"x2\"],\n                points[element_type][i][\"y2\"],\n                points[element_type][i][\"z2\"]\n              ),\n              raw: points[element_type][i],\n            });\n            oldPoints.push({\n              bottom: new THREE.Vector3(\n                this.points[element_type][i][\"x1\"],\n                this.points[element_type][i][\"y1\"],\n                this.points[element_type][i][\"z1\"]\n              ),\n              top: new THREE.Vector3(\n                this.points[element_type][i][\"x2\"],\n                this.points[element_type][i][\"y2\"],\n                this.points[element_type][i][\"z2\"]\n              )\n            });\n          } else {\n            // if there is more points add new ones\n            newPoints.push({\n              bottom: new THREE.Vector3(\n                points[element_type][i][\"x1\"],\n                points[element_type][i][\"y1\"],\n                points[element_type][i][\"z1\"]\n              ),\n              top: new THREE.Vector3(\n                points[element_type][i][\"x2\"],\n                points[element_type][i][\"y2\"],\n                points[element_type][i][\"z2\"]\n              ),\n              raw: points[element_type][i],\n            });\n          }\n        }\n        //creating points for top and bottom;\n\n        // create verticals from these points aray\n\n        for (let i = 0; i < newPoints.length; i++) {\n          if (element_type === \"verticals\") {\n            this.createSingleVertical(i, oldPoints[i], newPoints[i]);\n          } else if (element_type === \"horizontals\") {\n            this.createHorizontal(i, oldPoints[i], newPoints[i]);\n          } else if (element_type === \"supports\") {\n            let supportType;\n            if((points[element_type][i].x2 + points[element_type][i].x1) < 0 ){\n              supportType = Math.abs(points[element_type][i].x2) > Math.abs(points[element_type][i].x1) ? 'left' : 'right';\n            } else {\n              supportType = Math.abs(points[element_type][i].x2) < Math.abs(points[element_type][i].x1) ? 'left' : 'right';\n            }\n            this.createSingleSupport(i, oldPoints[i], newPoints[i], supportType);\n          } else if (element_type === \"backs\") {\n            this.createSingleBack(i, oldPoints[i], newPoints[i]);\n          } else if (element_type === \"component\") {\n            console.log(\"bboxcomp\",element_type, newPoints[i] );\n          } else if (element_type === \"topBottomWalls\") {\n            this.createHorizontalPanels(i, oldPoints[i], newPoints[i]);\n          } else if (element_type === \"leftRightWalls\") {\n            this.createVerticalPanels(i, oldPoints[i], newPoints[i], ivy.pattern );\n          } else if (element_type === \"inserts\") {\n                this.createInsertAndPlynth(newPoints[i], newPoints);\n          } else if (element_type === \"buttons\") {\n                this.createButton(newPoints[i], newPoints);\n          } else if (element_type === \"boxes\") {\n                this.createInsertAndPlynth(newPoints[i], newPoints);\n          }\n        }\n\n\n      });\n\n      // create shadows\n\n\n\n      let shadows = points.additional_elements;\n      let getSizeFromPoint = function(item) {\n        return new THREE.Vector3(\n          Math.abs(item.x1 - item.x2),\n          Math.abs(item.y1 - item.y2),\n          Math.abs(item.z1 - item.z2)\n        );\n      };\n      let getPositionFromPoint = function(item) {\n        return new THREE.Vector3(\n          (item.x1 + item.x2) / 2,\n          item.y1 - 1.5,\n          (item.z1 + item.z2) / 2\n        );\n      };\n      this.walls.shadows.forEach(shadow_list => {\n        for (let i = 0; i < shadow_list.length; i++) {\n          scene.remove(shadow_list[i]);\n        }\n        shadow_list.length = 0;\n      });\n      for (let shadow_type in shadows) {\n        if (shadow_type === \"styles\") {\n          continue; // as its style for doors, not shadow\n        }\n        let shadow_list = shadows[shadow_type];\n\n        let newPoints = [];\n        for (let i = 0; i < shadow_list.length; i++) {\n          newPoints.push({\n            size: getSizeFromPoint(shadow_list[i]),\n            position: getPositionFromPoint(shadow_list[i])\n          });\n        }\n        for (let i = 0; i < newPoints.length; i++) {\n          this.createShadows(\n            i,\n            undefined,\n            newPoints[i],\n            shadow_translator[shadow_type]\n          );\n        }\n      }\n\n      for (let i = 0; i < this.walls.additionalHorizontalElements.length; i++) {\n        scene.remove(this.walls.additionalHorizontalElements[i]);\n      }\n      this.walls.additionalHorizontalElements.length = 0;\n      // create double openings\n      for (let i = 0; i < points.additionalHorizontalElements.length; i++) {\n        this.createAdditionalHorizontalPanels(points.additionalHorizontalElements[i]);\n      }\n\n      // remove legs because there could be less\n      for (let i = 0; i < this.walls.legs.length; i++) {\n        scene.remove(this.walls.legs[i]);\n      }\n      this.walls.legs.length = 0;\n      // create new legs\n      for (let i = 0; i < points.legs.length; i++) {\n        this.createLegs(points.legs[i]);\n      }\n\n      // plinths\n      for (let i = 0; i < this.walls.plinth.length; i++) {\n        scene.remove(this.walls.plinth[i]);\n      }\n      this.walls.plinth.length = 0;\n      for (let i = 0; i < points.plinth.length; i++) {\n          this.createInsertAndPlynth({\n                  bottom: {x: points.plinth[i].x1, y: points.plinth[i].y1, z: points.plinth[i].z1},\n                  top: {x: points.plinth[i].x2, y: points.plinth[i].y2, z: points.plinth[i].z2}\n              }\n          );\n      }\n\n      this.door_lists = [[], [], [], [], [], [], [], [], [], [], [], []];\n      this.door_lists_elements = [\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        []\n      ];\n\n      this.drawers_lists = [[], [], [], [], [], [], [], [], [], [], [], []];\n\n      for (let i = 0; i < this.walls.doors.length; i++) {\n        scene.remove(this.walls.doors[i]);\n      }\n\n      for (let i = 0; i < this.walls.door_groups.length; i++) {\n        scene.remove(this.walls.door_groups[i]);\n      }\n      this.walls.door_groups.length = 0;\n      this.walls.doors.length = 0;\n\n      for (let i = 0; i < points.doors.length; i++) {\n        this.createDoor(\n          getPositionFromPoint(points.doors[i]),\n          getSizeFromPoint(points.doors[i]),\n          points.doors[i].type,\n          points.doors[i].flip,\n          [278,278,278,278,278,278,278,278,278,278,278,278,278,],\n           0,\n            !!points.doors[i].selected,\n            points.doors[i].innerOffset\n        );\n      }\n      // drawers\n\n      for (let i = 0; i < this.walls.drawers.length; i++) {\n        scene.remove(this.walls.drawers[i]);\n      }\n      this.walls.drawers.length = 0;\n      // create new drawers\n      for (let i = 0; i < points.drawers.length; i++) {\n        this.createDrawers(\n          getPositionFromPoint(points.drawers[i]),\n          getSizeFromPoint(points.drawers[i]),\n          [278,278,278,278,278,278,278,278,278,278,278,278,278,],\n          this.shelf_type,\n          points.drawers[i]\n        );\n      }\n\n      // handlers\n      for (let i = 0; i < this.handlers.length; i++) {\n        scene.remove(this.handlers[i]);\n      }\n      for (let i = 0; i < ivy.rows; i++) {\n        let height = 0;\n        for (let j = 0; j < i; j++) {\n          height += ivy.constants.rowHeight[j];\n        }\n        height += ivy.constants.rowHeight[i] / 2;\n        let obj = new THREE.Object3D();\n        /*\n                    var group = new THREE.Group();\n                    var geometry = new THREE.BoxGeometry( 100, 100, 100 );\n                    var material = new THREE.MeshBasicMaterial( {color: 0x00ff00} );\n                    group.add(new THREE.Mesh( geometry, material ));\n                    */\n        this.handlers.push(obj);\n        this.handlers[i].position.setX(ivy.width / 2 + 40);\n        this.handlers[i].position.setY(height);\n        this.handlers[i].position.setZ(250);\n        this.handlers[i].matrixWorldNeedsUpdate = true;\n        scene.add(this.handlers[i]);\n      }\n  }\nlet calculatedCapacity = getTotalCapacity(\n      points,\n      ivy.rows,\n      ivy.pattern,\n      ivy.width\n    );\n\n    this.capacity = calculatedCapacity[0]; // Shelf total load\n    this.compartmentCapacity = [calculatedCapacity[1], calculatedCapacity[2]]; // [ Capacity Min, Capacity Max ]\n\n    this.points = points;\n    this.row_styles_availability = points.additional_elements.styles;\n    this.depth_previous = this.depth;\n\n    //generateSVG(this.width, this.getHeight(), this.points.horizontals, this.points.verticals, this.points.supports, !snapping);\n    //console.log('================= GenerateCanvas Call -- fn RebuildWalls=============================');\n    //window.dConf.fn.timerStop();\n\n    if (typeof generateCanvas != \"undefined\") {\n      generateCanvas(\n        ivy.width,\n        ivy.getHeight(),\n        this.points.horizontals,\n        this.points.verticals,\n        this.points.supports\n      );\n    }\n\n    if (!this.skip_animations){\n\n      for (let i = 0; i < this.magical_materials_for_rows[\"doors\"].length; i++) {\n        this.magical_materials_for_rows[\"doors\"][\n          i\n        ].uniforms.blending_ratio.value = 0;\n        this.magical_materials_for_rows[\"handlers\"][\n          i\n        ].uniforms.blending_ratio.value = 0;\n      }\n    }\n    //console.log(\"ok, here pubsub\", snapping, initial, snapping || initial, PubSub);\n    if (typeof PubSub != \"undefined\") {\n        PubSub.publishSync(\"shelfChanged\", points);\n        if (snapping) {\n          //TODO: assign price as pubsub\n          PubSub.publish(\"shelfChangedSnapped\", points);\n        } else {\n          PubSub.publishSync(\"shelfChanged\", points);\n        }\n    }\n  }\n\n  setShelfType(shelfType=0) {\n    if (shelfType == 0) {\n      this.row_a = 200;\n      this.row_b = 300;\n      this.row_c = 400;\n      this.shelf_type = 0;\n    } else if (shelfType == 1) {\n      this.row_a = 200;\n      this.row_b = 300;\n      this.row_c = 400;\n      this.shelf_type = 1;\n    }\n  }\n\n  setDesignerMode(mode) {\n    this.designer_mode = mode;\n  }\n\n  setDnaTool(dnaTools) {\n      this.dnaTools = dnaTools;\n  }\n\n  createSingleVertical(index, oldPoints, newPoints) {\n    let dif;\n    let scene = this.scene;\n    // check if there are any old points, used for initial run\n    if (typeof oldPoints !== \"undefined\") {\n      dif = this.compare_dnas(oldPoints, newPoints);\n    } else {\n      dif = {\n        newitem: true,\n        scaled: true,\n        only_moved: true\n      };\n    }\n    if (this.depth != this.depth_previous) {\n      dif.scaled = true;\n      dif.same = false;\n    }\n\n    // if its the same quit\n    if (dif.same === true) {\n      return true;\n    }\n    let vertical;\n    // if new item\n    if (dif.newitem) {\n      // clone the base element\n      vertical = elements[\"vertical\"].clone();\n    } else {\n      // else take a vertical already existing\n      vertical = this.walls.verticals[index];\n    }\n\n    let distance = Math.abs(newPoints.bottom.y - newPoints.top.y) + 3;\n\n    // if scaled set proper height\n    if (dif.scaled) {\n      let verticalBox = boundingBox.setFromObject(elements[\"vertical\"]);\n      vertical.scale.setX(1);\n      vertical.scale.setY(this.depth / 100);\n       vertical.scale.setZ(distance / 100);\n      vertical.rotation.x = -Math.PI / 2;\n      if (dif.newitem) {\n        // if its a new item it also needs width and depth\n        vertical.scale.setX(1);\n        vertical.scale.setY(this.depth / 100);\n        vertical.scale.setZ(distance / 100);\n      }\n      // set proper position\n      vertical.position.setX(newPoints.bottom.x);\n      vertical.position.setY(\n        Math.min(newPoints.bottom.y, newPoints.top.y)-1.5\n      );\n    }\n\n    if (dif.only_moved) {\n      // if it was only moved changed x,y. Z is always the same\n      vertical.position.setX(newPoints.bottom.x);\n      vertical.position.setY(\n        Math.min(newPoints.bottom.y, newPoints.top.y)-1.5\n\n      );\n      vertical.scale.setY(this.depth / 100);\n      vertical.scale.setZ(distance / 100);\n    }\n\n    if (dif.newitem) {\n      // if it is a new item add it to scene and to the furniture parts pool\n      vertical.name = \"vertical\";\n      scene.add(vertical);\n      this.walls.verticals.push(vertical);\n    }\n  }\n\n  createSingleBack(index, oldPoints, newPoints) {\n    let dif;\n    let scene = this.scene;\n\n    if (typeof oldPoints !== \"undefined\") {\n      dif = this.compare_dnas(oldPoints, newPoints);\n    } else {\n      dif = {\n        newitem: true,\n        scaled: true,\n        only_moved: true\n      };\n    }\n    if (dif.same === true) {\n      return true;\n    }\n\n    let back;\n    if (dif.newitem) {\n      back = elements[\"top-bottom\"].clone();\n    } else {\n      back = this.walls.backs[index];\n    }\n    let backBox = boundingBox.setFromObject(elements[\"top-bottom\"]);\n\n    let scaleX, scaleY;\n    back.rotation.x = -Math.PI;\n\n    scaleY = Math.abs(newPoints.bottom.y - newPoints.top.y - 18) / backBox.size().x;\n    scaleX = Math.abs(newPoints.bottom.x - newPoints.top.x - 18) / backBox.size().x;\n\n    back.position.setX((newPoints.bottom.x + newPoints.top.x)/2);\n    back.position.setY(newPoints.bottom.y - 9);\n    back.position.setZ(22);\n    back.scale.setY(scaleY);\n    back.scale.setX(scaleX);\n\n    if (dif.newitem) {\n      back.name = \"backss\";\n      scene.add(back);\n      if (this.walls.backs === undefined) {\n        this.walls.backs= [];\n      }\n      this.walls.backs.push(back);\n    }\n  }\n\n  createSingleSupport(index, oldPoints, newPoints, supportType) {\n\n    let dif;\n    let scene = this.scene;\n    if (typeof oldPoints !== \"undefined\") {\n      dif = this.compare_dnas(oldPoints, newPoints);\n    } else {\n      dif = {\n        newitem: true,\n        scaled: true,\n        only_moved: true\n      };\n    }\n    if (this.depth != this.depth_previous) {\n      dif.scaled = true;\n      dif.same = false;\n    }\n    if (dif.same === true) {\n      return true;\n    }\n\n    let support;\n    if (dif.newitem) {\n      support = elements[\"support\"].clone();\n    } else {\n      support = this.walls[\"supports\"][index];\n    }\n    let supportBox = boundingBox.setFromObject(elements[\"horizontal\"]);\n\n    let scaleX, scaleZ;\n    if(supportType == 'left') {\n        support.rotation.x = -Math.PI / 2;\n    } else {\n      support.rotation.x = Math.PI / 2;\n    }\n    if (dif.newitem) {\n\n    }\n    scaleZ =\n      Math.abs(newPoints.bottom.y - newPoints.top.y) / supportBox.size().x;\n    //scaleX = this.constants.gradientSupportWidth / supportBox.size().x;\n    scaleX =\n      Math.abs(newPoints.bottom.x - newPoints.top.x) / supportBox.size().x;\n\n    if (dif.scaled) {\n      support.scale.setZ(scaleZ);\n      support.scale.setX(scaleX);\n      if (dif.newitem) {\n        support.scale.setY(1);\n      }\n      supportBox = boundingBox.setFromObject(support);\n      support.position.set(\n        Math.min(newPoints.bottom.x, newPoints.top.x) + supportBox.size().x / 2,\n        Math.min(newPoints.bottom.y, newPoints.top.y) +\n          Math.abs(newPoints.bottom.y - newPoints.top.y)/2,\n        0\n      );\n    }\n\n    if (dif.only_moved) {\n      supportBox = boundingBox.setFromObject(support);\n      support.position.set(\n        Math.min(newPoints.bottom.x, newPoints.top.x) + supportBox.size().x / 2,\n        Math.min(newPoints.bottom.y, newPoints.top.y) +\n          Math.abs(newPoints.bottom.y - newPoints.top.y)/2,\n        9\n      );\n    }\n\n    if (dif.newitem) {\n      support.name = \"supports\";\n      scene.add(support);\n      if (this.walls[\"supports\"] === undefined) {\n        this.walls[\"supports\"] = [];\n      }\n      this.walls[\"supports\"].push(support);\n    }\n  }\n\n  createHorizontal(index, oldPoints, newPoints) {\n    let dif;\n    let scene = this.scene;\n    if (typeof oldPoints !== \"undefined\") {\n      dif = this.compare_dnas(oldPoints, newPoints);\n    } else {\n      dif = {\n        newitem: true,\n        scaled: true,\n        only_moved: true\n      };\n    }\n    if (this.depth != this.depth_previous) {\n      dif.scaled = true;\n      dif.same = false;\n    }\n    if (dif.same === true) {\n      return true;\n    }\n    let horizontal;\n    if (dif.newitem) {\n      horizontal = elements[\"horizontal\"].clone();\n    } else {\n      horizontal = this.walls.horizontals[index];\n    }\n\n    let distance = Math.abs(newPoints.bottom.x - newPoints.top.x);\n    if (dif.scaled) {\n      let horizontalBox = boundingBox.setFromObject(elements[\"horizontal\"]);\n      let scale = distance / horizontalBox.size().x;\n      horizontal.scale.setX(scale);\n      horizontal.rotation.x = -Math.PI / 2;\n\n      horizontal.scale.setZ(1);\n      horizontal.scale.setY(this.depth / horizontalBox.size().y);\n      horizontal.position.setY(newPoints.bottom.y);\n      horizontal.position.setX(\n        Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2\n      );\n    }\n\n    if (dif.only_moved) {\n      horizontal.position.setY(newPoints.bottom.y);\n      horizontal.position.setX(\n        Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2\n      );\n    }\n\n    if (dif.newitem) {\n      horizontal.name = \"horizontal\";\n      this.scene.add(horizontal);\n      this.walls.horizontals.push(horizontal);\n    }\n  }\n\n  // the same as verticals, horizontals but also takes what type of shadow it is to know which element to clone\n\n\n\n  getLeftRightWallPosition(points){\n    //console.log('getSidePanelsPosition', points.verticals);\n    let topLeft = [];\n    let tempMax = 0;\n    let tempMin = 0;\n    points.verticals.map((item) => {\n      if( Math.abs(item.x1) > tempMax ||  Math.abs(item.x2) > tempMax ){\n        tempMax =  Math.abs(item.x1) > Math.abs(item.x2) ? item.x1 : item.x2\n      }\n      if( item.x1 < tempMin ||  item.x2 < tempMin ){\n        tempMin =  item.x1 > item.x2 ? item.x2 : item.x1\n      }\n    });\n\n    points.verticals.map((item) => {\n      if(item.x1 === tempMax || item.x1 === tempMin ){\n        topLeft.push(item);\n      }\n    });\n\n    return topLeft\n  }\n\n  getTopBottomWallPosition(points){\n    let topBottom = [];\n    let minHorizontal = points.horizontals[0]['x1'];\n    let maxHorizontal = points.horizontals[points.horizontals.length-1]['x2'];\n    let maxHeight = _.max(points.horizontals.map(x=>x['y1']));\n    let minHeight = _.min(points.horizontals.map(x=>x['y1']));\n    for (let i=0; i< points.horizontals.length; i++){\n      if (minHorizontal > _.min([points.horizontals[i]['x1'],points.horizontals[i]['x2']])) {\n        minHorizontal = _.min([points.horizontals[i]['x1'],points.horizontals[i]['x2']]);\n      }\n      if (maxHorizontal < _.max([points.horizontals[i]['x1'],points.horizontals[i]['x2']])) {\n        maxHorizontal = _.max([points.horizontals[i]['x1'],points.horizontals[i]['x2']]);\n      }\n    }\n    topBottom.push({'x1': minHorizontal, 'x2': maxHorizontal, 'y1': minHeight, 'y2': minHeight});\n    topBottom.push({'x1': minHorizontal, 'x2': maxHorizontal, 'y1': maxHeight, 'y2': maxHeight});\n\n    return topBottom;\n  }\n\n  getAdditionalHorizontalPanelPosition(points){\n    let minX = _.min(points.horizontals.map(x=>_.min([x['x1'],x['x2']])));\n    let maxX = _.max(points.horizontals.map(x=>_.max([x['x1'],x['x2']])));\n    let panelPoints = [];\n\n    for (let i of points.horizontals){\n      let localMinX = _.min([i['x1'],i['x2']]);\n      let localMaxX = _.max([i['x1'],i['x2']]);\n      if (localMinX > minX){\n        panelPoints.push({'x': localMinX - 1, 'y': i['y1']})\n      }\n      if (localMaxX < maxX){\n        panelPoints.push({'x': localMaxX + 1, 'y': i['y1']})\n      }\n    }\n    return panelPoints; //topBottom;\n  }\n\n  createVerticalPanels(index, oldPoints, newPoints, pattern ) {\n    let dif;\n    let scene = this.scene;\n\n    if (typeof oldPoints !== \"undefined\") {\n      dif = this.compare_dnas(oldPoints, newPoints);\n    } else {\n      dif = {\n        newitem: true,\n        scaled: true,\n        only_moved: true\n      };\n    }\n    if (this.depth != this.depth_previous) {\n      dif.scaled = true;\n      dif.same = false;\n    }\n    if (dif.same === true) {\n      return true;\n    }\n\n    let wall;\n\n    if (dif.newitem) {\n      wall = elements[\"left-right\"].clone();\n    } else {\n      wall = this.walls.leftRightWalls[index];\n    }\n\n    let distance = Math.abs(newPoints.bottom.y - newPoints.top.y);\n    let scale = (distance + 3)/ 100;\n    // LEFT/RIGHT - half of horizontal edge width\n    let factor = 0;\n\n    if(newPoints.bottom.x < 0 ){\n      //Left panel\n        factor = - 7.5;\n    } else {\n      // Right panel\n      factor = 7.5;\n    }\n    // chowanie dla slanta scianki\n    if(pattern == 0){\n      factor = - factor;\n    }\n\n    if (dif.scaled) {\n\n      let leftRightWall = boundingBox.setFromObject(elements[\"left-right\"]);\n      wall.rotation.x = - Math.PI/2;\n      wall.scale.setY(this.depth / 100);\n        wall.scale.setZ(scale);\n\n\n      wall.position.setY(newPoints.bottom.y - 1.5);\n      wall.position.setX(newPoints.bottom.x + factor);\n    }\n    //\n    if (dif.only_moved) {\n      wall.position.setY(newPoints.bottom.y - 1.5);\n      wall.position.setX(newPoints.bottom.x + factor);\n      wall.scale.setY(this.depth / 100);\n      wall.scale.setZ(scale);\n    }\n    if (dif.newitem) {\n      wall.name = \"Left-right-wall\";\n      this.scene.add(wall);\n      this.walls.leftRightWalls.push(wall);\n    }\n\n  }\n\n\n  createAdditionalHorizontalPanels(newPoints) {\n    let dif;\n    let scene = this.scene;\n\n\n    let panel = elements[\"horizontal-plug\"].clone();\n    panel.rotation.x = -Math.PI / 2;\n\n    panel.position.setY(newPoints.y);\n    panel.position.setX(newPoints.x);\n    //panel.position.setZ();\n\n    let panelBox = boundingBox.setFromObject(elements[\"horizontal-plug\"]);\n\n    panel.scale.setY(this.depth / panelBox.size().y);\n\n\n    panel.name = \"additionalHorizontalPanel\";\n    this.scene.add(panel);\n    this.walls.additionalHorizontalElements.push(panel);\n  }\n\n  createHorizontalPanels(index, oldPoints, newPoints) {\n    let dif;\n    let scene = this.scene;\n    if (typeof oldPoints !== \"undefined\") {\n      dif = this.compare_dnas(oldPoints, newPoints);\n    } else {\n      dif = {\n        newitem: true,\n        scaled: true,\n        only_moved: true\n      };\n    }\n    if (this.depth != this.depth_previous) {\n      dif.scaled = true;\n      dif.same = false;\n    }\n    if (dif.same === true) {\n      return true;\n    }\n\n    let wall;\n\n    let distance = Math.abs(newPoints.bottom.x - newPoints.top.x);\n\n    // wall = elements[\"top-bottom\"].clone();\n    // wall.scale.y = 3.2;\n    // wall.rotation.x = - Math.PI/2;\n    //wall.position.y = index ? (newPoints.bottom.y + 7.5) : (newPoints.bottom.y) - 7.5;\n    //wall.name = \"wall\";\n    // this.scene.add(wall);\n\n    if (dif.newitem) {\n      wall = elements[\"top-bottom\"].clone();\n    } else {\n      wall = this.walls.topBottomWalls[index];\n    }\n    //\n    //let distance = Math.abs(newPoints.bottom.x - newPoints.top.x);\n    if (dif.scaled) {\n      let topBottomWall = boundingBox.setFromObject(elements[\"top-bottom\"]);\n      let scale = distance / topBottomWall.size().x;\n      wall.scale.setX(scale);\n      wall.rotation.x = - Math.PI/2;\n\n      if (dif.newitem) {\n        wall.scale.setZ(1);\n      }\n      wall.scale.setY(this.depth / topBottomWall.size().y);\n\n      wall.position.y = index ? (newPoints.bottom.y + 7.5) : (newPoints.bottom.y) - 7.5;\n      wall.position.setX(\n        Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2\n      );\n    }\n    //\n    if (dif.only_moved) {\n      //wall.position.setY(newPoints.bottom.y);\n      wall.position.y = index ? (newPoints.bottom.y + 7.5) : (newPoints.bottom.y) - 7.5;\n      wall.position.setX(\n        Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2\n      );\n    }\n    //\n    if (dif.newitem) {\n      wall.name = \"wall\";\n      this.scene.add(wall);\n      this.walls.topBottomWalls.push(wall);\n    }\n  }\n\n  setDnaTool(dnaTools) {\n    this.dnaTools = dnaTools;\n  }\n\n  setMaterialColor({\n    color,\n    shelf_type,\n    skipTracking = true,\n\n  }){\n\n    let loaclElements = this.elements;\n    let walls = this.walls;\n    let magicalElements = this.magical_materials_for_rows;\n\n   /* if (!window.render_test) {\n        return self.setOldColor(value,skipTracking);\n    }\n    */\n\n    skipTracking = skipTracking || false;\n\n    let opacity;\n    let hex_color;\n    let hex_handler_color;\n    let backs_color;\n    let reflectivityValue;\n    let reflectivityValueDoors;\n    let material_id;\n\n    [\n      material_id,\n      color,\n      opacity,\n      hex_color,\n      hex_handler_color,\n      backs_color,\n      reflectivityValue,\n      reflectivityValueDoors\n      ] = getDefinitionForMaterial (color, parseInt(shelf_type || window.cstm.item.shelf_type));\n\n    // change base elements based on color\n    let reflectionCube = loaclElements['cubemap'];\n\n    loaclElements['vertical'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.dispose();\n            child.material.map = loaclElements[color+'-vert'];\n            child.material.needsUpdate = true;\n            //child.material.color = new THREE.Color(1,1,1);\n            child.material.transparent = false;\n            child.material.reflectivity = reflectivityValue;\n        }\n    });\n\n\n    loaclElements['horizontal'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.dispose();\n            child.material.map = loaclElements[color+'-hori'];\n            child.material.needsUpdate = true;\n            //child.material.color = new THREE.Color(1,1,1);\n            child.material.transparent = false;\n            child.material.reflectivity = reflectivityValue;\n        }\n    });\n\n    loaclElements['horizontal-plug'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.dispose();\n            child.material.map = loaclElements[color+'-hori'];\n            child.material.needsUpdate = true;\n            child.material.transparent = false;\n            child.material.reflectivity = reflectivityValue;\n        }\n    });\n\n    loaclElements['support'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.dispose();\n            child.material.map = elements[color+'-support'];\n            child.material.needsUpdate = true;\n            child.material.color = new THREE.Color(1,1,1);\n            child.material.transparent = false;\n            child.material.reflectivity = reflectivityValue;\n            child.renderOrder = 40;\n        }\n    });\n\n    loaclElements['insert'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.dispose();\n            child.material.map = elements[color+'-support'];\n            child.material.needsUpdate = true;\n            child.material.color = new THREE.Color(1,1,1);\n            child.material.transparent = false;\n            child.material.reflectivity = reflectivityValue;\n            child.renderOrder = 40;\n        }\n    });\n\n    loaclElements['support-drawer'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.dispose();\n            child.material.map = loaclElements[color+'-support-drawer'];\n            child.material.needsUpdate = true;\n            child.material.color = new THREE.Color(1,1,1);\n            child.material.transparent = false;\n            child.material.reflectivity = reflectivityValue;\n        }\n    });\n\n    loaclElements['shadow'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.dispose();\n            child.material.map = loaclElements[color+'-shadowbox'];\n            child.material.needsUpdate = true;\n            child.material.color = new THREE.Color(1,1,1);\n            child.material.transparent = false;\n            child.material.reflectivity = reflectivityValue;\n            child.renderOrder = 0;\n        }\n    });\n\n    loaclElements['shadow-left'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.dispose();\n            child.material.map = loaclElements[color+'-shadowbox'];\n            child.material.needsUpdate = true;\n            child.material.color = new THREE.Color(1,1,1);\n            child.material.transparent = false;\n            child.material.reflectivity = reflectivityValue;\n        }\n    });\n\n    loaclElements['shadow-right'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.dispose();\n            child.material.map = loaclElements[color+'-shadowbox'];\n            child.material.needsUpdate = true;\n            child.material.color = new THREE.Color(1,1,1);\n            child.material.transparent = false;\n            child.material.reflectivity = reflectivityValue;\n        }\n    });\n\n    loaclElements['left-right'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.dispose();\n            child.material.map = loaclElements[color+'-vert'];\n            child.material.needsUpdate = true;\n            //child.material.color = new THREE.Color(1,1,1);\n            child.material.transparent = false;\n            child.material.reflectivity = reflectivityValue;\n        }\n    });\n\n    loaclElements['top-bottom'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.dispose();\n            child.material.map = loaclElements[color+'-hori'];\n            child.material.needsUpdate = true;\n            //child.material.color = new THREE.Color(1,1,1);\n            child.material.reflectivity = reflectivityValue;\n        }\n    });\n\n    loaclElements['backs'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.color = new THREE.Color(backs_color);\n            //child.material.reflectivity = reflectivityValue;\n            child.renderOrder= -2000;\n        }\n    });\n\n    loaclElements['drawer_front'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            if(typeof ivy !== 'undefined') {\n                if (window.cstm.item.shelf_type === 1 && parseInt(ivy.material) === 0) {\n                    child.material.map = loaclElements['doors_open_white'];\n                } else {\n                    child.material.map = loaclElements['doors_open'];\n                }\n            }\n            child.material.color = new THREE.Color(hex_color);\n            child.material.reflectivity = reflectivityValueDoors;\n            child.material.needsUpdate = true;\n        }\n    });\n\n    loaclElements['drawers'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            // if (child.material.uniforms) {\n            //         child.material.uniforms.blending_color.value = new THREE.Color(hex_color);\n            //         child.material.reflectivity = reflectivityValue;\n            // }\n            child.material.color = new THREE.Color(hex_color);\n            child.material.reflectivity = reflectivityValueDoors;\n            child.material.needsUpdate = true;\n        }\n    });\n    loaclElements['door'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            if(typeof ivy !== 'undefined') {\n                if (window.cstm.item.shelf_type === 1 && parseInt(ivy.material) === 0) {\n                    child.material.map = loaclElements['doors_open_white'];\n                } else {\n                    child.material.map = loaclElements['doors_open'];\n                }\n            }\n            child.material.color = new THREE.Color(hex_color);\n            child.material.reflectivity = reflectivityValueDoors;\n            child.material.needsUpdate = true;\n        }\n    });\n\n    loaclElements['handle_short_left'].traverse( function ( child ) {\n        if ( child instanceof THREE.Mesh ) {\n            child.material.color = new THREE.Color(hex_handler_color);\n\n        }\n    });\n\n    // console.log('ile zmienic, ', magicalElements['doors'].length, magicalElements['handlers'].length );\n    for (let i=0; i < magicalElements['doors'].length;i++){\n        // build3d.magical_materials_for_rows['doors'][i].uniforms.blending_color.value = new THREE.Color(hex_color);\n        magicalElements['handlers'][i].uniforms.blending_color.value = new THREE.Color(hex_handler_color);\n        magicalElements['handlers'][i].needsUpdate = true;\n    }\n    for (let i=0; i < magicalElements['handlers'].length;i++){\n        // build3d.magical_materials_for_rows['doors'][i].uniforms.blending_color.value = new THREE.Color(hex_color);\n        magicalElements['handlers'][i].uniforms.blending_color.value = new THREE.Color(hex_handler_color);\n        magicalElements['handlers'][i].needsUpdate = true;\n    }\n  }\n\n  createShadows(index, oldPoints, newPoints, type) {\n    let scene = this.scene;\n    if (window.location.href.indexOf(\"noshadow\") > -1) {\n      return;\n    }\n    let dif;\n    if (typeof oldPoints !== \"undefined\") {\n      dif = this.compare_shadows(oldPoints, newPoints);\n    } else {\n      dif = {\n        newitem: true,\n        scaled: true,\n        only_moved: true\n      };\n    }\n\n    if (dif.same === true) {\n      return true;\n    }\n    let shadow;\n\n\n    let isShadowIn = (\n      (this.context !== 'grid' && !this.isMobile) ||\n      (window.cstm_i18n.gridView === true) ||\n      (window.cstm_i18n.configuratorView && (window.is_mobile_loaded || window.is_tablet_loaded))\n\n    );\n\n    //let isShadowIn = true;\n    let shadowIn;\n    let shadowBox;\n\n    if (dif.newitem) {\n      switch (type) {\n        case 0:\n          shadow = elements[\"shadow\"].clone();\n          shadowBox = boundingBox.setFromObject(elements[\"shadow\"]);\n          if(isShadowIn) shadowIn = elements['wall_compartment_shadow'].clone();\n          break;\n\n        case 1:\n          shadow = elements[\"shadow-left\"].clone();\n          shadowBox = boundingBox.setFromObject(elements[\"shadow-left\"]);\n          break;\n\n        case 2:\n          shadow = elements[\"shadow-right\"].clone();\n          shadow.name = \"shadow-right\";\n          shadowBox = boundingBox.setFromObject(elements[\"shadow-right\"]);\n          break;\n\n      }\n    } else {\n      switch (type) {\n        case 0:\n          shadow = this.walls.shadows[0][index];\n          shadowBox = boundingBox.setFromObject(elements[\"shadow\"]);\n          break;\n\n        case 1:\n          shadow = this.walls.shadows[1][index];\n          shadowBox = boundingBox.setFromObject(elements[\"shadow-left\"]);\n          break;\n\n        case 2:\n          shadow = this.walls.shadows[2][index];\n          shadowBox = boundingBox.setFromObject(elements[\"shadow-right\"]);\n          break;\n\n        case 3:\n          shadow = elements[\"support-left\"].clone();\n          shadowBox = boundingBox.setFromObject(elements[\"shadow\"]);\n          break;\n\n        case 4:\n          shadow = elements[\"support-right\"].clone();\n          shadowBox = boundingBox.setFromObject(elements[\"shadow\"]);\n          break;\n\n      }\n    }\n\n    if (dif.scaled) {\n\n      shadow.rotation.x = - Math.PI/2;\n      // scale for proper types of shadows\n      shadow.scale.setX((newPoints.size.x + 3) / 100);\n      shadow.scale.setZ((newPoints.size.y + 3) / 100);\n\n      //ShadowIN\n      if(typeof shadowIn != \"undefined\" && isShadowIn === true) {\n          shadowIn.scale.setX((newPoints.size.x + 24) / 100);\n          shadowIn.scale.setY((newPoints.size.y + 24) / 100);\n          shadowIn.position.setX(newPoints.position.x + 7);\n          shadowIn.position.setY(newPoints.position.y + newPoints.size.y / 2 );\n          shadowIn.position.setZ(-5);\n      }\n\n      if (type === 3 || type === 4) {\n        shadow.scale.setZ(newPoints.size.z / shadowBox.size().z);\n        shadow.position.setZ(newPoints.position.z);\n      } else {\n        shadow.scale.setY(this.depth / shadowBox.size().z);\n      }\n      shadow.position.setX(newPoints.position.x);\n      shadow.position.setY(newPoints.position.y);\n    }\n\n    if (dif.moved) {\n      shadow.position.setX(newPoints.position.x);\n      shadow.position.setY(newPoints.position.y);\n\n\n    }\n\n    shadow.renderOrder = 10000000;\n\n    if (dif.newitem) {\n\n      scene.add(shadow);\n      if(isShadowIn && shadowIn) scene.add(shadowIn);\n      switch (type) {\n        case 0:\n          this.walls.shadows[0].push(shadow);\n          if(isShadowIn) this.walls.shadows[4].push(shadowIn);\n          break;\n        case 1:\n          this.walls.shadows[1].push(shadow);\n          break;\n        case 2:\n          this.walls.shadows[2].push(shadow);\n          break;\n        case 3:\n          shadow = elements[\"support-left\"].clone();\n          shadowBox = boundingBox.setFromObject(elements[\"shadow\"]);\n          break;\n        case 4:\n          shadow = elements[\"support-right\"].clone();\n          shadowBox = boundingBox.setFromObject(elements[\"shadow\"]);\n          break;\n      }\n    }\n\n    if(isShadowIn) {\n   //   this.wallShadowsObjects.push(shadowIn);\n    }\n\n\n  }\n\n  getIndents(shelfSize) {\n\n /*\n  return [\n    ..._points.additional_elements.shadow_left,\n    ..._points.additional_elements.shadow_right,\n   // ..._points.backs,\n  ].map((v) => [ v.x1, v.y1,  v.x2,  v.y2, 0]);\n*/\n\n    let delta = { x: -25, y: 150 - 25 };\n    let shelfPosition = { x: -1, y: -1 };\n\n    return [\n      ..._points.additional_elements.shadow_left,\n      ..._points.additional_elements.shadow_right\n    ]\n    .map((shdw) => ({\n      x: shdw.x1,\n      y: shdw.y1,\n      z: shdw.x2,\n      w: shdw.y2\n    }))\n    .map((indent) => ({\n      w: indent.z - indent.x,\n      h: indent.w - indent.y,\n      x: indent.z,\n      y: indent.w\n    }))\n    .map((size,n) => {\n\n      let half = 0.5,\n          fixWarp = { y: -16, x: 10 };\n\n      let left = -size.x + size.w * half;\n      let top = shelfSize[1] - Math.abs(size.y - size.h * half - fixWarp.y) - 30;\n\n      return [ left, top,\n        size.w * half + fixWarp.x,\n        size.h * half - fixWarp.y\n      ];\n\n    });\n  }\n\n  //create the casted shadows which consists of 3 parts: sides and center, positioned flat on the ground below the shelf\n  createCastShadows(width) {\n    let scene = this.scene;\n    if (width === undefined) {\n      width = this.width;\n    }\n    let shadowCenter, shadowLeft, shadowRight;\n    shadowCenter = elements[\"cast-shadow-center\"].clone();\n    shadowRight = elements[\"cast-shadow-right\"].clone();\n    shadowLeft = elements[\"cast-shadow-left\"].clone();\n\n    for (let i = 0; i < this.walls.castShadows.length; i++) {\n      scene.remove(this.walls.castShadows[i]);\n    }\n\n    this.walls.castShadows.length = 0;\n    /*\n        if (!shadowCastEnabled) {\n            return;\n        }*/\n    let positionBottom = -22;\n    let positionZ = this.depth / 2;\n    let shadowBox = boundingBox.setFromObject(elements[\"cast-shadow-center\"]);\n    let scaleZ = this.depth * 2 / shadowBox.size().z;\n    shadowCenter.scale.setX(width / shadowBox.size().x);\n    shadowCenter.scale.setZ(scaleZ);\n    shadowCenter.position.setY(positionBottom);\n    shadowCenter.position.setZ(positionZ);\n    this.walls.castShadows.push(shadowCenter);\n\n    shadowCenter.renderOrder = 10000;\n\n    shadowBox = boundingBox.setFromObject(elements[\"cast-shadow-right\"]);\n    let scaleX = this.depth / 2 / shadowBox.size().x;\n\n    shadowRight.scale.setX(scaleX);\n    shadowRight.scale.setZ(scaleZ);\n    shadowRight.position.setX(-width / 2 + this.depth / 2 / 2);\n    shadowRight.position.setY(positionBottom);\n    shadowRight.position.setZ(positionZ);\n    this.walls.castShadows.push(shadowRight);\n\n    shadowLeft.scale.setX(scaleX);\n    shadowLeft.scale.setZ(scaleZ);\n    shadowLeft.position.setX(width / 2 - this.depth / 2 / 2);\n    shadowLeft.position.setY(positionBottom);\n    shadowLeft.position.setZ(positionZ);\n    this.walls.castShadows.push(shadowLeft);\n\n    this.scene.add(shadowCenter);\n    this.scene.add(shadowLeft);\n    this.scene.add(shadowRight);\n  }\n\n  // compare dna points\n  compare_dnas(old_one, new_one) {\n    let result = {};\n\n    //check if it is the same\n    if (\n      old_one.bottom.x === new_one.bottom.x &&\n      old_one.bottom.y === new_one.bottom.y &&\n      old_one.top.x === new_one.top.x &&\n      old_one.top.y === new_one.top.y\n    ) {\n      result.same = true;\n      return result;\n    }\n    // check if it was only moved on X\n    if (\n      Math.abs(old_one.bottom.x - old_one.top.x) ===\n        Math.abs(new_one.bottom.x - new_one.top.x) &&\n      Math.abs(old_one.bottom.y - old_one.top.y) ===\n        Math.abs(new_one.bottom.y - new_one.top.y)\n    ) {\n      result.only_moved = true;\n    }\n    // check if it was scaled\n    if (\n      Math.abs(old_one.top.y - old_one.bottom.y) !==\n        Math.abs(new_one.top.y - new_one.bottom.y) ||\n      Math.abs(old_one.top.x - old_one.bottom.x) !==\n        Math.abs(new_one.top.x - new_one.bottom.x)\n    ) {\n      result.scaled = true;\n    }\n\n    return result;\n  }\n\n  // same as earlier, only the points are bit easier to compare\n  compare_shadows(old_one, new_one) {\n    let result = {};\n\n    if (\n      old_one.position.x === new_one.position.x &&\n      old_one.position.y === new_one.position.y &&\n      old_one.size.x === new_one.size.x &&\n      old_one.size.y === new_one.size.y\n    ) {\n      result.same = true;\n      return result;\n    }\n    if (\n      old_one.position.x !== new_one.position.x ||\n      old_one.position.y !== new_one.position.y\n    ) {\n      result.moved = true;\n    }\n    if (\n      old_one.size.x !== new_one.size.x ||\n      old_one.size.y !== new_one.size.y\n    ) {\n      result.scaled = true;\n    }\n\n    return result;\n  }\n\n  get_only_points(ivy, pattern, motion, width, rows, rowHeight, rowStyles) {\n    let points = this.dnaTools.get_elements(\n      ivy.patterns[pattern].json,\n      motion,\n      width * 10,\n      rows,\n      rowHeight,\n      rowStyles,\n      320,\n      9,\n      125,\n      true,\n      [0,0,0,0]\n    );\n    return points;\n  }\n\n  rebuildWalls(initial, snapping = false, skip_dna = false, ivy) {\n    this.depth = ivy.depth;\n\n    if (window.loadPresetFlag) {\n      return;\n    }\n    let scene = this.scene;\n    if (sizePlane.length > 0) {\n      for (let i = 0; i < sizePlane.length; i++) {\n        scene.remove(sizePlane[i]);\n      }\n      sizePlane = [];\n    }\n\n    let initialRun = initial || false;\n    if (initialRun && dnaStartWasSent == false) {\n      if (typeof tracking != \"undefined\") {\n        tracking.trackDnaStart(ivy.actual_dna_name);\n        dnaStartWasSent = true;\n      }\n    } else {\n      //this.send_customization();\n    }\n    let generateCounter = getUrlParameter(\"generateWalls\") || 1;\n    let points;\n    if (skip_dna === true) {\n      this.walls.shadows.forEach(shadow_list => {\n        for (let i = 0; i < shadow_list.length; i++) {\n          this.scene.remove(shadow_list[i]);\n        }\n        shadow_list.length = 0;\n      });\n      let typical_elements = [\"verticals\", \"horizontals\", \"supports\", \"top-bottom\"];\n\n      typical_elements.forEach(element_type => {\n        // check if there are more elements than on a previous run, if so remove them from scene\n        for (let j = 0; j < this.walls[element_type].length; j++) {\n          this.scene.remove(this.walls[element_type][j]);\n        }\n        this.walls[element_type].length = 0;\n      });\n      points = {\n        doors: [],\n        horizontals: ivy.data.horizontals,\n        legs: [],\n        verticals: ivy.data.verticals,\n        supports: ivy.data.supports,\n        additional_elements: {\n          shadow_left: [],\n          shadow_middle: [],\n          shadow_right: [],\n          shadow_side: []\n        }\n      };\n      let shelf_rows = this.dnaTools.get_row_coor(\n        ivy.rows,\n        ivy.constants.rowHeight\n      );\n      let simplified_data = this.dnaTools.generate_elements_simplified_data(\n        points,\n        shelf_rows,\n        9\n      );\n      let openings = this.dnaTools.get_shadows_openings(\n        simplified_data,\n        ivy.width,\n        9\n      );\n      points[\"additional_elements\"] = this.dnaTools.get_shadows_geometry(\n        openings,\n        shelf_rows,\n        ivy.width,\n        9,\n        ivy.depth\n      );\n      points[\"legs\"] = this.dnaTools.generate_legs(\n        [[-ivy.width / 2, ivy.width / 2], simplified_data[1][1]],\n        shelf_rows,\n        ivy.depth\n      );\n    } else {\n      for (let i = 0; i < generateCounter; i++) {\n        // old chaos\n        if (ivy.patterns[ivy.pattern].json === undefined) {\n          let own_walls = ivy.patterns[4].generateWalls(\n            ivy.Property1,\n            ivy.width,\n            ivy.rows,\n            ivy.constants.rowHeight,\n            ivy.depth,\n            ivy.minX,\n            ivy.maxX\n          );\n          points = {\n            doors: [],\n            horizontals: [],\n            legs: [],\n            verticals: [],\n            supports: [],\n            additional_elements: {\n              shadow_left: [],\n              shadow_middle: [],\n              shadow_right: [],\n              shadow_side: []\n            }\n          };\n          for (let i = 0; i < own_walls[\"ptHorizontalsAll\"].length; i += 2) {\n            points[\"horizontals\"].push({\n              x1: own_walls[\"ptHorizontalsAll\"][i][\"x\"],\n              y1: own_walls[\"ptHorizontalsAll\"][i][\"y\"],\n              z1: own_walls[\"ptHorizontalsAll\"][i][\"z\"],\n              x2: own_walls[\"ptHorizontalsAll\"][i + 1][\"x\"],\n              y2: own_walls[\"ptHorizontalsAll\"][i + 1][\"y\"],\n              z2: own_walls[\"ptHorizontalsAll\"][i + 1][\"z\"]\n            });\n          }\n          for (let i = 0; i < own_walls[\"ptVerticalsAll\"].length; i += 2) {\n            points[\"verticals\"].push({\n              x2: own_walls[\"ptVerticalsAll\"][i][\"x\"],\n              y2: own_walls[\"ptVerticalsAll\"][i][\"y\"],\n              z2: own_walls[\"ptVerticalsAll\"][i][\"z\"],\n              x1: own_walls[\"ptVerticalsAll\"][i + 1][\"x\"],\n              y1: own_walls[\"ptVerticalsAll\"][i + 1][\"y\"],\n              z1: own_walls[\"ptVerticalsAll\"][i + 1][\"z\"]\n            });\n          }\n          for (let i = 0; i < own_walls[\"ptSupportsLeft\"].length; i += 2) {\n            points[\"supports\"].push({\n              x2: own_walls[\"ptSupportsLeft\"][i][\"x\"],\n              y2: own_walls[\"ptSupportsLeft\"][i][\"y\"],\n              z2: own_walls[\"ptSupportsLeft\"][i][\"z\"],\n              x1: own_walls[\"ptSupportsLeft\"][i + 1][\"x\"],\n              y1: own_walls[\"ptSupportsLeft\"][i + 1][\"y\"],\n              z1: own_walls[\"ptSupportsLeft\"][i + 1][\"z\"]\n            });\n          }\n          for (let i = 0; i < own_walls[\"ptSupportsRight\"].length; i += 2) {\n            points[\"supports\"].push({\n              x2: own_walls[\"ptSupportsRight\"][i][\"x\"],\n              y2: own_walls[\"ptSupportsRight\"][i][\"y\"],\n              z2: own_walls[\"ptSupportsRight\"][i][\"z\"],\n              x1: own_walls[\"ptSupportsRight\"][i + 1][\"x\"],\n              y1: own_walls[\"ptSupportsRight\"][i + 1][\"y\"],\n              z1: own_walls[\"ptSupportsRight\"][i + 1][\"z\"]\n            });\n          }\n          let shelf_rows = this.dnaTools.get_row_coor(\n            ivy.rows,\n            ivy.constants.rowHeight\n          );\n          let simplified_data = this.dnaTools.generate_elements_simplified_data(\n            points,\n            shelf_rows,\n            9\n          );\n          let openings = this.dnaTools.get_shadows_openings(\n            simplified_data,\n            ivy.width,\n            9\n          );\n          points[\"additional_elements\"] = this.dnaTools.get_shadows_geometry(\n            openings,\n            shelf_rows,\n            ivy.width,\n            9,\n            ivy.depth\n          );\n          points[\"legs\"] = this.dnaTools.generate_legs(\n            [[-this.width / 2, this.width / 2], simplified_data[1][1]],\n            shelf_rows,\n            ivy.depth\n          );\n          ivy.actual_dna_name = \"old chaos\";\n        } else {\n          if (this.row_styles_presets !== -1) {\n            ivy.constants.rowStyles = this.dnaTools.get_row_styles_list(\n              this.dnaTools.get_row_styles(ivy.patterns[ivy.pattern].json),\n              row_styles_presets,\n              ivy.rows\n            );\n          }\n          points = preparePoints(this.dnaTools,\n            ivy.patterns[ivy.pattern].json,\n            ivy.Property1,\n            ivy.width,\n            ivy.rows,\n            ivy.constants.rowHeight,\n            ivy.constants.rowStyles,\n            ivy.depth,\n            9,\n            125,\n            snapping || initial,\n            [0,0,0,0], true, -1, ivy.backpanel_styles, ivy.shelf_type\n          );\n          points.topBottomWalls = this.getTopBottomWallPosition(points);\n          points.leftRightWalls = this.getLeftRightWallPosition(points);\n          points.additionalHorizontalElements = this.getAdditionalHorizontalPanelPosition(points);\n\n          _points = points;\n\n          if (ivy.patterns[ivy.pattern].json[\"dna_name\"] !== undefined) {\n            ivy.actual_dna_name =\n              ivy.patterns[ivy.pattern].json[\"dna_name\"] || \"\";\n          }\n        }\n      }\n    }\n    let rebuildCounter = getUrlParameter(\"rebuild\") || 1;\n    let shadow_translator = {\n      shadow_middle: 0,\n      shadow_left: 1,\n      shadow_right: 2,\n      shadow_side: 0\n    };\n\n    /*if (isMobile) { doorFlagOn = true }*/\n    for (let j = 0; j < rebuildCounter; j++) {\n      let typical_elements = [\"verticals\", \"horizontals\", \"supports\", \"backs\", \"topBottomWalls\", \"leftRightWalls\"];\n      typical_elements.forEach(element_type => {\n        // check if there are more elements than on a previous run, if so remove them from scene\n        if (this.points[element_type] === undefined) {\n          this.points[element_type] = [];\n        }\n\n        if (this.points[element_type] && this.points[element_type].length > 0 && points[element_type].length < this.points[element_type].length) {\n          let dif = this.points[element_type].length - points[element_type].length;\n\n          if (this.walls[element_type]) {\n            for ( let j = this.walls[element_type].length - dif; j < this.walls[element_type].length;  j++) {\n              scene.remove(this.walls[element_type][j]);\n            }\n            this.walls[element_type].length = this.walls[element_type].length - dif < 0 ? 0 : this.walls[element_type].length - dif;\n          }\n        }\n\n        //arrays for holding points\n        let newPoints = [];\n        let oldPoints = [];\n        // build points\n\n        for (let i = 0; i < points[element_type].length; i++) {\n          //if there are points push them for comparison\n          if (\n            typeof this.points[element_type][i] !== \"undefined\" &&\n            !initialRun\n          ) {\n            newPoints.push({\n              bottom: new THREE.Vector3(\n                points[element_type][i][\"x1\"],\n                points[element_type][i][\"y1\"],\n                points[element_type][i][\"z1\"]\n              ),\n              top: new THREE.Vector3(\n                points[element_type][i][\"x2\"],\n                points[element_type][i][\"y2\"],\n                points[element_type][i][\"z2\"]\n              )\n            });\n            oldPoints.push({\n              bottom: new THREE.Vector3(\n                this.points[element_type][i][\"x1\"],\n                this.points[element_type][i][\"y1\"],\n                this.points[element_type][i][\"z1\"]\n              ),\n              top: new THREE.Vector3(\n                this.points[element_type][i][\"x2\"],\n                this.points[element_type][i][\"y2\"],\n                this.points[element_type][i][\"z2\"]\n              )\n            });\n          } else {\n            // if there is more points add new ones\n            newPoints.push({\n              bottom: new THREE.Vector3(\n                points[element_type][i][\"x1\"],\n                points[element_type][i][\"y1\"],\n                points[element_type][i][\"z1\"]\n              ),\n              top: new THREE.Vector3(\n                points[element_type][i][\"x2\"],\n                points[element_type][i][\"y2\"],\n                points[element_type][i][\"z2\"]\n              )\n            });\n          }\n        }\n        //creating points for top and bottom;\n\n        // create verticals from these points aray\n        for (let i = 0; i < newPoints.length; i++) {\n          if (element_type === \"verticals\") {\n            this.createSingleVertical(i, oldPoints[i], newPoints[i]);\n          } else if (element_type === \"horizontals\") {\n            this.createHorizontal(i, oldPoints[i], newPoints[i]);\n          } else if (element_type === \"supports\") {\n            let supportType;\n            if((points[element_type][i].x2 + points[element_type][i].x1) < 0 ){\n              supportType = Math.abs(points[element_type][i].x2) > Math.abs(points[element_type][i].x1) ? 'left' : 'right';\n            } else {\n              supportType = Math.abs(points[element_type][i].x2) < Math.abs(points[element_type][i].x1) ? 'left' : 'right';\n            }\n            this.createSingleSupport(i, oldPoints[i], newPoints[i], supportType);\n          } else if (element_type === \"backs\") {\n            this.createSingleBack(i, oldPoints[i], newPoints[i]);\n          } else if (element_type === \"topBottomWalls\") {\n            this.createHorizontalPanels(i, oldPoints[i], newPoints[i]);\n          } else if (element_type === \"leftRightWalls\") {\n            this.createVerticalPanels(i, oldPoints[i], newPoints[i], ivy.pattern );\n          }\n        }\n\n\n      });\n\n      // create shadows\n\n\n\n      let shadows = points.additional_elements;\n      let getSizeFromPoint = function(item) {\n        return new THREE.Vector3(\n          Math.abs(item.x1 - item.x2),\n          Math.abs(item.y1 - item.y2),\n          Math.abs(item.z1 - item.z2)\n        );\n      };\n      let getPositionFromPoint = function(item) {\n        return new THREE.Vector3(\n          (item.x1 + item.x2) / 2,\n          item.y1 - 1.5,\n          (item.z1 + item.z2) / 2\n        );\n      };\n      this.walls.shadows.forEach(shadow_list => {\n        for (let i = 0; i < shadow_list.length; i++) {\n          scene.remove(shadow_list[i]);\n        }\n        shadow_list.length = 0;\n      });\n      for (let shadow_type in shadows) {\n        if (shadow_type === \"styles\") {\n          continue; // as its style for doors, not shadow\n        }\n        let shadow_list = shadows[shadow_type];\n\n        let newPoints = [];\n        for (let i = 0; i < shadow_list.length; i++) {\n          newPoints.push({\n            size: getSizeFromPoint(shadow_list[i]),\n            position: getPositionFromPoint(shadow_list[i])\n          });\n        }\n        for (let i = 0; i < newPoints.length; i++) {\n          this.createShadows(\n            i,\n            undefined,\n            newPoints[i],\n            shadow_translator[shadow_type]\n          );\n        }\n      }\n\n\n      for (let i = 0; i < this.walls.additionalHorizontalElements.length; i++) {\n        scene.remove(this.walls.additionalHorizontalElements[i]);\n      }\n      this.walls.additionalHorizontalElements.length = 0;\n      // create double openings\n      for (let i = 0; i < points.additionalHorizontalElements.length; i++) {\n        this.createAdditionalHorizontalPanels(points.additionalHorizontalElements[i]);\n      }\n\n      // remove legs because there could be less\n      for (let i = 0; i < this.walls.legs.length; i++) {\n        scene.remove(this.walls.legs[i]);\n      }\n      this.walls.legs.length = 0;\n      // create new legs\n      for (let i = 0; i < points.legs.length; i++) {\n        this.createLegs(points.legs[i]);\n      }\n\n\n\n      this.door_lists = [[], [], [], [], [], [], [], [], [], [], [], []];\n      this.door_lists_elements = [\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        []\n      ];\n\n      this.drawers_lists = [[], [], [], [], [], [], [], [], [], [], [], []];\n\n      for (let i = 0; i < this.walls.doors.length; i++) {\n        scene.remove(this.walls.doors[i]);\n      }\n\n      for (let i = 0; i < this.walls.door_groups.length; i++) {\n        scene.remove(this.walls.door_groups[i]);\n      }\n      this.walls.door_groups.length = 0;\n      this.walls.doors.length = 0;\n\n      for (let i = 0; i < points.doors.length; i++) {\n        this.createDoor(\n          getPositionFromPoint(points.doors[i]),\n          getSizeFromPoint(points.doors[i]),\n          points.doors[i].type,\n          points.doors[i].flip,\n          ivy.constants.rowHeight,\n          this.shelf_type\n        );\n      }\n      // drawers\n\n      for (let i = 0; i < this.walls.drawers.length; i++) {\n        scene.remove(this.walls.drawers[i]);\n      }\n      this.walls.drawers.length = 0;\n      // create new legs\n      for (let i = 0; i < points.drawers.length; i++) {\n        this.createDrawers(\n          getPositionFromPoint(points.drawers[i]),\n          getSizeFromPoint(points.drawers[i]),\n          ivy.constants.rowHeight,\n          this.shelf_type\n        );\n      }\n\n      // handlers\n      for (let i = 0; i < this.handlers.length; i++) {\n        scene.remove(this.handlers[i]);\n      }\n      for (let i = 0; i < ivy.rows; i++) {\n        let height = 0;\n        for (let j = 0; j < i; j++) {\n          height += ivy.constants.rowHeight[j];\n        }\n        height += ivy.constants.rowHeight[i] / 2;\n        let obj = new THREE.Object3D();\n\n        this.handlers.push(obj);\n        this.handlers[i].position.setX(ivy.width / 2 + 40);\n        this.handlers[i].position.setY(height);\n        this.handlers[i].position.setZ(250);\n        this.handlers[i].matrixWorldNeedsUpdate = true;\n        scene.add(this.handlers[i]);\n      }\n    }\n\n    let calculatedCapacity = getTotalCapacity(\n      points,\n      ivy.rows,\n      ivy.pattern,\n      ivy.width,\n      ivy.shelf_type\n    );\n\n    this.capacity = calculatedCapacity[0]; // Shelf total load\n    this.compartmentCapacity = [calculatedCapacity[1], calculatedCapacity[2]]; // [ Capacity Min, Capacity Max ]\n\n    this.points = points;\n    this.row_styles_availability = points.additional_elements.styles;\n    this.depth_previous = this.depth;\n\n    //generateSVG(this.width, this.getHeight(), this.points.horizontals, this.points.verticals, this.points.supports, !snapping);\n    //console.log('================= GenerateCanvas Call -- fn RebuildWalls=============================');\n    //window.dConf.fn.timerStop();\n\n    if (typeof generateCanvas != \"undefined\") {\n      generateCanvas(\n        ivy.width,\n        ivy.getHeight(),\n        this.points.horizontals,\n        this.points.verticals,\n        this.points.supports\n      );\n    }\n\n    //window.dConf.fn.timerStart();\n\n    for (let i = 0; i < this.magical_materials_for_rows[\"doors\"].length; i++) {\n      // this.magical_materials_for_rows[\"doors\"][\n      //   i\n      // ].uniforms.blending_ratio.value = 0;\n      // this.magical_materials_for_rows[\"handlers\"][\n      //   i\n      // ].uniforms.blending_ratio.value = 0;\n    }\n    //console.log(\"ok, here pubsub\", snapping, initial, snapping || initial, PubSub);\n\n    if (snapping || initial) {\n      //TODO: assign price as pubsub\n      PubSub.publish(\"shelfChangedSnapped\");\n    } else {\n      PubSub.publishSync(\"shelfChanged\");\n    }\n\n    // if(window.sceneOn && !window.sliderMoving){\n    //     this.createScene(this.width, this.getHeight());\n    //     this.createItems();\n    // } else {\n    //     this.removeScene();\n    //     this.removeItems(items);\n    //     items = [];\n    // }\n  }\n  toggleShadowCast() {\n    shadowCastEnabled = !shadowCastEnabled;\n    this.createCastShadows();\n  }\n}\n\nfunction getUrlParameter(sParam) {\n  let sPageURL = decodeURIComponent(window.location.search.substring(1)),\n    sURLVariables = sPageURL.split(\"&\"),\n    sParameterName,\n    i;\n\n  for (i = 0; i < sURLVariables.length; i++) {\n    sParameterName = sURLVariables[i].split(\"=\");\n\n    if (sParameterName[0] === sParam) {\n      return sParameterName[1] === undefined ? true : sParameterName[1];\n    }\n  }\n}\n\nfunction getRowByY(value_y, rowsList) {\n  let tmp = 0;\n  let row = 0;\n  while (!(tmp < value_y && tmp + rowsList[row] > value_y)) {\n    tmp += rowsList[row];\n    row++;\n    // security check\n    if (row >= 15) {\n      return -1;\n    }\n  }\n  return row;\n}\n\nfunction getTotalCapacity(shelfGeometry, rowAmount, dnaID, width, shelfType) {\n  let capacityTable = {\n    \"300\": 50,\n    \"400\": 40,\n    \"500\": 35,\n    \"600\": 30,\n    \"700\": 25,\n    \"800\": 20,\n    \"900\": 15,\n    \"1000\": 10\n  };\n\n  let setTableType02 = {\n    // Slant\n\n    \"0\": {\n      default: 8,\n      subset_len: 0.2,\n      doors: 0.7,\n      param_1: 0.85,\n      param_2: 0.95,\n      param_3: 0.88,\n      p_min: 5,\n      p_max: 60,\n      o_min: 10,\n      o_max: 40\n    },\n    // Grid\n\n    \"1\": {\n      default: 10,\n      subset_len: 0.15,\n      doors: 0.95,\n      param_1: 1,\n      param_2: 0.98,\n      param_3: 0.87,\n      p_min: 20,\n      p_max: 80,\n      o_min: 20,\n      o_max: 60\n    },\n\n    // Pattern\n\n    \"2\": {\n      default: 6,\n      subset_len: 0.25,\n      doors: 0.8,\n      param_1: 1,\n      param_2: 0.95,\n      param_3: 0.88,\n      p_min: 10,\n      p_max: 60,\n      o_min: 20,\n      o_max: 45\n    },\n\n\n    // Gradient\n\n    \"3\": {\n      default: 7,\n      subset_len: 0.3,\n      doors: 0.9,\n      param_1: 1.2,\n      param_2: 0.96,\n      param_3: 0.88,\n      p_min: 15,\n      p_max: 65,\n      o_min: 20,\n      o_max: 50\n    },\n\n\n\n  };\n\n  let setTable = {\n    // Slant\n\n    \"0\": {\n      default: 8,\n      subset_len: 0.2,\n      doors: 0.7,\n      param_1: 0.85,\n      param_2: 0.95,\n      param_3: 0.88,\n      p_min: 5,\n      p_max: 60,\n      o_min: 10,\n      o_max: 40\n    },\n\n    // Gradient\n\n    \"1\": {\n      default: 7,\n      subset_len: 0.3,\n      doors: 0.9,\n      param_1: 1.2,\n      param_2: 0.96,\n      param_3: 0.88,\n      p_min: 15,\n      p_max: 65,\n      o_min: 20,\n      o_max: 50\n    },\n\n    // Pattern\n\n    \"2\": {\n      default: 6,\n      subset_len: 0.25,\n      doors: 0.8,\n      param_1: 1,\n      param_2: 0.95,\n      param_3: 0.88,\n      p_min: 10,\n      p_max: 60,\n      o_min: 20,\n      o_max: 45\n    },\n\n    // Grid\n\n    \"3\": {\n      default: 10,\n      subset_len: 0.15,\n      doors: 0.95,\n      param_1: 1,\n      param_2: 0.98,\n      param_3: 0.87,\n      p_min: 20,\n      p_max: 80,\n      o_min: 20,\n      o_max: 60\n    }\n  };\n\n  let parameters = setTable[\"0\"];\n\n  if (shelfType === 1) {\n    if (dnaID in setTableType02) {\n      parameters = setTableType02[dnaID];\n    }\n  } else {\n    if (dnaID in setTable) {\n      parameters = setTable[dnaID];\n    }\n  }\n\n  if (shelfGeometry.additional_elements.shadow_middle.length === 0) {\n    //console.log( 'shelfGeometry.additional_elements.shadow_middle is an empty array. getTotalCapacity() Skipped.' );\n    return false;\n  }\n\n  let allSorted = shelfGeometry.additional_elements.shadow_middle\n    .map(el => {\n      return el.x2 - el.x1;\n    })\n    .sort((first, second) => {\n      return second - first;\n    });\n  //console.log('allSorted', allSorted);\n  let lengthAll = allSorted.length;\n  //console.log('lengthAll', lengthAll);\n  let shelfAmount =\n    parseInt(Math.floor(lengthAll * parameters.subset_len), 10) || 1;\n  let allSum = allSorted\n    .slice(0, shelfAmount)\n    .reduce((accumulator, currentValue) => {\n      return accumulator + currentValue;\n    });\n  //console.log('allSum',allSum);\n  let slicedCount =\n    shelfAmount <= allSorted.length ? shelfAmount : allSorted.length;\n  let sumAverage = allSum / slicedCount;\n\n  function getCapacity(k) {\n    let capacity = parameters.default;\n\n    for (let key in capacityTable) {\n      if (parseInt(key) >= k) {\n        capacity = capacityTable[key];\n        break;\n      }\n    }\n    return capacity;\n  }\n\n  let doors = shelfGeometry.doors || [];\n  let drawers = shelfGeometry.drawers || [];\n  let doorsAndDrawers = doors.concat(drawers);\n\n  let adjustment = doorsAndDrawers.length > 0 ? parameters.doors : 1;\n  adjustment *= Math.pow(parameters.param_1 * parameters.param_2, rowAmount);\n\n  //console.log('allSorted', allSorted);\n\n  let openings = [\n    roundTo(getCapacity(allSorted[0]) * adjustment, 5),\n    roundTo(getCapacity(allSorted[allSorted.length - 1] * adjustment), 5)\n  ];\n\n  //console.log('openings', openings);\n\n  // Check for Slant and Grid DNA Cases.\n  function checkIfOpeningsAreEqual(array) {\n    // For even openings, JS sometimes calculates almost identical values, but not entirely identical ( e.g. 379mm, 381mm ).\n    const maxDifferenceBetweenOpenings = 2;\n    for (let i = 0; i < array.length; i++) {\n      if (Math.abs(array[0] - array[i]) > maxDifferenceBetweenOpenings) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  let capMin = checkIfOpeningsAreEqual(allSorted)\n    ? null\n    : Math.max(parameters.o_min, Math.min.apply(Math, openings));\n  //console.log('capMin', capMin);\n  let capMax = Math.min(parameters.o_max, Math.max.apply(Math, openings));\n  //console.log('capMax', capMax);\n\n  let parameterWidth = rowAmount * width / 1000; //Przejscie na metro rzedy\n  let total =\n    getCapacity(sumAverage) * adjustment * lengthAll * parameters[\"param_3\"];\n\n  let totalMax = Math.max(\n    Math.min(parameters.p_max * parameterWidth, total),\n    parameters.p_min * parameterWidth,\n    lengthAll * parameters.o_min\n  );\n\n  function roundTo(value, multiplicity) {\n    let rounded = parseInt(value / multiplicity, 10) * multiplicity;\n    return rounded;\n  }\n\n  let totalMaxFlat = roundTo(totalMax, 10); // 264.444 -> 260\n\n  if (shelfType == 1) {\n    return [roundTo(totalMaxFlat * 0.66, 10), roundTo(capMin * 0.66,1), roundTo(capMax * 0.66,1)];\n  } else {\n    return [totalMaxFlat, capMin, capMax];\n  }\n\n}\n\nexport { Build3d };\n", "/**\n * A specialized version of `_.filter` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction arrayFilter(array, predicate) {\n  var index = -1,\n      length = array == null ? 0 : array.length,\n      resIndex = 0,\n      result = [];\n\n  while (++index < length) {\n    var value = array[index];\n    if (predicate(value, index, array)) {\n      result[resIndex++] = value;\n    }\n  }\n  return result;\n}\n\nmodule.exports = arrayFilter;\n", "/**\n * The base implementation of `_.property` without support for deep paths.\n *\n * @private\n * @param {string} key The key of the property to get.\n * @returns {Function} Returns the new accessor function.\n */\nfunction baseProperty(key) {\n  return function(object) {\n    return object == null ? undefined : object[key];\n  };\n}\n\nmodule.exports = baseProperty;\n", "var getNative = require('./_getNative');\n\nvar defineProperty = (function() {\n  try {\n    var func = getNative(Object, 'defineProperty');\n    func({}, '', {});\n    return func;\n  } catch (e) {}\n}());\n\nmodule.exports = defineProperty;\n", "/**\n * This method returns the first argument it receives.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {*} value Any value.\n * @returns {*} Returns `value`.\n * @example\n *\n * var object = { 'a': 1 };\n *\n * console.log(_.identity(object) === object);\n * // => true\n */\nfunction identity(value) {\n  return value;\n}\n\nmodule.exports = identity;\n", "var baseIsNative = require('./_baseIsNative'),\n    getValue = require('./_getValue');\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\nmodule.exports = getNative;\n", "var baseIsArguments = require('./_baseIsArguments'),\n    isObjectLike = require('./isObjectLike');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nvar isArguments = baseIsArguments(function() { return arguments; }()) ? baseIsArguments : function(value) {\n  return isObjectLike(value) && hasOwnProperty.call(value, 'callee') &&\n    !propertyIsEnumerable.call(value, 'callee');\n};\n\nmodule.exports = isArguments;\n", "/**\n * Appends the elements of `values` to `array`.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to append.\n * @returns {Array} Returns `array`.\n */\nfunction arrayPush(array, values) {\n  var index = -1,\n      length = values.length,\n      offset = array.length;\n\n  while (++index < length) {\n    array[offset + index] = values[index];\n  }\n  return array;\n}\n\nmodule.exports = arrayPush;\n", "var isFunction = require('./isFunction'),\n    isLength = require('./isLength');\n\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */\nfunction isArrayLike(value) {\n  return value != null && isLength(value.length) && !isFunction(value);\n}\n\nmodule.exports = isArrayLike;\n", "var arrayPush = require('./_arrayPush'),\n    isFlattenable = require('./_isFlattenable');\n\n/**\n * The base implementation of `_.flatten` with support for restricting flattening.\n *\n * @private\n * @param {Array} array The array to flatten.\n * @param {number} depth The maximum recursion depth.\n * @param {boolean} [predicate=isFlattenable] The function invoked per iteration.\n * @param {boolean} [isStrict] Restrict to values that pass `predicate` checks.\n * @param {Array} [result=[]] The initial result value.\n * @returns {Array} Returns the new flattened array.\n */\nfunction baseFlatten(array, depth, predicate, isStrict, result) {\n  var index = -1,\n      length = array.length;\n\n  predicate || (predicate = isFlattenable);\n  result || (result = []);\n\n  while (++index < length) {\n    var value = array[index];\n    if (depth > 0 && predicate(value)) {\n      if (depth > 1) {\n        // Recursively flatten arrays (susceptible to call stack limits).\n        baseFlatten(value, depth - 1, predicate, isStrict, result);\n      } else {\n        arrayPush(result, value);\n      }\n    } else if (!isStrict) {\n      result[result.length] = value;\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseFlatten;\n", "var setPrototypeOf = require(\"./setPrototypeOf\");\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) setPrototypeOf(subClass, superClass);\n}\n\nmodule.exports = _inherits;", "// @@match logic\nrequire('./_fix-re-wks')('match', 1, function (defined, MATCH, $match) {\n  // 21.1.3.11 String.prototype.match(regexp)\n  return [function match(regexp) {\n    'use strict';\n    var O = defined(this);\n    var fn = regexp == undefined ? undefined : regexp[MATCH];\n    return fn !== undefined ? fn.call(regexp, O) : new RegExp(regexp)[MATCH](String(O));\n  }, $match];\n});\n", "/**\n * A specialized version of `_.map` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction arrayMap(array, iteratee) {\n  var index = -1,\n      length = array == null ? 0 : array.length,\n      result = Array(length);\n\n  while (++index < length) {\n    result[index] = iteratee(array[index], index, array);\n  }\n  return result;\n}\n\nmodule.exports = arrayMap;\n", "var baseRest = require('./_baseRest'),\n    unzip = require('./unzip');\n\n/**\n * Creates an array of grouped elements, the first of which contains the\n * first elements of the given arrays, the second of which contains the\n * second elements of the given arrays, and so on.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {...Array} [arrays] The arrays to process.\n * @returns {Array} Returns the new array of grouped elements.\n * @example\n *\n * _.zip(['a', 'b'], [1, 2], [true, false]);\n * // => [['a', 1, true], ['b', 2, false]]\n */\nvar zip = baseRest(unzip);\n\nmodule.exports = zip;\n", "var arrayFilter = require('./_arrayFilter'),\n    arrayMap = require('./_arrayMap'),\n    baseProperty = require('./_baseProperty'),\n    baseTimes = require('./_baseTimes'),\n    isArrayLikeObject = require('./isArrayLikeObject');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * This method is like `_.zip` except that it accepts an array of grouped\n * elements and creates an array regrouping the elements to their pre-zip\n * configuration.\n *\n * @static\n * @memberOf _\n * @since 1.2.0\n * @category Array\n * @param {Array} array The array of grouped elements to process.\n * @returns {Array} Returns the new array of regrouped elements.\n * @example\n *\n * var zipped = _.zip(['a', 'b'], [1, 2], [true, false]);\n * // => [['a', 1, true], ['b', 2, false]]\n *\n * _.unzip(zipped);\n * // => [['a', 'b'], [1, 2], [true, false]]\n */\nfunction unzip(array) {\n  if (!(array && array.length)) {\n    return [];\n  }\n  var length = 0;\n  array = arrayFilter(array, function(group) {\n    if (isArrayLikeObject(group)) {\n      length = nativeMax(group.length, length);\n      return true;\n    }\n  });\n  return baseTimes(length, function(index) {\n    return arrayMap(array, baseProperty(index));\n  });\n}\n\nmodule.exports = unzip;\n", "var freeGlobal = require('./_freeGlobal');\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\nmodule.exports = root;\n"], "sourceRoot": ""}