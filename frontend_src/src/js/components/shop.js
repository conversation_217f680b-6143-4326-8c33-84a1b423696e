/* Shop Related SubViews and API Calls */
const Site = window.Site || {};

Site.Shop = (function($) {
    const promoTrack = (eventLabel, eventAction) => {
        window.dataLayer.push({
            event: 'userInteraction',
            eventType: 'NOEEC',
            eventCategory: 'promo-code',
            eventAction,
            eventLabel,
            eventValue: undefined,
            eventNonInteraction: false,
        });
    };
    const Subviews = {
        // do to -- subview prototype?
        Cart: (function() {
            const endPoint = `/${cstm_i18n.language}/cart/`;
            let xhr = null;
            const el = '#view-cart';
            const reload = function() {
                if (xhr) {
                    xhr.abort();
                }

                xhr = $.ajax({
                    url: endPoint,
                    global: false,
                    type: 'GET',
                    dataType: 'html',
                    async: true,
                })
                    .done(data => {
                        $(el).html($(data).find(el).html());
                        bind();
                    })
                    .fail(data => {
                        Site.Notify.onError(data);
                    });
            };

            return {
                reload,
            };
        }()),

        Totals: (function() {
            const endPoint = `/${cstm_i18n.language}/cart/`;
            const el = '#view-totals';
            let xhr = null;
            const reload = function() {
                if (xhr) {
                    xhr.abort();
                }

                xhr = $.ajax({
                    url: endPoint,
                    global: false,
                    type: 'GET',
                    dataType: 'html',
                    async: true,
                })
                    .done(data => {
                        $(el).html($(data).find(el).html());
                        bind();
                    })
                    .fail(data => {
                        Site.Notify.onError(data);
                    });
            };

            return {
                reload,
            };
        }()),

        Library: (function() {
            let xhr = null;

            const drop = function($el, data, cb) {
                if (xhr) {
                    xhr.abort();
                }

                // ajax request type temp. set to GET --> POST

                xhr = $.ajax({
                    url: data.target,
                    global: false,
                    type: 'DELETE',
                    dataType: 'html',
                    data,
                    async: true,
                })
                    .done(() => {
                        if (window.cstm_library) {
                            const headerElement = $('#library-header');
                            window.cstm_library.items_quantity -= 1;
                            if (window.cstm_library.items_quantity === 0) headerElement.text(window.cstm_library.header_empty);
                            if (window.cstm_library.items_quantity === 1) headerElement.text(window.cstm_library.header_singular);
                            if (window.cstm_library.items_quantity > 1) {
                                headerElement.text(`${window.cstm_library.header_plural_1} ${window.cstm_library.items_quantity} ${window.cstm_library.header_plural_2}`);
                            }
                        }

                        window.TweenLite.to(
                            $el,
                            0.4, {
                                height: 0,
                                opacity: 0,
                                ease: Power4.easeOut,
                                onComplete() {
                                    $el.remove();
                                },
                            },
                        );

                        if (cb) cb();
                    })
                    .fail(_data => {
                        Site.Notify.onError(_data.responseText);
                    });
            };

            const send = function(data, target) {
                if (xhr) {
                    xhr.abort();
                }

                xhr = $.ajax({
                    url: data.target,
                    global: false,
                    type: 'PATCH',
                    dataType: 'html',
                    data: target,
                    async: true,
                })
                    .done(() => {
                        Site.Notify.onSuccess('Name changed!');
                    })
                    .fail(_data => {
                        Site.Notify.onError(_data.responseText);
                    });
            };

            const checkPromo = function(target, promo, amount, redirect) {
                if (xhr) {
                    xhr.abort();
                }

                xhr = $.ajax({
                    url: `/api/v1/check_promo/${promo}/`,
                    global: false,
                    type: 'POST',
                    dataType: 'json',
                    data: amount,
                    async: true,
                })
                    .done(_data => {
                        if (_data.status === 'error_voucher_conditions') {
                            Site.Notify.onError(_data.message);
                            promoTrack(promo, 'invalid');
                        } else if (_data.status === 'error' || _data.is_active === false) {
                            Site.Notify.onError(_data.message);
                            promoTrack(promo, 'inactive');
                        } else {
                            const { priceFormatFn: formatPrice } = window;
                            const discountPrice = formatPrice(_data.order_pricing.discount_value);
                            const totalPrice = formatPrice(_data.order_pricing.total_price);

                            $('#promo-code-input').addClass('visually-hidden');
                            $('#discount-value').addClass('visually-hidden');
                            $('#promo-code-value').removeClass('visually-hidden');
                            $('#promo-text').text(promo);
                            $('#promo-value').text(discountPrice);
                            $('#total-value').text(totalPrice);
                            $('.js-cart-total-value').text(totalPrice);
                            $('#promo-summary').removeClass('visually-hidden');
                            $('.promo-warning').show();
                            $('#id_promo_text').val(promo);
                            $('.pay_promo_text').val(promo);
                            $('#promo').val('');
                            Site.Notify.onSuccess(cstm_i18n.promo_accepted);
                            if (redirect) {
                                setTimeout(() => {
                                    if ($('#summary-form').length > 0) {
                                        const self = $(target);
                                        const form = $('#summary-form');
                                        const tempElement = $("<input type='hidden'/>");

                                        tempElement
                                            .attr('name', self.attr('name'))
                                            .attr('type', 'submit')
                                            .val(self.val())
                                            .appendTo(form);

                                        tempElement.trigger('click');
                                    } else {
                                        window.location = target.data('target');
                                    }
                                }, 2000);
                            }
                            promoTrack(promo, 'active');
                        }
                    })
                    .fail(() => {
                        Site.Notify.onError(cstm_i18n.promo_not_accepted);
                        promoTrack(promo, 'inactive');
                    });
            };

            const dropPromo = function() {
                if (xhr) {
                    xhr.abort();
                }
                const promo = $('#promo').val();

                xhr = $.ajax({
                    url: '/api/v1/check_promo/',
                    global: false,
                    type: 'POST',
                    dataType: 'json',
                    data: null,
                    async: true,
                })
                    .done(_data => {
                        promoTrack(promo, 'removed');

                        const { priceFormatFn: formatPrice } = window;
                        const discountPrice = formatPrice(_data.order_pricing.discount_value);
                        const totalPrice = formatPrice(_data.order_pricing.total_price);

                        $('#promo-summary').addClass('visually-hidden');
                        $('#discount-value').removeClass('visually-hidden').css('display', 'block');
                        $('#discount-value .right-aligned').text(discountPrice);
                        $('#show-promo-code').css('display', 'block').removeClass('visually-hidden');
                        $('#promo-code-input').addClass('visually-hidden');
                        $('#id_promo_text').val('');
                        $('#pay_promo_text').val('');
                        $('#total-value').text(totalPrice);
                        $('.js-cart-total-value').text(totalPrice);
                        $('#promo').val('');
                    })
                    .fail(() => {
                        Site.Notify.onError(cstm_i18n.promo_not_accepted);
                    });
            };

            const move = function(type, id) {
                if (xhr) {
                    xhr.abort();
                }

                xhr = $.ajax({
                    url: `/api/v1/gallery/${type}/${id}/add_to_cart_by_id/`,
                    global: false,
                    type: 'POST',
                    dataType: 'json',
                    data: null,
                    async: true,
                })
                    .done(() => {
                        Site.CartController.sync();
                        const overlay = $('.menu-overlay-box.cart');
                        const background = $('.overlay-div-cover');
                        background.addClass('visible');
                        overlay.addClass('visible');
                        if (!background.is(':visible')) {
                            Site.Notify.onSuccess(cstm_i18n.added_to_cart);
                        }
                        setTimeout(() => {
                            background.removeClass('visible');
                            overlay.removeClass('visible');
                        }, 3000);
                    })
                    .fail(() => {
                        Site.Notify.onError(cstm_i18n.shelf_not_saved);
                    });
            };

            // /////////////////////////
            // /////////////////////////
            // /////////////////////////
            // Sidebar Cart 2017 - Start

            let sidebarCartCheckPromoInProgress = false;

            const sidebarCartCheckPromo = function(target, promo, amount, redirect) {
                const strikethroughCode = window.cstm_i18n.strikethrough_code;

                if (sidebarCartCheckPromoInProgress) {
                    return;
                }
                if (xhr) {
                    xhr.abort();
                }

                sidebarCartCheckPromoInProgress = true;

                xhr = $.ajax({
                    url: `/api/v1/check_promo/${promo}/`,
                    global: false,
                    type: 'POST',
                    dataType: 'json',
                    data: amount,
                    async: true,
                })
                    .done(_data => {
                        if (_data.status === 'error_voucher_conditions') {
                            Site.Notify.onError(_data.message);
                            promoTrack(promo, 'invalid');
                        } else if (_data.status === 'error' || _data.is_active === false) {
                            Site.Notify.onError(_data.message);
                            promoTrack(promo, 'inactive');
                        } else {
                            $('#promo-code-input').addClass('visually-hidden');
                            $('#discount-value').addClass('visually-hidden');
                            $('#promo-code-value').removeClass('visually-hidden');

                            if (strikethroughCode && strikethroughCode.toUpperCase() !== promo.toUpperCase()) {
                                $('.cart-item-price-crossed-out__container').addClass('cart-item-price-crossed-out__container--hidden');
                                $('.cart-item-price-crossed-out__item-price').addClass('cart-item-price-crossed-out__item-price--hidden');
                                $('.cart-item-price-crossed-out__item-price-withDiscount').addClass('cart-item-price-crossed-out__item-price--visible');
                            } else {
                                $('.cart-item-price-crossed-out__container').removeClass('cart-item-price-crossed-out__container--hidden');
                                $('.cart-item-price-crossed-out__item-price').removeClass('cart-item-price-crossed-out__item-price--hidden');
                                $('.cart-item-price-crossed-out__item-price-withDiscount').removeClass('cart-item-price-crossed-out__item-price--visible');
                            }

                            $('.sa-cart-promocode')
                                .removeClass('sa-cart-promocode-visible-input')
                                .addClass('sa-cart-promocode-visible-result');

                            const { priceFormatFn: formatPrice } = window;
                            const discountPrice = formatPrice(_data.order_pricing.discount_value);
                            const totalPrice = formatPrice(_data.order_pricing.total_price);
                            const recycleTax = formatPrice(_data.order_pricing.recycle_tax_value);
                            const totalPriceWithoutRecycleTax = formatPrice(_data.order_pricing.total_price - _data.order_pricing.recycle_tax_value);

                            Site.sidebarCartAssembly.updateRecycleTaxPopup(totalPriceWithoutRecycleTax, recycleTax, totalPrice);

                            $('#promo-text').text(promo);
                            $('#promo-value').text(discountPrice);
                            $('#total-value').text(totalPrice);
                            $('.js-cart-total-value').text(totalPrice);
                            $('.region-price').text(totalPrice);
                            $('.button-pay-value').text(totalPrice);

                            $('#promo-summary').removeClass('visually-hidden');
                            $('.promo-warning').show();
                            $('#id_promo_text').val(promo);
                            $('.pay_promo_text').val(promo);
                            $('#promo').val('');
                            $('.luke-sidebar #promo').css('border-color', '');
                            Site.Notify.onSuccess(cstm_i18n.promo_accepted);
                            if (redirect) {
                                setTimeout(() => {
                                    if ($('#summary-form').length > 0) {
                                        const self = $(target);
                                        const form = $('#summary-form');
                                        const tempElement = $("<input type='hidden'/>");

                                        tempElement
                                            .attr('name', self.attr('name'))
                                            .attr('type', 'submit')
                                            .val(self.val())
                                            .appendTo(form);

                                        tempElement.trigger('click');
                                    } else {
                                        window.location = target.data('target');
                                    }
                                }, 2000);
                            }
                            promoTrack(promo, 'active');
                        }
                    })
                    .fail(() => {
                        Site.Notify.onError(cstm_i18n.promo_not_accepted);
                        promoTrack(promo, 'inactive');
                    })
                    .always(() => {
                        sidebarCartCheckPromoInProgress = false;
                        $('.sa-cart-promocode').removeClass('disabled-temporarily');
                    });
            };

            const sidebarCartDropPromo = function() {
                if (xhr) {
                    xhr.abort();
                }

                const promo = $('#promo-text')[0].textContent;

                xhr = $.ajax({
                    url: '/api/v1/check_promo/',
                    global: false,
                    type: 'POST',
                    dataType: 'json',
                    data: null,
                    async: true,
                })
                    .done(_data => {
                        promoTrack(promo, 'removed');

                        const { priceFormatFn: formatPrice } = window;
                        const totalPrice = formatPrice(_data.order_pricing.total_price);

                        $('.sa-cart-promocode').removeClass('sa-cart-promocode-visible-result');


                        $('.cart-item-price-crossed-out__container').addClass('cart-item-price-crossed-out__container--hidden');
                        $('.cart-item-price-crossed-out__item-price').addClass('cart-item-price-crossed-out__item-price--hidden');
                        $('.cart-item-price-crossed-out__item-price-withDiscount').addClass('cart-item-price-crossed-out__item-price--visible');

                        $('#promo-summary').addClass('visually-hidden');
                        $('#discount-value').removeClass('visually-hidden').css('display', 'block');
                        $('#discount-value .right-aligned').text(totalPrice);
                        $('#show-promo-code').css('display', 'block').removeClass('visually-hidden');
                        $('#promo-code-input').addClass('visually-hidden');
                        $('#id_promo_text').val('');
                        $('#pay_promo_text').val('');
                        // console.log(_data);
                        $('#total-value').text(totalPrice);
                        $('.js-cart-total-value').text(totalPrice);
                        $('.region-price').text(totalPrice);
                        $('.button-pay-value').text(totalPrice);


                        $('#promo').val('');
                    })
                    .fail(() => {
                        Site.Notify.onError(cstm_i18n.promo_not_accepted);
                    })
                    .always(() => {
                        $('.sa-cart-promocode').removeClass('disabled-temporarily');
                    });
            };

            // Wishlist page button action
            const sidebarCartMove = function(type, id, cb) {
                if (xhr) {
                    xhr.abort();
                }

                xhr = $.ajax({
                    url: `/api/v1/gallery/${type}/${id}/add_to_cart_by_id/`,
                    global: false,
                    type: 'POST',
                    dataType: 'json',
                    data: null,
                    async: true,
                })
                    .done(() => {
                        Site.CartController.sync();
                        Site.sidebarCartAssembly.showCartHandler();

                        if (cb) cb();

                        Site.sidebarCartAssembly.fadeInLastCartItem(900);

                        setTimeout(() => {
                            Site.sidebarCartAssembly.hideCartHandler();
                        }, 3000);
                    })
                    .fail(() => {
                        Site.Notify.onError(cstm_i18n.shelf_not_saved);
                    });
            };

            // Sidebar Cart 2017 - END
            // ///////////////////////
            // ///////////////////////
            // ///////////////////////


            return {
                drop,
                send,
                move,
                checkPromo,
                dropPromo,
                sidebarCartCheckPromo,
                sidebarCartDropPromo,
                sidebarCartMove,
            };
        }()),
    };

    const onUnLoad = function() {};

    const actions = {
        dropAsk(data, target) {
            const $this = $(target);
            const attr = $this.attr('data-target-action');
            const popup = $($this.data('target-popup'));
            popup.find('a.confirm').attr('data-shop-trigger', attr);
            popup.trigger('open');
        },

        dropProduct(data, target, event) {
            event.preventDefault();
            const product = $(`.cart-list-product[data-product-id='${data.id}']`);

            Site.CartController.remove(data, data => {
                Site.CartController.priceReload(data);
            });

            if (!product.siblings().length || ((product.parent().find('.assembly-check').length > 0) && product.siblings().length === 1)) {
                const parent = product.parent();
                parent.fadeOut(() => {
                    parent.remove();
                    if (!$('.cart-list-products').length) {
                        window.location = window.location;
                    }
                });
            } else {
                window.TweenLite.to(
                    product,
                    0.4, {
                        opacity: 0,
                        onComplete() {
                            product.remove();
                            Subviews.Cart.reload();
                        },
                    },
                );
            }
        },

        dropLibraryProduct(data, target) {
            let $el;
            if (window.cro_wishlist_attempt_1) {
                $el = target.closest('.library-item').parent();
            } else {
                $el = target.closest('.library-item');
            }
            Subviews.Library.drop($el, data, () => {
                Site.Track.trackRemoveFromCart($(target).closest('[data-item-id]').find('.ecommerce-gtm-data').data('ecommerce-data'));
            });

            const counter = $('.wishlist-indicator > em');
            counter.each(function() {
                const count = parseInt($(this).html(), 10) - 1;
                $(this).html(count);
                if (count === 0) $(this).hide();
            });
        },

        moveLibraryProduct(data, target, event) {
            event.preventDefault();
            Subviews.Library.move(data.type, data.id);
        },

        sidebarCartMoveLibraryProduct(data, target, event) {
            event.preventDefault();
            Subviews.Library.sidebarCartMove(data.type, data.id, () => {
                Site.Track.trackAddToCart($(target).closest('[data-item-id]').find('.ecommerce-gtm-data').data('ecommerce-data'));
            });
        },

        checkPromo(data, target) {
            if (data.redirect && ($('#promo').val().length === 0)) {
                if ($('#summary-form').length > 0) {
                    const self = $(target);
                    const form = $('#summary-form');
                    const tempElement = $("<input type='hidden'/>");

                    tempElement
                        .attr('name', self.attr('name'))
                        .attr('type', 'submit')
                        .val(self.val())
                        .appendTo(form);

                    tempElement.trigger('click');
                } else {
                    window.location = target.data('target');
                }
            } else if ($('#promo').val().length > 0) {
                Subviews.Library.checkPromo(target, $('#promo').val(), {
                    value: $('#order_actual_value').val(),
                }, data.redirect);
            } else {
                Site.Notify.onError(cstm_i18n.promo_not_accepted);
            }
            return false;
        },

        dropPromo() {
            Subviews.Library.dropPromo();
        },

        sidebarCartCheckPromo(data, target) {
            if (data.redirect && ($('#promo').val().length === 0)) {
                if ($('#summary-form').length > 0) {
                    const self = $(target);
                    const form = $('#summary-form');
                    const tempElement = $("<input type='hidden'/>");

                    tempElement
                        .attr('name', self.attr('name'))
                        .attr('type', 'submit')
                        .val(self.val())
                        .appendTo(form);

                    tempElement.trigger('click');
                } else {
                    window.location = target.data('target');
                }
            } else if ($('#promo').val().length > 0) {
                Subviews.Library.sidebarCartCheckPromo(target, $('#promo').val(), {
                    value: $('#order_actual_value').val(),
                }, data.redirect);
            } else {
                Site.Notify.onError(cstm_i18n.promo_not_accepted);
            }
            return false;
        },

        sidebarCartDropPromo() {
            Subviews.Library.sidebarCartDropPromo();
        },

        updateLibraryProduct(data, target) {
            Subviews.Library.send(data, target);
        },
        addQuantity(data) {
            Site.CartController.addQuantity(data, Site.CartController.priceReload);
        },
        changeAssembly(data) {
            Site.CartController.setAssembly(data, Site.CartController.priceReload);
        },
    };

    const callAction = function(action, data, target, e) {
        if (action in actions) {
            // call action with data
            actions[action](data, target, e);
        } else {
            // eslint-disable-next-line no-console
            console.warn(`action: ${action} not found`);
        }
    };

    const onTriggerClick = function(e) {
        if (e.delegateTarget.nodeName === 'BUTTON' || e.delegateTarget.nodeName === 'A') {
            e.preventDefault();
        }
        const target = $(e.currentTarget);
        const triggerOptions = JSON.parse(target.attr('data-shop-trigger'));
        if (target.hasClass('webview-delete')) {
            try {
                webkit.messageHandlers.cartCallback.postMessage({
                    itemCount: `${$('.cart-page-visible-list li').length - 1}`,
                });
            } catch (err) {
                // eslint-disable-next-line no-console
                console.log(err);
            }
        }

        callAction(triggerOptions.action, triggerOptions, target, e);
    };

    const onTriggerChange = function(e) {
        $(e.currentTarget).data('shop-trigger');
    };

    const onFormSubmit = function(e) {
        e.preventDefault();

        const $form = $(e.currentTarget);
        const formOptions = $form.data('shop-form');
        const data = $form.serialize();

        callAction(formOptions.action, formOptions, data);
    };

    const bind = function() {
        $('[data-shop-form]')
            .off('.shop')
            .on('submit.shop', onFormSubmit);

        $('[data-shop-trigger]')
            .off('.shop')
            .on('click.shop', onTriggerClick)
            .on('change.shop', onTriggerChange);
    };

    const initialize = function() {
        bind();
    };

    return {
        initialize,
        onUnLoad,
        Subviews,
    };
}(jQuery));
