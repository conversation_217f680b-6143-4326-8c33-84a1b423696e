// TODO: remove after new nuxt pdp release
const Site = window.Site || {};

Site.configuratorLoader = (function() {
    function initialize() {
        PubSub.subscribe('configuratorInitialized', () => {
            const configuratorLoader = document.getElementById('configuratorLoader');
            const configuratorWrapper = document.getElementsByClassName('configurator');
            if (configuratorWrapper && configuratorWrapper.length) { configuratorWrapper[0].style.opacity = 1; }
            if (configuratorLoader) {
                configuratorLoader.style.opacity = 0;
                setTimeout(() => {
                    configuratorLoader.remove();
                }, 500);
            }
        });
    }

    return {
        initialize,
    };
}(jQuery));
