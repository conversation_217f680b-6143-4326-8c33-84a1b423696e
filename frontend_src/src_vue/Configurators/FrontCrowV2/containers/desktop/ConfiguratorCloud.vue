<template>
  <div
    v-show="cloudPosition"
    class="configurator-dom-element configurator-cloud z-30 ml-4"
    :style="calculateHelper()"
  >
    <ControlsActiveRow v-if="!bottomStorageControlsVisible && !topStorageControlsVisible" />
    <ControlsStorage
      v-else-if="bottomStorageControlsVisible || topStorageControlsVisible"
      :storageType="bottomStorageControlsVisible ? 'bottom' : 'top'"
    />
  </div>
</template>

<script>
import ControlsStorage from './ControlsStorage';
import ControlsActiveRow from './ControlsActiveRow';

export default {
  components: {
    ControlsActiveRow,
    ControlsStorage,
  },
  computed: {
    cloudPosition() {
      return this.$store.getters['ui/GET_CLOUD_POSITION'];
    },
    topStorage() {
      return this.$store.getters['configuration/GET_TOP_STORAGE'];
    },
    bottomStorage() {
      return this.$store.getters['configuration/GET_BOTTOM_STORAGE'];
    },
    rowAmount() {
      let rows = this.$store.getters['configuration/GET_ROW_AMOUNT'];
      if (this.topStorage) rows += 1;
      if (this.bottomStorage) rows += 1;
      return rows;
    },
    activeRow() {
      return this.$store.getters.GET_ACTIVE_ROW;
    },
    bottomStorageControlsVisible() {
      return this.activeRow === -1;
    },
    topStorageControlsVisible() {
      if (!this.topStorage) return false;
      let offset = 1;
      if (this.bottomStorage) offset += 1;
      return (this.rowAmount - offset) === Number(this.activeRow);
    },
  },
  methods: {
    calculateHelper() {
      return `
                 top: ${this.cloudPosition.y}px;
                 left: ${this.cloudPosition.x}px;
              `;
    },
  },
};
</script>
