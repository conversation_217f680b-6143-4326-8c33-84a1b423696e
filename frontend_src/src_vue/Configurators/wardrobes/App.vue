<template>
  <div
    class="noselect"
    :class="{ 'visible': mountedToHTML }"
  >
    <component
      :is="configuratorComponentName"
    />
  </div>
</template>
<script>
  import { configuratorName } from '@src_vue/Configurators/wardrobes/shared/config/configuratorName.ts';
  import CWatWar from './modules/CWatWar/CWatWar';
  import CVert from './modules/CVert/CVert';

  export default {
    name: 'App',
    components: {
      CWatWar,
      CVert,
    },
    data() {
      return {
        mountedToHTML: false,
        isMobile: false,
        configuratorComponentName: '',
      };
    },
    created() {
      this.mountedToHTML = true;
      this.configuratorComponentName = configuratorName[window.cstm.shelfType];
    },
  };
</script>
