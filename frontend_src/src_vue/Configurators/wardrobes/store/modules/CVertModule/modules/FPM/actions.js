import { debounce } from 'lodash';
import materialConfig from '@configurator-common/helpers/materialsConfigTheVertical';
import * as core from '../../../../../modules/CVert/core';

export default {
  INIT_FPM({ dispatch, rootState }, serialization) {
    core.initFurnitureProjectManager(serialization);

    if (Object.keys(rootState.commonFurniture.item.configurator_params).length === 0) {
      core.FPM.loadPresetFromSerialization(rootState.preset);
    } else {
      core.FPM.loadPresetFromJetty(rootState.commonFurniture.item);
    }

    const configuration = core.FPM.getConfiguration();

    dispatch('configuration/UPDATE_CONFIGURATION', { payload: configuration, history: true }, { root: true });

    dispatch('UPDATE_DYNAMIC_DELIVERY', null, { root: true });
  },
  CHANGE_CONFIGURATION({ dispatch }, { payload, cameraUpdate }) {
    const shouldUpdateCamera = Object.keys(payload)[0] === 'drawerStyle' ? false : cameraUpdate;
    if (Object.keys(payload).length === 1 && cameraUpdate) {
      dispatch('FPM/DISPATCH_TRACKING_DATA', { paramName: Object.keys(payload)[0], payload: payload[Object.keys(payload)[0]] }, { root: true });
    }
    const newConfiguration = core.FPM.changeConfiguration(payload);
    dispatch('configuration/UPDATE_CONFIGURATION', { payload: newConfiguration, cameraUpdate: shouldUpdateCamera }, { root: true });

    dispatch('UPDATE_DYNAMIC_DELIVERY', null, { root: true });
  },
  CHANGE_LOCAL_CONFIGURATION({ dispatch, rootState }, { paramName, payload }) {
    const { activeComponent } = rootState;
    const currentChannelId = rootState.FPM.components[activeComponent].channel_id;

    const configuration = core.FPM.changeConfigurationWithLocalChange(paramName, currentChannelId, payload);
    dispatch('configuration/UPDATE_CONFIGURATION', { payload: configuration, history: true }, { root: true });
    dispatch('FPM/DISPATCH_TRACKING_DATA', { paramName, payload }, { root: true });

    if (paramName === 'doors_direction' || paramName === 'column_exposition') {
      dispatch('ui/RERENDER_THUMBNAILS', null, { root: true });
    }
    if (['thumbnail', 'local_x', 'doors_direction', 'column_exposition'].includes(paramName)) {
      setTimeout(() => { dispatch('renderer/UPDATE_INTERACTION_STATE', null, { root: true }); }, 1);
    }

    dispatch('UPDATE_DYNAMIC_DELIVERY', null, { root: true });
  },
  UPDATE_THUMBNAILS_GEOMETRY({ commit, rootState }, payload) {
    const activeComponentId = payload || rootState.activeComponent;
    const thumbs = core.FPM.getThumbnails(activeComponentId);
    commit('updateThumbnailsGeometry', thumbs);
  },
  UPDATE_GEOMETRY({
    rootState, dispatch, commit, rootGetters,
  }, cameraUpdate = true) {
    const rawGeom = core.FPM.getGeometry();
    const renderGeom = core.FPM.getRender();

    dispatch('UPDATE_PRICE', rawGeom, { root: true });
    commit('updateComponents', renderGeom.components);
    commit('updateGeom', renderGeom);
    commit('updateRawGeom', rawGeom);
    if (rootState.isRendererLoaded) dispatch('renderer/UPDATE_RENDERER', cameraUpdate, { root: true });
    if (rootState.dimensions.show) dispatch('dimensions/UPDATE_DIMENSIONS', null, { root: true });

    if (cameraUpdate && core.offscreenRenderer && rootState.ui.isMobile) {
      dispatch('DEBOUNCE_UPDATE_EXIT_MOBILE_SECTION');
    }
  },
  DEBOUNCE_UPDATE_EXIT_MOBILE_SECTION: debounce(({ dispatch, rootState: { renderer: { material }, commonFurniture: { item } } }) => {
    dispatch('renderer/UPDATE_OFFSCREEN_RENDERER', null, { root: true });
    const geom = core.FPM.getGeometry();
    geom.material = material;
    dispatch('commonGeomLocalStorage/UPDATE_GEOM_LOCAL_STORAGE', {
      screenshot: core.offscreenRenderer.getScreenshotForCart(),
      geom,
      endpoint: 'watty',
      base_preset: item.id,
      isWardrobe: true,
    }, { root: true });
  }, 1000),
  DISPATCH_TRACKING_DATA({ rootState }, { payload, paramName }) {
    PubSub.publish('atupaleLayer_configurator', { method: 'hitGlobalObjectData', payload: { configuratorState: rootState, paramName, payload } });
  },
  UPDATE_PRODUCT_DETAILS({ commit, rootState: { configuration, renderer: { material } }, rootGetters }) {
    const {
      doors, drawers, bars, height, backs,
    } = core.FPM.getGeometry();
    const wattyInfo = {
      doors: doors.length,
      drawers: drawers.length,
    };
    const materialObj = materialConfig('cvert')[0].find(config => config.value === material);
    const heightInCm = Math.round(height / 10);
    commit('updateWattyInfo', wattyInfo);

    const prices = {
      price: rootGetters['commonPrices/GET_PRICE'],
      priceWithDiscount: rootGetters['commonPrices/GET_RAW_DISCOUNTED_PRICE'],
      promo: rootGetters['commonPrices/GET_PROMO'],
    };

    PubSub.publish('configuratorParametersChange', {
      prices,
      depth: configuration.depth,
      height: heightInCm,
      width: configuration.width,
      materialValue: materialObj.value,
      barSubtypes: bars.length ? bars[0].subtype : null,
      backpanels: backs.length,
      ...wattyInfo,
    });
  },
};
