
export const getCookie = name => {
    const re = new RegExp(`${name}=([^;]+)`);
    const value = re.exec(document.cookie);
    return (value != null) ? unescape(value[1]) : null;
};

export const generateEditShelfLink = (isWardrobe, lang, origin, id) => {
    const baseText = `${origin}/`;
    const concatText = {
        en: isWardrobe ? 'wardrobe/' : 'shelf/',
        de: isWardrobe ? 'de/wardrobe/' : 'de/regal/',
        fr: isWardrobe ? 'fr/wardrobe/' : 'fr/etagere/',
    };
    const userId = getCookie('userGaId').replace('"user-', '').slice(0, -1);
    return `${baseText + concatText[lang] + id}/?sharing=true&userId=${userId}`;
};
