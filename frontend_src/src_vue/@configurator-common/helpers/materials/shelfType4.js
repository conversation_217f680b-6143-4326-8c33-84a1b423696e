export default (prefix = 'cvert') => [
  {
    type: 'type13',
    imgPath: '/r_static/products-list/color-swatches',
    imgFileName: 'T03-white-vert',
    shelfType: 4,
    value: 0,
    alt: 'white',
    material_name: 'white',
    translation: `${prefix}_material_white`,
    bgColor: '#ECE7A6',
    order: 6,
  },
  {
    type: 'type13',
    imgPath: '/r_static/products-list/color-swatches',
    imgFileName: 'T02-grey-vert',
    shelfType: 4,
    value: 3,
    alt: 'Grey',
    material_name: 'grey',
    translation: `${prefix}_material_grey`,
    bgColor: '#DCDDE0',
    order: 7,
  },
  {
    type: 'type13',
    imgPath: '/r_static/products-list/color-swatches',
    imgFileName: 'T03-black',
    shelfType: 4,
    value: 11,
    alt: 'Black',
    material_name: 'black',
    translation: `${prefix}_material_black`,
    bgColor: '#1A1B1A',
    order: 8,
  },
  {
    type: 'type13',
    imgPath: '/r_static/products-list/color-swatches',
    imgFileName: 'T03-clay-brown',
    shelfType: 4,
    value: 8,
    alt: 'Clay Brown',
    material_name: 'clay_brown',
    translation: `${prefix}_material_clay_brown`,
    bgColor: '#C88A64',
    order: 9,
  },
  {
    type: 'type13',
    imgPath: '/r_static/products-list/color-swatches',
    imgFileName: 'T03-olive-green',
    shelfType: 4,
    value: 9,
    alt: 'Olive Green',
    material_name: 'olive_green',
    translation: `${prefix}_material_olive_green`,
    bgColor: '#717A54',
    order: 10,
  },
  {
    type: 'type13',
    imgPath: '/r_static/products-list/color-swatches',
    imgFileName: 'T03-sand',
    shelfType: 4,
    value: 10,
    alt: 'Sand',
    material_name: 'sand',
    translation: `${prefix}_material_sand`,
    bgColor: '#DBDCB',
    order: 11,
  },
  {
    type: 'type13',
    imgPath: '/r_static/products-list/color-swatches',
    imgFileName: 'T01p-white-vert',
    shelfType: 4,
    value: 5,
    alt: 'white',
    material_name: 'white-plywood',
    translation: `${prefix}_material_white_plywood`,
    bgColor: '#FFF8C9',
    order: 0,
    prefabrication: [],
  },
  {
    type: 'type13',
    imgPath: '/r_static/products-list/color-swatches',
    imgFileName: 'T01p-grey-vert',
    shelfType: 4,
    value: 6,
    alt: 'grey',
    material_name: 'grey-plywood',
    translation: `${prefix}_material_grey_plywood`,
    bgColor: '#9B7D65',
    order: 1,
    prefabrication: [],
  },
  {
    type: 'type13',
    imgPath: '/r_static/products-list/color-swatches',
    imgFileName: 'T01p-black-vert',
    shelfType: 4,
    value: 7,
    alt: 'black',
    material_name: 'black-plywood',
    translation: `${prefix}_material_black_plywood`,
    bgColor: '#D2BFA9',
    order: 2,
    prefabrication: [],
  },
  {
    type: 'type13',
    imgPath: '/r_static/products-list/color-swatches',
    imgFileName: 'T02-sand-mustard-yellow-vert',
    shelfType: 4,
    value: 2,
    alt: 'Sand + Mustard Yellow',
    material_name: 'sand-mustard-yellow',
    translation: `${prefix}_material_sand_mustard_yellow`,
    bgColor: '#864834',
    order: 4,
    prefabrication: [],
  },
  {
    type: 'type13',
    imgPath: '/r_static/products-list/color-swatches',
    imgFileName: 'T02-beige-vert',
    shelfType: 4,
    value: 1,
    alt: 'sand',
    material_name: 'sand-midnight-blue',
    translation: `${prefix}_material_sand_midnight_blue`,
    bgColor: '#864834',
    order: 3,
    prefabrication: [],
  },
  {
    type: 'type13',
    imgPath: '/r_static/products-list/color-swatches',
    imgFileName: 'T02-grey-dark-grey-vert',
    shelfType: 4,
    value: 4,
    alt: 'Grey + Dark Grey',
    material_name: 'grey-darkgrey',
    translation: `${prefix}_material_grey_dark_grey`,
    bgColor: '#DCDDE0',
    order: 5,
  },
];
