import shelfType0 from './materials/shelfType0';
import shelfType1 from './materials/shelfType1';
import shelfType2 from './materials/shelfType2';
import { shelfTypeMaterialConfigGenerator } from './materials/shelfTypeHelpers';

const deskMaterialConfig = (prefix = 'cplus') => {
  const shelfType0Config = shelfTypeMaterialConfigGenerator(shelfType0(prefix), ['white', 'grey', 'black', 'dusty_pink', 'yellow', 'blue', 'moss_green']);
  const shelfType1Config = shelfTypeMaterialConfigGenerator(shelfType1(prefix), ['basic_white', 't02_grey', 'beige', 'sage_green', 'cotton', 'orange', 'stone_grey', 'reisingers_pink']);
  const shelfType2Config = shelfTypeMaterialConfigGenerator(shelfType2(prefix), ['ash_veneer', 'oak_veneer']);

  return [
    shelfType0Config,
    shelfType1Config,
    shelfType2Config,
  ];
};
export default deskMaterialConfig;
