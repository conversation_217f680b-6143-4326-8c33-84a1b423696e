import shelfType0 from './materials/shelfType0';
import shelfType1 from './materials/shelfType1';
import shelfType2 from './materials/shelfType2';
import { shelfTypeMaterialConfigGenerator } from './materials/shelfTypeHelpers';

const materialConfig = (prefix = 'cplus') => {
  const shelfType0Config = shelfTypeMaterialConfigGenerator(shelfType0(prefix), [
    'white', 'black', 'dusty_pink', 'grey',
    'yellow', 'blue', 'moss_green',
  ]);

  const shelfType1Config = shelfTypeMaterialConfigGenerator(shelfType1(prefix), [
    'basic_white', 'beige', 'indygo', 'matte_black', 'orange', 'sky_blue',
    'stone_grey', 't02_grey', 'cotton', 'sage_green',
    'burgundy', 'reisingers_pink',
  ]);
  const shelfType2Config = shelfTypeMaterialConfigGenerator(shelfType2(prefix), [
    'walnut_veneer', 'oak_veneer', 'ash_veneer',
  ]);

  const materials = [
    shelfType0Config,
    shelfType1Config,
    shelfType2Config,
  ];

  return materials;
};

export default materialConfig;
