<template>
  <div class="materials-exterior-interior-wrapper">
    <div
      class="row middle-xs"
      :class="{
        'mt-16': !isMobile
      }"
    >
      <div class="col-xs-3">
        <p class="normal-14 text-offblack-800">
          {{ $t("cwatwar_color_exterior") }}
        </p>
      </div>
      <TylkoMaterials
        :materialsConfig="exteriorMaterialsConfig"
        :activeMaterial="activeExteriorMaterialValue"
        :handleMaterialUpdate="updateExteriorMaterial"
        colorTooltips
        :colorTranslations="exteriorMaterialTrans"
        class="col-xs-9 start-xs"
        :largeIcons="true"
      />
    </div>
    <div class="row middle-xs mt-16">
      <div class="col-xs-3">
        <p class="normal-14 text-offblack-800">
          {{ $t("cwatwar_color_interior") }}
        </p>
      </div>
      <TylkoMaterials
        :materialsConfig="interiorMaterialsConfig"
        :activeMaterial="activeInteriorMaterial"
        :handleMaterialUpdate="updateInteriorMaterial"
        colorTooltips
        :colorTranslations="interiorMaterialTrans"
        class="col-xs-9 start-xs"
        :largeIcons="true"
      />
    </div>
  </div>
</template>
<script>

import { TylkoMaterials } from '@componentsConfigurator';
import materialsConfig from '@configurator-common/helpers/materialsConfigWatty';
import MaterialsExteriorInteriorAdapter from '@configurator-common/helpers/materialsExteriorInteriorAdapter';

const MATERIAL_CONFIG = materialsConfig()[0];

export default {
  components: {
    TylkoMaterials,
  },
  data() {
    return {
      // Material value for config definitions of colors
      activeExteriorMaterialValue: 0,
    };
  },
  computed: {
    isMobile() {
      return this.$store.getters['ui/GET_IS_MOBILE'];
    },
    formattedMaterialsConfig() {
      const formattedMaterialsConfig = new MaterialsExteriorInteriorAdapter(MATERIAL_CONFIG);

      return formattedMaterialsConfig.getFormattedMaterialConfig();
    },
    exteriorMaterialsConfig() {
      return this.formattedMaterialsConfig;
    },
    interiorMaterialsConfig() {
      const currentExteriorColor = this.formattedMaterialsConfig.find(exteriorColorObject => {
        if (exteriorColorObject.value === this.activeExteriorMaterialValue) {
          return exteriorColorObject.interiorMaterials;
        }
      });

      return currentExteriorColor.interiorMaterials;
    },
    activeInteriorMaterial() {
      // Actual material value for furniture
      return this.$store.getters['renderer/GET_MATERIAL'];
    },
    exteriorMaterialTrans() {
      return [...this.exteriorMaterialsConfig.map(el => this.$t(el.translation))];
    },
    interiorMaterialTrans() {
      return [...this.interiorMaterialsConfig.map(el => this.$t(el.translation))];
    },
    isDoorsOpen() {
      return this.$store.getters['renderer/GET_DOORS_OPEN'];
    },
  },
  watch: {
    activeInteriorMaterial() {
      // Update default value when material is changed in store
      this.updateExteriorMaterialValue();
    },
  },
  mounted() {
    // Update default value on mounted
    this.updateExteriorMaterialValue();
  },
  methods: {
    updateExteriorMaterialValue() {
      // Material value from store
      const { activeInteriorMaterial } = this;
      // Find value for material in config
      const activeMaterial = MATERIAL_CONFIG.find(materialObject => materialObject.value === activeInteriorMaterial);

      // If material is exterior just pass the value to activeExteriorMaterialValue
      if (activeMaterial.exteriorMaterial) {
        this.activeExteriorMaterialValue = activeMaterial.value;
      } else {
        // If material is interior, pass exteriorMaterialValue as value for activeExteriorMaterialValue
        this.activeExteriorMaterialValue = activeMaterial.exteriorMaterialValue;
        // Open doors when choosing different color than exterior
        if (!this.isDoorsOpen) {
          this.openDoors();
        }
      }
    },
    updateExteriorMaterial(materialId) {
      const oldInteriorMaterial = this.findInteriorMaterial();
      this.activeExteriorMaterialValue = materialId;

      // If old material is one of exteriorMaterials then use passed materialId
      if (oldInteriorMaterial.exteriorMaterial) {
        this.updateInteriorMaterial(materialId);
      } else {
        // Find material with the same key in current interiorMaterialsConfig
        const newInteriorMaterial = this.findSimilarInteriorMaterial(oldInteriorMaterial);
        this.updateInteriorMaterial(newInteriorMaterial.value);
      }
    },
    updateInteriorMaterial(materialId) {
      this.$store.dispatch('commonGeomLocalStorage/UPDATE_WAS_CONFIGURED');
      this.$store.dispatch('renderer/CHANGE_MATERIAL', materialId);
    },
    findInteriorMaterial() {
      const { activeInteriorMaterial } = this;
      return this.interiorMaterialsConfig.find(materialObject => materialObject.value === activeInteriorMaterial);
    },
    findSimilarInteriorMaterial(oldInteriorMaterial) {
      return this.interiorMaterialsConfig.find(materialColor => materialColor.materialKey === oldInteriorMaterial.materialKey);
    },
    openDoors() {
      this.$store.dispatch('renderer/UPDATE_DOORS_OPEN', true);
    },
  },
};
</script>

<style lang="scss">
    .materials-exterior-interior-wrapper ul.colors-wrapper {
        gap: 0.5rem;
    }
</style>
