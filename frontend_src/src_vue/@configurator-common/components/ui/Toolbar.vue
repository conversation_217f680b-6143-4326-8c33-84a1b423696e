<template>
  <div class="tooltip-wrapper">
    <TylkoToolbarButton
      :content="isDoorsOpen ? $t(translationKeys.doors_close) : $t(translationKeys.doors_open)"
      class="mb-8"
    >
      <openDoorsButton v-model="isDoorsOpen" />
    </TylkoToolbarButton>
    <TylkoToolbarButton
      v-if="hasItems && itemsOnShelvesWardrobesAbTest"
      :content="shouldShowItems ? $t('cwatwar_items_1') : $t('cwatwar_items_2')"
      class="mb-8"
    >
      <button
        :class="['c-button-round control-btn', {active: shouldShowItems}]"
        @click="shouldShowItems = !shouldShowItems"
      >
        <img
          svg-inline
          src="@tylko_ui/icons-cplus/ic_show-items.svg"
          alt="Show items icon"
        >
      </button>
    </TylkoToolbarButton>
    <TylkoToolbarButton
      :content="isShowDimensions ? $t(translationKeys.dimensions_hide) : $t(translationKeys.dimensions_show)"
      class="mb-8"
    >
      <showDimensionsButton v-model="isShowDimensions" />
    </TylkoToolbarButton>
    <TylkoToolbarButton
      :content="$t(translationKeys.product_details)"
      class="mb-8"
    >
      <ProductDetailsButton />
    </TylkoToolbarButton>
    <TylkoToolbarButton
      :content="$t(translationKeys.share)"
      class="mb-8"
    >
      <button
        class="c-button-round control-btn"
        @click="shareShelf"
      >
        <img
          svg-inline
          src="@tylko_ui/icons-crow/share.svg"
          alt="Share icon"
          class="tooltip-wrapper--no-outline"
        >
      </button>
    </TylkoToolbarButton>
    <ThumbnailsGallery
      :activeMaterial="material"
      :shelfType="shelfType"
      :confType="confType"
    />
  </div>
</template>

<script>

import { TylkoToolbarButton, ThumbnailsGallery } from '@componentsConfigurator';
import ProductDetailsButton from '@configurator-common/components/ui/ProductDetailsButton';
import OpenDoorsButton from './OpenDoorsButton';
import ShowDimensionsButton from './ShowDimensionsButton';

export default {
  components: {
    TylkoToolbarButton,
    OpenDoorsButton,
    ShowDimensionsButton,
    ThumbnailsGallery,
    ProductDetailsButton,
  },
  props: {
    confType: {
      type: String,
      required: true,
    },
    translationKeys: {
      type: Object,
      required: true,
    },
  },
  computed: {
    isDoorsOpen: {
      get() {
        return this.$store.getters['renderer/GET_DOORS_OPEN'];
      },
      set(newVal) {
        this.$store.dispatch('renderer/UPDATE_DOORS_OPEN', newVal);
      },
    },
    isShowDimensions: {
      get() {
        return this.$store.getters['dimensions/GET_SHOW_DIMENSIONS'];
      },
      set(newVal) {
        this.$store.dispatch('dimensions/UPDATE_SHOW_DIMENSIONS', newVal);
      },
    },
    shouldShowItems: {
      get() {
        return this.$store.getters['renderer/GET_ITEMS_VISIBILITY'];
      },
      set(newVal) {
        this.$store.dispatch('renderer/UPDATE_ITEMS_VISIBILITY', newVal);
      },
    },
    hasItems() {
      return this.$store.getters['commonItemsOnShelves/GET_ITEMS_ON_SHELVES_COUNT'];
    },
    itemsOnShelvesWardrobesAbTest() {
      return this.$store.getters['commonAbTests/AB_TEST_VALUE']('items_on_shelves_wardrobes').isActive;
    },
    shelfType() {
      return this.$store.getters.GET_SHELF_TYPE;
    },
    material() {
      return this.$store.getters['renderer/GET_MATERIAL'];
    },
  },
  methods: {
    shareShelf() {
      this.$store.dispatch('SHARE_SHELF');
    },
  },
};
</script>
