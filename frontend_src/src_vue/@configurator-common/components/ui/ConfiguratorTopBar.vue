<template>
  <nav
    class="configurator-top-bar flex items-center"
    :class="[isTopBarVisible ? 'show' : '']"
  >
    <div class="grid-container flex items-center w-full">
      <div class="configurator-top-bar__column-1 flex flex-1 items-center">
        <div
          class="configurator-top-bar__product-title text-offblack-700"
          :class="[isMobile ? 'bold-12' : 'bold-20']"
        >
          {{ furnitureTitle }}
        </div>
        <div class="delivery md:flex md:items-center">
          <img
            class="mr-8"
            svg-inline
            src="@tylko_ui/icons-crow/delivery.svg"
          >
          <DynamicDeliverTimeLabel />
        </div>
      </div>
      <div class="configurator-top-bar__column-2 flex flex-1 justify-end items-center">
        <div class="configurator-top-bar__product-price">
          <slot name="product-price" />
        </div>
        <div class="configurator-top-bar__action-buttons flex">
          <slot name="save-for-later" />
          <slot name="cart" />
        </div>
      </div>
    </div>
  </nav>
</template>

<script>
import IntersectionObserverManager from '../../helpers/intersectionObserverManager';
import DynamicDeliverTimeLabel from './DynamicDeliverTimeLabel';

export default {
  name: 'ConfiguratorTopBar',
  components: {
    DynamicDeliverTimeLabel,
  },
  data() {
    return {
      isTopBarVisible: false,
      lastScrollPosition: 0,
      furnitureTitle: document.getElementById('pdp-row').getAttribute('data-furniture-title'),
      shortTitleChanged: false,
      intersectionObserverManager: null,
      productSummaryPassed: false,
      hideStickyBarAt: 101,
      productSummary: null,
    };
  },
  computed: {
    isMobile() {
      return this.$store.getters['ui/GET_IS_MOBILE'];
    },
    shortSeoTitle() {
      return this.$store.getters['ui/GET_SHORT_SEO_TITLE'];
    },
  },
  watch: {
    shortSeoTitle(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.furnitureTitle = this.shortSeoTitle;
        this.shortTitleChanged = true;
      }
    },
  },
  created() {
    if (process.browser) {
      window.addEventListener('scroll', this.onScrollHandler);
      window.addEventListener('product-summary-mounted', this.productSummaryListener);
    }
  },
  methods: {
    productSummaryListener(e) {
      this.productSummary = e.detail;
    },
    observeProductSummary() {
      if (window.IntersectionObserver && this.productSummary) {
        this.intersectionObserverManager = new IntersectionObserverManager(this.productSummary, e => {
          if (e.intersectionRect.y <= this.hideStickyBarAt) this.productSummaryPassed = true;
          else if (e.intersectionRect.y > this.hideStickyBarAt) this.productSummaryPassed = false;
        });
        this.intersectionObserverManager.observeElement();
      }
    },
    handleTopBarScroll() {
      const currentScrollPosition = document.documentElement.scrollTop;
      if (this.lastScrollPosition > this.hideStickyBarAt && this.lastScrollPosition < currentScrollPosition && !this.productSummaryPassed) {
        this.isTopBarVisible = true;
      } else if ((this.lastScrollPosition > currentScrollPosition && !this.shortTitleChanged) || this.productSummaryPassed) {
        this.isTopBarVisible = false;
      }

      this.shortTitleChanged = false;
      this.lastScrollPosition = window.scrollY;
    },
    onScrollHandler() {
      this.observeProductSummary();
      this.handleTopBarScroll();
    },
  },
  unmounted() {
    if (process.browser) {
      window.removeEventListener('scroll', this.onScrollHandler);
      window.removeEventListener('product-summary-mounted', this.productSummaryListener);
    }
  },
};
</script>

<style lang="scss">
@import '../../../../src/scss/utils/variables';
@import '../../../../src/scss/utils/colors';
@import '../../../../src/scss/utils/mixins';

.configurator-top-bar {
    width: 100%;
    background: $t-white;
    position: fixed;
    top: 0;
    height: var(--header-height);
    border-bottom: 1px solid $t-grey-500;
    z-index: 30;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(-120px);

    &.show {
      transform: translateY(var(--ribbon-height));
      padding: 8px 0;
    }


    &__column-1 {
        gap: 29px;

        @include to-desktop-min {
          > .delivery {
            display: none;
          }
        }
    }

    &__column-2 {
        gap: 21px;
    }

    &__product-title {
        font-weight: 700;
        line-height: 26px;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 345px;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;

        @include to-desktop-min {
          max-width: 130px;

          &.bold-20 {
            font-size: 12px;
          }
        }
    }

    &__action-buttons {
        gap: 8px;
        justify-content: center;
        align-items: center;

        > p {
          max-height: 50px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }

        @include to-desktop-min {
          .add-to-cart-button {
            display: none;
          }

          button.cta-button.save-for-later {
            height: 32px;
            min-width: 90px !important;
          }

          > p {
            max-height: 34px;
            -webkit-line-clamp: 2;
          }
        }

    }

    &__product-price {
      @include to-desktop-min {
        .cplus-crossed-out-price {
          justify-content: flex-end;

          .percentage--in-one-line {
            margin-top: 2px !important;
          }
        }

      }
    }
   .cta-button {
      #configurator-main-wrapper & {
        min-width: 205px;
        padding: 0 10px;
      }
   }
}

</style>
