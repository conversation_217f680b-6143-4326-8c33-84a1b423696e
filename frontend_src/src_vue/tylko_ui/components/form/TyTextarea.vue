<template>
    <ValidationProvider
        v-slot="{ errors }"
        :rules="rules"
        tag="div"
    >
        <div
            class="yoda-input-wrapper tmb-s"
            :class="{
                'yoda-input-wrapper--error': errors && errors.length,
                'js-form-error': errors && errors.length
            }"
        >
            <div class="yoda-input">
                <label
                    class="th-5-m"
                    :for="name"
                >
                    <slot name="label" />
                </label>
                <textarea
                    :id="name"
                    :name="name"
                    :value.prop="innerModel"
                    :placeholder="placeholder"
                    :style="height ? `height:${height}px; min-height:0px;` : `min-height:115px`"
                    class="js-autoresize"
                    @input="emitValue($event)"
                    @focus="emitFocus"
                    @blur="emitBlur($event)"
                />
            </div>
            <div
                v-if="errors && errors.length"
                class="yoda-error-message yoda-error-message-- tpl-s tpt-xxs th-5-m tfc-red-base"
            >
                {{ errors[0] }}
            </div>
            <div
                v-if="$slots.subtext"
                class="yoda-input-subtext tpl-s tpt-xxs th-5-m tfc-grey-1150"
            >
                <slot name="subtext" />
            </div>
        </div>
    </ValidationProvider>
</template>
<script>
    export default {
        props: {
            name: {
                type: String,
                required: true,
            },
            placeholder: {
                type: String,
                default: '',
            },
            value: {
                type: [String, Number],
                default: '',
            },
            rules: { // vee-validate rules: https://logaretm.github.io/vee-validate/api/rules.html
                type: [String, Object],
                default: '',
            },
            height: {
                type: [String, Number],
                default: '',
            },
        },

        data() {
            return {
                innerModel: null,
            };
        },

        created() {
            this.innerModel = this.value;
        },

        mounted() {
            this.setResizeListeners(this.$el, '.js-autoresize');
        },

        methods: {
            emitValue(event) {
                this.innerModel = event.target.value;
                this.$emit('input', this.innerModel);
            },
            emitFocus() {
                this.$emit('focus');
            },
            emitBlur(event) {
                this.$emit('blur', event.target.value);
            },
            setResizeListeners($el, query) {
                const targets = $el.querySelectorAll(query);
                const textareaHeight = this.height;
                targets.forEach(target => {
                    target.style.height = `${target.scrollHeight}px` // eslint-disable-line
                    target.addEventListener('input', function () { // eslint-disable-line
                        if (this.scrollHeight > textareaHeight) {
                            this.style.height = 'auto';
                            this.style.height = `${this.scrollHeight}px`;
                        }
                    });
                });
            },
        },
    };
</script>
