<template>
    <li class="instruction-item flex between-xs mb-16">
        <p class="normal-14 text-offblack-600">
            {{ $t('mail24_shelf') }} {{ dimensions }}
        </p>
        <a
            :href="url"
            target="_blank"
            rel="noreferrer noopener"
        >
            <img
                svg-inline
                src="@tylko_ui/icons-dtf/ic_instruction.svg"
            >
        </a>
    </li>
</template>

<script>
    export default {
        props: {
            url: {
                type: String,
                default: () => '',
            },
            dimensions: {
                type: String,
                default: () => '',
            },
        },
    };
</script>
<style lang="scss" scoped>
    .instruction-item {
        max-width: 376px;
    }
</style>
