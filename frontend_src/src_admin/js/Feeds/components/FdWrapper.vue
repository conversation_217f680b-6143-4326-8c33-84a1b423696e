<template>
  <div>
    <div class="container-fluid ft-container-main">
        <FdHeader v-bind:categoryName="categoryName"/>
        <FdFilter v-bind:filters="filters" v-bind:gridMode="gridMode" />
        <FdSortBy v-bind:count="count" v-bind:viewMode="viewMode" >
          <FdPagination
            v-bind:next="next"
            v-bind:maxPage="maxPage"
            v-bind:currentPage="currentPage" />
        </FdSortBy>
    </div>

    <FdAddToCategoryList v-if="viewMode === 'list'"
      v-bind:addToCategoryList="addToCategoryList" />
    <FdAddToCategoryGrid v-if="viewMode === 'grid'"
      v-bind:addToCategoryGrid="addToCategoryList" />
    <FdAddToCategoryImage v-if="viewMode === 'image'"
      v-bind:addToCategoryGrid="addToCategoryList" />

    <FdEmptyList v-if="count == 0"/>

    <div v-if="count !== 0" class="container-fluid ft-container-main">
      <FdPagination
        v-bind:next="next"
        v-bind:maxPage="maxPage"
        v-bind:currentPage="currentPage" />
    </div>
  </div>

</template>

<script>
import FdHeader from "./FdHeader";
import FdFilter from "./FdFilter";
import FdSortBy from "./FdSortBy";
import FdAddToCategoryList from "./FdAddToCategoryList";
import FdAddToCategoryGrid from "./FdAddToCategoryGrid";
import FdAddToCategoryImage from "./FdAddToCategoryImage";
import FdEmptyList from "./FdEmptyList";
import FdPagination from "./FdPagination";

import { EventBus } from "../utils/EventBus";
import scrollToTop from "../utils/ScrollToTop";
import axios from "axios";
import cloneDeep from "clone-deep";

export default {
  components: {
    FdHeader,
    FdFilter,
    FdSortBy,
    FdAddToCategoryList,
    FdAddToCategoryGrid,
    FdAddToCategoryImage,
    FdPagination,
    FdEmptyList
  },
  props: ["gridMode"],

  data: function() {
    return {
      addToCategoryList: [],
      previous: null,
      next: null,
      count: 0,
      currentPage: 1,
      pageSize: 50,
      viewMode: "list",
      categoryId: window.location.pathname.split("/")[3],
      categoryName: window.category,

      filters: {
        minWidth: 70,
        maxWidth: 450,
        minHeight: 24,
        maxHeight: 401,
        color: "all",
        features: {
          drawers: false,
          doors: false,
          extended: false,
          custom: false,
          open: false,
        },
        dna: {
          slant: false,
          pattern: false,
          gradient: false,
          grid: false
        },
        depth: {
          cm32: false,
          cm40: false,
        },
        minPrice: 0,
        maxPrice: 100000,
        minRating: 0,
        maxRating: 5,
        minRating1: 0,
        maxRating1: 99999999,
        minRating2: 0,
        maxRating2: 99999999,
        minRating3: 0,
        maxRating3: 99999999,
        minRating4: 0,
        maxRating4: 99999999,
        minRating5: 0,
        maxRating5: 99999999,
        minSortBy: 0,
        maxSortBy: 99999,
        minScoring: 0,
        maxScoring: 99999,
        minSizeS: 0,
        maxSizeS: 100,
        minSizeM: 0,
        maxSizeM: 100,
        minSizeL: 0,
        maxSizeL: 100,
        info: '',
        section: '',
        categories: {},
        boardCategories: [
              {
                  'name': 'All shelves',
                  'id': 'cat_all',
                  'isSelected': false
              },
              {
                  'name': 'bookcase',
                  'id': 'cat_1',
                  'isSelected': false
              },
              {
                  'name': 'sideboard',
                  'id': 'cat_3',
                  'isSelected': false
              },
              {
                  'name': 'tv-stand',
                  'id': 'cat_4',
                  'isSelected': false
              },
              {
                  'name': 'shoerack',
                  'id': 'cat_2',
                  'isSelected': false
              },
              {
                  'name': 'wallstorage',
                  'id': 'cat_5',
                  'isSelected': false
              },
              {
                  'name': 'chest-of-drawers',
                  'id': 'cat_6',
                  'isSelected': false
              },
              {
                  'name': 'vinyl_storage',
                  'id': 'cat_7',
                  'isSelected': false
              },
          ]
      },
      defaultFilters: {},
      ordering: "id",
    };
  },

  computed: {
    maxPage() {
      return Math.ceil(this.count / this.pageSize);
    },
    getBaseUrl() {
      return this.gridMode ? "/api/v1/grid/" : "/api/v1/product_feeds/jetty_rated/";
    }
  },

  created() {
    this.defaultFilters = cloneDeep(this.filters); //Object.assign({}, this.filters);
    this.getData();
    this.getCategories();
    EventBus.$on("FilterInputChanged", this.handleFilterInputChanged);
    EventBus.$on("FilterCheckboxChanged", this.handleFilterCheckboxChanged);
    EventBus.$on("RadioValueChanged", this.handleRadioValueChanged);
    EventBus.$on(
      "FilterCheckboxChangedCat",
      this.handleFilterCheckboxChangedCat
    );
    EventBus.$on("SelectShelf", this.handleSelectShelf);
    EventBus.$on("FilterList", this.handleFilterList);
    EventBus.$on("RemoveFilters", this.handleRemoveFilters);
    EventBus.$on("OrderList", this.handleOrderList);
    EventBus.$on("ChangeViewMode", this.handleChangeViewMode);
    EventBus.$on("AddToFeedCategory", this.handleAddToFeedCategory);
    EventBus.$on("NextPagePagination", this.handleNextPagePagination);
    EventBus.$on("PrevPagePagination", this.handlePrevPagePagination);

    EventBus.$on("ChangeHeightSlider", this.handleChangeHeightSlider);
    EventBus.$on("ChangeWidthSlider", this.handleChangeWidthtSlider);
  },

  methods: {
    getData() {
      this.url = this.getBaseUrl;
      let params = this.getPramsFromFilter();
      this.url = this.url + params;
      axios
        .get(this.url)
        .then(res => {
          this.addToCategoryList = [];
          this.count = 0;
          setTimeout(() => {
            this.addToCategoryList = res.data.results;
            this.addToCategoryList.map(x => {
              x.isSelected = false;
            });
            this.count = res.data.count;
            this.next = res.data.next;
            this.previous = res.data.previous;
            scrollToTop();
          }, 50);
        })
        .catch(err => {
          alert("something went wrong!");
          console.log(err);
        });
    },
    getCategories() {
      let url = "/api/v1/rating_tool/board_category";
      axios.get(url).then(res => {
        res.data.forEach(item => (item.isSelected = false));
        this.filters.categories = res.data;
      });
    },
    getPramsFromFilter() {
      let param = "?";
      param += "min_height=" + this.filters.minHeight;
      param += "&max_height=" + this.filters.maxHeight;
      param += "&min_width=" + this.filters.minWidth;
      param += "&max_width=" + this.filters.maxWidth;
      param += "&min_price=" + this.filters.minPrice;
      param += "&max_price=" + this.filters.maxPrice;
      param += "&min_rating=" + this.filters.minRating;
      param += "&max_rating=" + this.filters.maxRating;
      param += "&min_rating1=" + this.filters.minRating1;
      param += "&max_rating1=" + this.filters.maxRating1;
      param += "&min_rating2=" + this.filters.minRating2;
      param += "&max_rating2=" + this.filters.maxRating2;
      param += "&min_rating3=" + this.filters.minRating3;
      param += "&max_rating3=" + this.filters.maxRating3;
      param += "&min_rating4=" + this.filters.minRating4;
      param += "&max_rating4=" + this.filters.maxRating4;
      param += "&min_rating5=" + this.filters.minRating5;
      param += "&max_rating5=" + this.filters.maxRating5;

      param += "&min_sort_by=" + this.filters.minSortBy;
      param += "&max_sort_by=" + this.filters.maxSortBy;
      param += "&min_scoring=" + this.filters.minScoring;
      param += "&max_scoring=" + this.filters.maxScoring;

      param += "&min_size_s=" + this.filters.minSizeS;
      param += "&max_size_s=" + this.filters.maxSizeS;
      param += "&min_size_m=" + this.filters.minSizeM;
      param += "&max_size_m=" + this.filters.maxSizeM;
      param += "&min_size_l=" + this.filters.minSizeL;
      param += "&max_size_l=" + this.filters.maxSizeL;

      if (this.filters.info) param += "&info=" + this.filters.info;
      if (this.filters.section) param += "&section=" + this.filters.section;

      if (this.filters.features.drawers) param += "&features=drawers";
      if (this.filters.features.doors) param += "&features=doors";
      if (this.filters.features.extended) param += "&features=extended";
      if (this.filters.features.custom) param += "&features=custom";
      if (this.filters.features.custom) param += "&features=open";

      if (this.filters.dna.slant) param += "&pattern=slant";
      if (this.filters.dna.pattern) param += "&pattern=pattern";
      if (this.filters.dna.gradient) param += "&pattern=gradient";
      if (this.filters.dna.grid) param += "&pattern=grid";

      if (this.filters.dna.cm32) param += "&depth=32";
      if (this.filters.dna.cm40) param += "&depth=40";

      if (this.filters.categories.length > 0) {
        this.filters.categories.forEach(item => {
          if (item.isSelected) {
            param += "&category=" + item.id;
          }
        });
      }

      if (this.filters.boardCategories.length > 0) {
        this.filters.boardCategories.forEach(item => {
          if (item.isSelected) {
            param += "&board_category=" + item.id;
          }
        });
      }

      if (this.filters.color !== "all") {
        param += "&color=" + this.filters.color;
      }

      param += "&page=" + this.currentPage;
      param += "&page_size=" + this.pageSize;
      param += "&ordering=" + this.ordering;
      param += "&feed_category=" + this.categoryId;

      return param;
    },
    handleFilterInputChanged(payload) {
      this.filters[payload.key] = payload.value;
    },
    handleFilterCheckboxChanged(payload) {
      this.filters[payload.filterKey][payload._key] = payload._value;
    },
    handleFilterCheckboxChangedCat(payload) {
      this.filters[payload.filterKey][payload._key].isSelected = payload._value;
    },
    handleSelectShelf(payload) {
      this.addToCategoryList.forEach(item => {
        if (item.shelfId == payload.shelfId) {
          item.isSelected = payload.value;
        }
      });
    },
    handleRadioValueChanged(payload) {
      this.filters.color = payload.color;
    },
    handleFilterList(payload) {
      this.currentPage = 1;
      this.getData();
    },
    handleRemoveFilters() {
      this.filters = cloneDeep(this.defaultFilters); //Object.assign({}, this.defaultFilters);
      this.getCategories();
      this.getData();
    },
    handleOrderList(payload) {
      this.ordering = payload.orderingValue;
      this.getData();
    },
    handleChangeViewMode(payload) {
      this.viewMode = payload.viewMode;
    },
    handleAddToFeedCategory(payload) {
      let data = [];
      this.addToCategoryList.forEach((item, index) => {
        if (item.isSelected == true) {
          data.push({
            category: this.categoryId,
            furniture_in_category: item.shelfId
          });
        }
      });
      if (data.length > 0) {
        this.postToFeedCategory(data);
      } else {
        alert("You didn't select any shelfs");
      }
    },
    postToFeedCategory(data) {
      axios.post("/api/v1/product_feeds/feed_item/", data).then(res => {
        data.forEach((value, index) => {
          this.addToCategoryList.splice(index, 1);
        });
        this.getData();
      });
    },
    handleNextPagePagination() {
      if (this.currentPage < this.maxPage) {
        this.currentPage += 1;
        this.getData();
      }
    },
    handlePrevPagePagination() {
      if (this.currentPage > 1) {
        this.currentPage -= 1;
        this.getData();
      }
    },
    handleChangeHeightSlider(payload) {
      this.filters.minHeight = payload.newValue[0];
      this.filters.maxHeight = payload.newValue[1];
    },
    handleChangeWidthtSlider(payload) {
      this.filters.minWidth = payload.newValue[0];
      this.filters.maxWidth = payload.newValue[1];
    }
  }
};
</script>
<style lang="scss">
@import "../scss/variables";
</style>
