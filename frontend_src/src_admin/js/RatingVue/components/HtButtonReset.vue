<template>
    <div>
        <button
                class="ht-button-secondary ht-button-reset color-gray"
                v-on:click="handleResetClick">Reset <i class="arrow-go"></i>
        </button>
    </div>
</template>

<script>
    import {EventBus} from '../utils/EventBus'

    export default {
        props: [],
        methods: {
            handleResetClick() {
                EventBus.$emit('reset');
            }
        }
    }
</script>
